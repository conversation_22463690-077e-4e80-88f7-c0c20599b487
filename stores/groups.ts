import { create } from 'zustand';
import { groupService, GroupResponse, GroupRequest, GroupWithMembersResponse } from '@/services/group';
import { ContactResponse } from '@/services/contact';

interface GroupWithContacts extends GroupResponse {
  members: ContactResponse[];
}

interface GroupsState {
  groups: GroupResponse[];
  selectedGroup: GroupWithContacts | null;
  isLoading: boolean;
  error: string | null;
  fetchGroups: () => Promise<void>;
  getGroupById: (id: number) => Promise<void>;
  createGroup: (name: string, contactIds: number[]) => Promise<boolean>;
  updateGroup: (groupId: number, name: string, memberIds: number[]) => Promise<boolean>;
  updateGroupMembers: (groupId: number, memberIds: number[]) => Promise<boolean>;
  deleteGroup: (groupId: number) => Promise<boolean>;
  reorderGroups: (reorderedGroups: GroupResponse[]) => Promise<boolean>;
  clearError: () => void;
}

export const useGroupsStore = create<GroupsState>((set, get) => ({
  groups: [],
  selectedGroup: null,
  isLoading: false,
  error: null,

  fetchGroups: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await groupService.getAllGroups();

      if (response.error) {
        set({ error: response.error, isLoading: false });
        return;
      }

      set({ groups: response.data || [], isLoading: false });
    } catch (error) {
      console.error('Error fetching groups:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch groups',
        isLoading: false
      });
    }
  },

  getGroupById: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      // Validate the group ID
      if (!id) {
        set({ error: 'Invalid group ID', isLoading: false });
        return;
      }

      // Use the getGroupMembers method to get both group details and members
      const response = await groupService.getGroupMembers(id);

      if (response.error) {
        // Handle specific error cases
        if (response.status === 404) {
          set({ error: `Group with ID ${id} not found`, isLoading: false });
        } else {
          set({ error: response.error, isLoading: false });
        }
        return;
      }

      // Set the selected group with its members
      set({
        selectedGroup: {
          ...response.data,
          members: response.data?.members || []
        },
        isLoading: false
      });
    } catch (error) {
      console.error(`Error fetching group ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to fetch group ${id}`,
        isLoading: false
      });
    }
  },

  createGroup: async (name: string, contactIds: number[]) => {
    set({ isLoading: true, error: null });
    try {
      const groupData: GroupRequest = {
        name,
        contacts: contactIds
      };

      const response = await groupService.createGroup(groupData);

      if (response.error) {
        set({ error: response.error, isLoading: false });
        return false;
      }

      // Refresh the groups list
      await get().fetchGroups();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error('Error creating group:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create group',
        isLoading: false
      });
      return false;
    }
  },

  updateGroup: async (groupId: number, name: string, memberIds: number[]) => {
    set({ isLoading: true, error: null });
    try {
      // Validate the group ID
      if (!groupId) {
        set({ error: 'Invalid group ID', isLoading: false });
        return false;
      }

      // Validate the group name
      if (!name.trim()) {
        set({ error: 'Group name is required', isLoading: false });
        return false;
      }

      const response = await groupService.updateGroup(groupId, {
        name: name.trim(),
        members: memberIds
      });

      if (response.error) {
        // Handle specific error cases
        if (response.status === 404) {
          set({ error: `Group with ID ${groupId} not found`, isLoading: false });
        } else {
          set({ error: response.error, isLoading: false });
        }
        return false;
      }

      // Refresh the selected group to show updated data
      await get().getGroupById(groupId);

      // Also refresh the groups list
      await get().fetchGroups();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error(`Error updating group ${groupId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update group ${groupId}`,
        isLoading: false
      });
      return false;
    }
  },

  updateGroupMembers: async (groupId: number, memberIds: number[]) => {
    set({ isLoading: true, error: null });
    try {
      // Validate the group ID
      if (!groupId) {
        set({ error: 'Invalid group ID', isLoading: false });
        return false;
      }

      const response = await groupService.updateGroupMembers(groupId, memberIds);

      if (response.error) {
        // Handle specific error cases
        if (response.status === 404) {
          set({ error: `Group with ID ${groupId} not found`, isLoading: false });
        } else {
          set({ error: response.error, isLoading: false });
        }
        return false;
      }

      // Refresh the selected group to show updated members
      await get().getGroupById(groupId);

      // Also refresh the groups list
      await get().fetchGroups();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error(`Error updating group ${groupId} members:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update group ${groupId} members`,
        isLoading: false
      });
      return false;
    }
  },

  deleteGroup: async (groupId: number) => {
    set({ isLoading: true, error: null });
    try {
      // Validate the group ID
      if (!groupId) {
        set({ error: 'Invalid group ID', isLoading: false });
        return false;
      }

      const response = await groupService.deleteGroup(groupId);

      if (response.error) {
        // Handle specific error cases
        if (response.status === 404) {
          set({ error: `Group with ID ${groupId} not found`, isLoading: false });
        } else {
          set({ error: response.error, isLoading: false });
        }
        return false;
      }

      // Refresh the groups list after successful deletion
      await get().fetchGroups();

      // Clear the selected group if it was the one deleted
      const currentSelectedGroup = get().selectedGroup;
      if (currentSelectedGroup && currentSelectedGroup.id === groupId) {
        set({ selectedGroup: null });
      }

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error(`Error deleting group ${groupId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete group ${groupId}`,
        isLoading: false
      });
      return false;
    }
  },

  reorderGroups: async (reorderedGroups: GroupResponse[]) => {
    set({ isLoading: true, error: null });
    try {
      // Create the order update payload
      const groupOrders = reorderedGroups.map((group, index) => ({
        id: group.id,
        sort_order: index + 1
      }));

      const response = await groupService.updateGroupOrder(groupOrders);

      if (response.error) {
        set({ error: response.error, isLoading: false });
        return false;
      }

      // Update the local state with the new order
      set({ groups: reorderedGroups, isLoading: false });
      return true;
    } catch (error) {
      console.error('Error reordering groups:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to reorder groups',
        isLoading: false
      });
      return false;
    }
  },

  clearError: () => set({ error: null }),
}));
