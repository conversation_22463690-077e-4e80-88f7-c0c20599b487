import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Contacts from 'expo-contacts';
import { contactService, ContactRequest } from '@/services/contact';

export interface PhoneContact {
  id: string;
  name: string;
  phoneNumbers?: Array<{
    id: string;
    label: string;
    number: string;
  }>;
  emails?: Array<{
    id: string;
    label: string;
    email: string;
  }>;
  image?: string;
  isPriority: boolean;
  added?: boolean;
  // Flag to indicate if this contact has an email that matches an existing contact
  hasMatchingEmail?: boolean;
}

interface ContactsState {
  deviceContacts: PhoneContact[];
  isLoading: boolean;
  error: string | null;
  hasPermission: boolean;
  fetchContacts: () => Promise<void>;
  checkPermission: () => Promise<void>;
  setDeviceContacts: (contacts: PhoneContact[]) => void;
  togglePriority: (contactId: string) => void;
  toggleAdded: (contactId: string) => void;
  addContactToDatabase: (contactId: string) => Promise<void>;
  clearError: () => void;
  setHasPermission: (hasPermission: boolean) => void;
}

export const useContactsStore = create(
  persist<ContactsState>(
    (set, get) => ({
      deviceContacts: [],
      isLoading: false,
      error: null,
      hasPermission: false,

      fetchContacts: async () => {
        set({ isLoading: true, error: null });
        try {
          // Check current permission status first
          let { status } = await Contacts.getPermissionsAsync();

          // If permission is not granted, request it
          if (status !== 'granted') {
            const permissionResult = await Contacts.requestPermissionsAsync();
            status = permissionResult.status;
          }

          if (status === 'granted') {
            set({ hasPermission: true });

            // Fetch contacts from the device
            const { data } = await Contacts.getContactsAsync({
              fields: [
                Contacts.Fields.ID,
                Contacts.Fields.Name,
                Contacts.Fields.FirstName,
                Contacts.Fields.LastName,
                Contacts.Fields.PhoneNumbers,
                Contacts.Fields.Emails,
                Contacts.Fields.Image,
              ],
              sort: Contacts.SortTypes.FirstName,
            });

            if (data.length > 0) {
              // Transform contacts to our format
              const transformedContacts: PhoneContact[] = data.map(contact => contact.id ? ({
                id: contact.id,
                name: contact.name || `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unknown',
                phoneNumbers: contact.phoneNumbers?.map(phone => ({
                  id: phone.id || '',
                  label: phone.label || '',
                  number: phone.number || '',
                })),
                emails: contact.emails?.map(email => ({
                  id: email.id || '',
                  label: email.label || '',
                  email: email.email || '',
                })),
                image: contact.image?.uri,
                isPriority: false,
                added: false,
              }) : null).filter(Boolean) as PhoneContact[];

              set({ deviceContacts: transformedContacts, isLoading: false });
            } else {
              set({ error: 'No contacts found', isLoading: false });
            }
          } else {
            set({
              hasPermission: false,
              error: 'Permission to access contacts was denied',
              isLoading: false
            });
          }
        } catch (error) {
          console.error('Error fetching contacts:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch contacts',
            isLoading: false
          });
        }
      },

      checkPermission: async () => {
        try {
          const { status } = await Contacts.getPermissionsAsync();
          set({ hasPermission: status === 'granted' });
        } catch (error) {
          console.error('Error checking contacts permission:', error);
          set({ hasPermission: false });
        }
      },

      setDeviceContacts: (contacts) => set({ deviceContacts: contacts }),

      togglePriority: (contactId) => set(state => ({
        deviceContacts: state.deviceContacts.map(contact =>
          contact.id === contactId
            ? { ...contact, isPriority: !contact.isPriority }
            : contact
        )
      })),

      toggleAdded: (contactId) => set(state => ({
        deviceContacts: state.deviceContacts.map(contact =>
          contact.id === contactId
            ? { ...contact, added: !contact.added }
            : contact
        )
      })),

      addContactToDatabase: async (contactId) => {
        try {
          set({ isLoading: true, error: null });

          // Find the contact in the device contacts
          const contact = get().deviceContacts.find(c => c.id === contactId);

          if (!contact) {
            set({ error: 'Contact not found', isLoading: false });
            return;
          }

          // Parse the name into first and last name
          let firstName = '';
          let lastName = '';

          if (contact.name) {
            const nameParts = contact.name.split(' ');
            firstName = nameParts[0] || '';
            lastName = nameParts.slice(1).join(' ') || '';
          }

          // Prepare contact data for API
          const contactData: ContactRequest = {
            first_name: firstName || 'Unknown',
            last_name: lastName || undefined,
            email: contact.emails && contact.emails.length > 0 ? contact.emails[0].email : undefined,
            phone_number: contact.phoneNumbers && contact.phoneNumbers.length > 0 ? contact.phoneNumbers[0].number : undefined,
            is_priority: contact.isPriority || false
          };

          // Create contact in database
          try {
            const response = await contactService.createContact(contactData);

            if (!response || (response as any).error) {
              const errorMessage = (response as any)?.error || 'Failed to create contact';
              console.error('Contact creation error:', errorMessage);
              set({ error: errorMessage, isLoading: false });
              return;
            }

            // Mark contact as added in the local state
            set(state => ({
              deviceContacts: state.deviceContacts.map(c =>
                c.id === contactId
                  ? { ...c, added: true }
                  : c
              ),
              isLoading: false
            }));

            console.log('Contact added to database:', response.data);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create contact';
            console.error('Contact creation error:', errorMessage);
            set({ error: errorMessage, isLoading: false });
            return;
          }
        } catch (error) {
          console.error('Error adding contact to database:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to add contact to database',
            isLoading: false
          });
        }
      },

      clearError: () => set({ error: null }),

      setHasPermission: (hasPermission) => set({ hasPermission }),
    }),
    {
      name: 'contacts-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
