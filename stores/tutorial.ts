import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface TutorialState {
  hasSeenTutorial: boolean;
  markTutorialAsSeen: () => void;
  resetTutorialState: () => void;
}

export const useTutorialStore = create(
  persist<TutorialState>(
    (set) => ({
      hasSeenTutorial: false,
      markTutorialAsSeen: () => set({ hasSeenTutorial: true }),
      resetTutorialState: () => set({ hasSeenTutorial: false }),
    }),
    {
      name: 'tutorial-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
