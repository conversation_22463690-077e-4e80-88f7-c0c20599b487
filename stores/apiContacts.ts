import { create } from 'zustand';
import { contactService, ContactResponse } from '@/services/contact';

interface ContactsState {
  contacts: ContactResponse[];
  isLoading: boolean;
  error: string | null;
  fetchContacts: () => Promise<void>;
  togglePriority: (contactId: number) => Promise<void>;
  clearError: () => void;
}

export const useApiContactsStore = create<ContactsState>((set, get) => ({
  contacts: [],
  isLoading: false,
  error: null,

  fetchContacts: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await contactService.getAllContacts();
      
      if (response.error) {
        set({ error: response.error, isLoading: false });
        return;
      }
      
      set({ contacts: response.data || [], isLoading: false });
    } catch (error) {
      console.error('Error fetching contacts:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch contacts', 
        isLoading: false 
      });
    }
  },

  togglePriority: async (contactId: number) => {
    try {
      const contacts = get().contacts;
      const contact = contacts.find(c => c.id === contactId);
      
      if (!contact) {
        return;
      }
      
      // Optimistically update the UI
      set({
        contacts: contacts.map(c => 
          c.id === contactId 
            ? { ...c, is_priority: !c.is_priority } 
            : c
        )
      });
      
      // Update in the database
      await contactService.updateContact(contactId, {
        is_priority: !contact.is_priority
      });
      
    } catch (error) {
      console.error('Error toggling priority:', error);
      
      // Revert the optimistic update if there was an error
      await get().fetchContacts();
      
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update contact priority'
      });
    }
  },

  clearError: () => set({ error: null }),
}));
