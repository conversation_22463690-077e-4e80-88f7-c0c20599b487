import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { mixpanelService } from '@/services/mixpanel';

export type AuthProvider = 'google' | 'facebook' | 'email';
export type AccessLevel = 'admin' | 'user' | 'editor' | 'viewer';

interface UserData {
  id?: number;
  displayName?: string;
  email?: string;
  accessLevel?: AccessLevel;
  token?: string;
  refreshToken?: string;
  profileImageUrl?: string;
  emailVerified?: boolean;
}

interface AuthState {
  isLoggedIn: boolean;
  userData: UserData | null;
  login: (userData?: UserData) => void;
  logout: () => void;
  updateUserData: (data: Partial<UserData>) => void;
}

export const useAuthStore = create(
  persist<AuthState>(
    (set) => ({
      isLoggedIn: false,
      userData: null,
      login: (userData = {}) => set({ isLoggedIn: true, userData }),
      logout: () => {
        // Track logout and reset Mixpanel data
        mixpanelService.trackLogOut().catch(error => {
          console.error('Error tracking logout:', error);
        });
        mixpanelService.reset().catch(error => {
          console.error('Error resetting Mixpanel data:', error);
        });
        set({ isLoggedIn: false, userData: null });
      },
      updateUserData: (data) => set((state) => ({
        userData: state.userData ? { ...state.userData, ...data } : data
      })),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);