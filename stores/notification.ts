import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { notificationService, NotificationViewModel } from '@/services/notification';
import { useAuthStore } from './auth';

// Helper function to check if two notification arrays are different
const areNotificationsDifferent = (
  currentNotifications: NotificationViewModel[],
  newNotifications: NotificationViewModel[]
): boolean => {
  // Quick check: different lengths means they're different
  if (currentNotifications.length !== newNotifications.length) {
    return true;
  }

  // Create maps for faster lookup
  const currentMap = new Map<number, NotificationViewModel>();
  currentNotifications.forEach(notification => {
    currentMap.set(notification.id, notification);
  });

  // Check if any notification in the new array is different from the current array
  for (const newNotification of newNotifications) {
    const currentNotification = currentMap.get(newNotification.id);

    // If notification doesn't exist in current array, they're different
    if (!currentNotification) {
      return true;
    }

    // Check relevant properties that would cause a UI update if changed
    if (
      currentNotification.read !== newNotification.read ||
      currentNotification.text !== newNotification.text ||
      currentNotification.isAccepted !== newNotification.isAccepted ||
      // Check if actions have changed (added, removed, or modified)
      JSON.stringify(currentNotification.actions) !== JSON.stringify(newNotification.actions)
    ) {
      return true;
    }
  }

  // If we got here, the notifications are the same
  return false;
};

interface NotificationState {
  notifications: NotificationViewModel[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;

  // Actions
  fetchNotifications: () => Promise<void>;
  fetchNotificationsSilently: () => Promise<void>; // New function for background polling
  markAllAsRead: () => Promise<void>;
  markAsRead: (notificationId: number) => Promise<void>;
  setNotifications: (notifications: NotificationViewModel[]) => void;
  updateNotificationAfterAction: (notificationId: number, isAccepted?: boolean) => void;
  updateUnreadCount: () => void;
}

export const useNotificationStore = create(
  persist<NotificationState>(
    (set, get) => ({
      notifications: [],
      unreadCount: 0,
      isLoading: false,
      error: null,
      lastFetched: null,

      fetchNotifications: async () => {
        const userData = useAuthStore.getState().userData;
        if (!userData?.id) {
          set({ error: 'User not logged in', isLoading: false });
          return;
        }

        set({ isLoading: true, error: null });
        try {
          const response = await notificationService.getUserNotifications();

          if (response.error) {
            set({ error: response.error, isLoading: false });
            return;
          }

          // Get current notifications from the store
          const currentNotifications = get().notifications;
          const newNotifications = response.data;

          // Check if notifications have actually changed
          const hasChanged = areNotificationsDifferent(currentNotifications, newNotifications);

          if (hasChanged) {
            console.log('Notifications have changed, updating store (with loading indicator)');
            set({
              notifications: newNotifications,
              isLoading: false,
              lastFetched: Date.now()
            });

            // Update unread count
            get().updateUnreadCount();
          } else {
            // Just update the loading state and lastFetched timestamp
            set({
              isLoading: false,
              lastFetched: Date.now()
            });
          }
        } catch (error) {
          console.error('Error fetching notifications:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch notifications',
            isLoading: false
          });
        }
      },

      // Fetch notifications without showing loading indicator (for background polling)
      fetchNotificationsSilently: async () => {
        const userData = useAuthStore.getState().userData;
        if (!userData?.id) {
          return;
        }

        try {
          const response = await notificationService.getUserNotifications();

          if (response.error) {
            console.error('Silent notification fetch error:', response.error);
            return;
          }

          // Get current notifications from the store
          const currentNotifications = get().notifications;
          const newNotifications = response.data;

          // Check if notifications have actually changed
          const hasChanged = areNotificationsDifferent(currentNotifications, newNotifications);

          if (hasChanged) {
            console.log('Notifications have changed, updating store');
            set({
              notifications: newNotifications,
              lastFetched: Date.now()
            });

            // Update unread count
            get().updateUnreadCount();
          } else {
            // Just update the lastFetched timestamp
            set({
              lastFetched: Date.now()
            });
          }
        } catch (error) {
          console.error('Error fetching notifications silently:', error);
        }
      },

      markAllAsRead: async () => {
        const { notifications } = get();
        const unreadNotifications = notifications.filter(n => !n.read);

        if (unreadNotifications.length === 0) return;

        try {
          // Mark each unread notification as read
          const markPromises = unreadNotifications.map(notification =>
            notificationService.markAsRead(notification.id)
          );

          await Promise.all(markPromises);

          // Update local state
          set(state => ({
            notifications: state.notifications.map(n => ({ ...n, read: true })),
            unreadCount: 0
          }));
        } catch (error) {
          console.error('Error marking all notifications as read:', error);
        }
      },

      markAsRead: async (notificationId: number) => {
        try {
          const response = await notificationService.markAsRead(notificationId);

          if (response.success) {
            // Update the local state
            set(state => {
              const updatedNotifications = state.notifications.map(notification =>
                notification.id === notificationId
                  ? { ...notification, read: true }
                  : notification
              );

              return {
                notifications: updatedNotifications,
                unreadCount: updatedNotifications.filter(n => !n.read).length
              };
            });
          }
        } catch (error) {
          console.error('Error marking notification as read:', error);
        }
      },

      setNotifications: (notifications: NotificationViewModel[]) => {
        set({ notifications });
        get().updateUnreadCount();
      },

      updateNotificationAfterAction: (notificationId: number, isAccepted: boolean = false) => {
        console.log(`Store: Updating notification ${notificationId} after action, isAccepted: ${isAccepted}`);
        set(state => {
          const updatedNotifications = state.notifications.map(notification => {
            if (notification.id === notificationId) {
              // Create a copy of the notification without the actions
              // For connection requests, set isAccepted based on the parameter
              const updated = {
                ...notification,
                actions: undefined, // Remove the action buttons
                isAccepted: notification.type === 'connection' ? isAccepted : undefined
              };
              console.log(`Store: Updated notification ${notificationId}:`, updated);
              return updated;
            }
            return notification;
          });

          console.log(`Store: Setting updated notifications`);
          return { notifications: updatedNotifications };
        });
      },

      updateUnreadCount: () => {
        const { notifications } = get();
        const unreadCount = notifications.filter(n => !n.read).length;
        set({ unreadCount });
      }
    }),
    { // @ts-ignore - partialize is correctly returning a subset of the state
      name: 'notification-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => (({
        unreadCount: state.unreadCount,
        lastFetched: state.lastFetched
      }) as unknown as NotificationState),
    }
  )
);
