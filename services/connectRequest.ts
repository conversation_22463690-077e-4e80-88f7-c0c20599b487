import { api } from './api';
import { useAuthStore } from '@/stores/auth';

// ConnectRequest interfaces
export interface ConnectRequestRequest {
  email?: string;
  first_name: string;
  last_name?: string;
  phone_number?: string;
}

export interface ConnectRequestResponse {
  id: number;
  owner_id: number;
  email?: string;
  first_name: string;
  last_name?: string;
  phone_number?: string;
  is_accepted: boolean;
  is_declined: boolean;
  created_at: string;
  updated_at: string;
  contact?: any; // Optional - no longer created when sending the request
}

// Helper function to add auth headers
const withAuth = (): Record<string, string> => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// ConnectRequest service
export const connectRequestService = {
  // Get all connect requests
  getAllConnectRequests: async () => {
    try {
      return await api.get<ConnectRequestResponse[]>('/connect-requests', undefined, withAuth());
    } catch (error) {
      console.error('Get all connect requests service error:', error);
      throw error;
    }
  },

  // Get connect request by ID
  getConnectRequestById: async (id: number) => {
    try {
      return await api.get<ConnectRequestResponse>(`/connect-requests/${id}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get connect request by ID ${id} service error:`, error);
      throw error;
    }
  },

  // Create a new connect request
  createConnectRequest: async (connectRequestData: ConnectRequestRequest) => {
    try {
      return await api.post<ConnectRequestResponse>('/connect-requests', connectRequestData, withAuth());
    } catch (error) {
      console.error('Create connect request service error:', error);
      throw error;
    }
  },

  // Accept a connect request
  acceptConnectRequest: async (id: number) => {
    try {
      return await api.post<{ message: string, connectRequest: ConnectRequestResponse, recipientContact: any, senderContact: any }>(
        `/connect-requests/${id}/accept`,
        {},
        withAuth()
      );
    } catch (error) {
      console.error(`Accept connect request ${id} service error:`, error);
      throw error;
    }
  },

  // Decline a connect request
  declineConnectRequest: async (id: number) => {
    try {
      return await api.post<{ message: string, connectRequest: ConnectRequestResponse }>(
        `/connect-requests/${id}/decline`,
        {},
        withAuth()
      );
    } catch (error) {
      console.error(`Decline connect request ${id} service error:`, error);
      throw error;
    }
  },

  // Delete a connect request
  deleteConnectRequest: async (id: number) => {
    try {
      return await api.delete<void>(`/connect-requests/${id}`, withAuth());
    } catch (error) {
      console.error(`Delete connect request ${id} service error:`, error);
      throw error;
    }
  }
};
