// import * as Google from 'expo-auth-session/providers/google';
// import { useAuthRequest, makeRedirectUri } from 'expo-auth-session';
// import * as WebBrowser from 'expo-web-browser';
// import Constants from 'expo-constants';
// import { Platform } from 'react-native';
// import { useAuthStore } from '@/stores/auth';
// import { authService } from './auth';
// import { mixpanelService } from './mixpanel';

// // Register the redirect URI for web browser
// WebBrowser.maybeCompleteAuthSession();

// // Get the Google OAuth client IDs from app.config.js
// const googleAndroidClientId = Constants.expoConfig?.extra?.googleAndroidClientId;
// const googleIosClientId = Constants.expoConfig?.extra?.googleIosClientId;

// // Configure the discovery document for Google OAuth
// // Using a static discovery document instead of the Hook version
// const discovery = {
//   authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
//   tokenEndpoint: 'https://oauth2.googleapis.com/token',
//   revocationEndpoint: 'https://oauth2.googleapis.com/revoke',
// };

// /**
//  * Handles Google Sign-In using Expo Auth Session
//  * @returns Promise with the authentication result
//  */
// export const signInWithGoogle = async (promptAsync: any) => {
//   try {
//     const result = await promptAsync();
//     console.log('Auth result:', JSON.stringify(result, null, 2));

//     if (result.type === 'success') {
//       // For authorization code flow, we need to exchange the code for tokens
//       const authCode = result.params?.code;
//       const accessToken = result.authentication?.accessToken;

//       // If we have an access token directly (fallback), use it
//       // Otherwise, we should have an authorization code
//       const tokenToUse = accessToken || authCode;

//       if (!tokenToUse) {
//         throw new Error('Failed to get authorization code or access token from Google');
//       }

//       console.log('Got token/code:', tokenToUse);

//       // Send the token to our backend to authenticate or create a user
//       const response = await authService.googleAuth({
//         access_token: tokenToUse,
//       });

//       if (response.data) {
//         // Store auth data in zustand store
//         useAuthStore.getState().login({
//           id: response.data.user.id,
//           displayName: response.data.user.display_name,
//           email: response.data.user.email,
//           accessLevel: response.data.user.access_level,
//           token: response.data.token,
//           refreshToken: response.data.refreshToken,
//           profileImageUrl: response.data.user.profile_image_url,
//           emailVerified: response.data.user.email_verified
//         });

//         // Track user sign up or sign in in Mixpanel
//         try {
//           if (response.data.isNewUser) {
//             await mixpanelService.trackSignUp(
//               response.data.user.email,
//               response.data.user.display_name,
//               'google'
//             );
//           } else {
//             await mixpanelService.trackSignIn(
//               response.data.user.email,
//               response.data.user.display_name,
//               'google'
//             );
//           }
//           // Always identify the user (new or existing)
//           await mixpanelService.identify(response.data.user.id.toString(), {
//             email: response.data.user.email,
//             name: response.data.user.display_name,
//             display_name: response.data.user.display_name,
//           });
//         } catch (analyticsError) {
//           console.error('Analytics tracking error:', analyticsError);
//           // Don't fail the authentication if analytics fails
//         }

//         return { success: true, data: response.data };
//       }
//     } else if (result.type === 'error') {
//       console.error('Auth error:', result.error);
//       throw new Error(result.error?.message || 'Authentication failed');
//     } else {
//       throw new Error('Authentication was cancelled or failed');
//     }
//   } catch (error) {
//     console.error('Google sign-in error:', error);
//     return {
//       success: false,
//       error: error instanceof Error ? error.message : 'An unexpected error occurred'
//     };
//   }
// };
