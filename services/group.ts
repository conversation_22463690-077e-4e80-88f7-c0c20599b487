import { api } from './api';
import { useAuthStore } from '@/stores/auth';
import { ContactResponse } from './contact';

// Group interfaces
export interface GroupRequest {
  name: string;
  owner_id?: number;
  enabled?: boolean;
  contacts?: number[]; // Array of contact IDs to associate with the group
  members?: number[]; // Array of user IDs that are members of this group
}

export interface GroupResponse {
  id: number;
  name: string;
  owner_id?: number;
  enabled?: boolean;
  members?: number[];
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface GroupWithMembersResponse extends Omit<GroupResponse, 'members'> {
  members?: ContactResponse[];
}

// Helper function to add auth headers
const withAuth = (): Record<string, string> => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Group service
export const groupService = {
  // Get all groups
  getAllGroups: async () => {
    try {
      return await api.get<GroupResponse[]>('/groups', undefined, withAuth());
    } catch (error) {
      console.error('Get all groups service error:', error);
      throw error;
    }
  },

  // Get group by ID
  getGroupById: async (id: number) => {
    try {
      return await api.get<GroupResponse>(`/groups/${id}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get group by ID ${id} service error:`, error);
      throw error;
    }
  },

  // Get group members
  getGroupMembers: async (id: number) => {
    try {
      // Validate the group ID
      if (!id) {
        return { error: 'Invalid group ID', data: null, status: 400 };
      }

      // Get the group details first to access the members array
      // Then filter contacts to only include those in the group
      const groupResponse = await api.get<GroupResponse>(`/groups/${id}`, undefined, withAuth());

      if (groupResponse.error) {
        return {
          error: groupResponse.error,
          data: null,
          status: groupResponse.status
        };
      }

      // Get all contacts
      const contactsResponse = await api.get<ContactResponse[]>('/contacts', undefined, withAuth());

      if (contactsResponse.error) {
        return {
          error: contactsResponse.error,
          data: null,
          status: contactsResponse.status
        };
      }

      // Filter contacts based on the group's members array
      const groupMembers = groupResponse.data?.members || [];
      const allContacts = contactsResponse.data || [];

      // Filter contacts to only include those whose IDs are in the group's members array
      const actualMembers = allContacts.filter(contact =>
        groupMembers.includes(contact.id)
      );

      return {
        data: {
          ...groupResponse.data,
          members: actualMembers
        },
        error: null,
        status: 200
      };
    } catch (error) {
      console.error(`Get group members for group ${id} service error:`, error);
      return {
        error: error instanceof Error ? error.message : `Failed to get group ${id} members`,
        data: null,
        status: 500
      };
    }
  },

  // Create a new group
  createGroup: async (groupData: GroupRequest) => {
    try {
      const userData = useAuthStore.getState().userData;
      const groupWithOwner = {
        ...groupData,
        owner_id: userData?.id
      };

      return await api.post<GroupResponse>('/groups', groupWithOwner, withAuth());
    } catch (error) {
      console.error('Create group service error:', error);
      throw error;
    }
  },

  // Update a group
  updateGroup: async (id: number, groupData: Partial<GroupRequest>) => {
    try {
      return await api.put<GroupResponse>(`/groups/${id}`, groupData, withAuth());
    } catch (error) {
      console.error(`Update group ${id} service error:`, error);
      throw error;
    }
  },

  // Update group members
  updateGroupMembers: async (id: number, memberIds: number[]) => {
    try {
      // Validate the group ID
      if (!id) {
        return { error: 'Invalid group ID', data: null, status: 400 };
      }

      // Update the members array directly
      return await api.put<GroupResponse>(`/groups/${id}`, { members: memberIds }, withAuth());
    } catch (error) {
      console.error(`Update group members for group ${id} service error:`, error);
      return {
        error: error instanceof Error ? error.message : `Failed to update group ${id} members`,
        data: null,
        status: 500
      };
    }
  },

  // Delete a group
  deleteGroup: async (id: number) => {
    try {
      return await api.delete<void>(`/groups/${id}`, withAuth());
    } catch (error) {
      console.error(`Delete group ${id} service error:`, error);
      throw error;
    }
  },

  // Update group order
  updateGroupOrder: async (groupOrders: { id: number; sort_order: number }[]) => {
    try {
      return await api.put<void>('/groups/order', { groupOrders }, withAuth());
    } catch (error) {
      console.error('Update group order service error:', error);
      throw error;
    }
  }
};
