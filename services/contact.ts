import { api } from './api';
import { useAuthStore } from '@/stores/auth';

// Contact interfaces
export interface ContactRequest {
  first_name: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  is_priority?: boolean;
  hide_events?: boolean;
  is_blocked?: boolean;
  enabled?: boolean;
}

// User interface for contact's associated user
export interface UserResponse {
  id: number;
  display_name: string;
  first_name?: string;
  last_name?: string;
  profile_image_url?: string;
}

export interface ContactResponse {
  id: number;
  user_id?: number;
  first_name: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  owner_id?: number;
  is_priority?: boolean;
  hide_events?: boolean;
  is_blocked?: boolean;
  enabled?: boolean;
  created_at: string;
  updated_at: string;
  user?: UserResponse;
}

// Helper function to add auth headers
const withAuth = (): Record<string, string> => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bear<PERSON> ${token}` } : {};
};

// Contact service
export const contactService = {
  // Get all contacts
  getAllContacts: async () => {
    try {
      return await api.get<ContactResponse[]>('/contacts', undefined, withAuth());
    } catch (error) {
      console.error('Get all contacts service error:', error);
      throw error;
    }
  },

  // Get contact by ID
  getContactById: async (id: number) => {
    try {
      return await api.get<ContactResponse>(`/contacts/${id}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get contact by ID ${id} service error:`, error);
      throw error;
    }
  },

  // Create a new contact
  createContact: async (contactData: ContactRequest) => {
    try {
      const userData = useAuthStore.getState().userData;
      const contactWithOwner = {
        ...contactData,
        owner_id: userData?.id
      };

      return await api.post<ContactResponse>('/contacts', contactWithOwner, withAuth());
    } catch (error) {
      console.error('Create contact service error:', error);
      throw error;
    }
  },

  // Update a contact
  updateContact: async (id: number, contactData: Partial<ContactRequest>) => {
    try {
      return await api.put<ContactResponse>(`/contacts/${id}`, contactData, withAuth());
    } catch (error) {
      console.error(`Update contact ${id} service error:`, error);
      throw error;
    }
  },

  // Delete a contact
  deleteContact: async (id: number) => {
    try {
      return await api.delete<void>(`/contacts/${id}`, withAuth());
    } catch (error) {
      console.error(`Delete contact ${id} service error:`, error);
      throw error;
    }
  },

  // Bulk create contacts from device
  bulkCreateContacts: async (contacts: ContactRequest[]) => {
    try {
      const userData = useAuthStore.getState().userData;
      const contactsWithOwner = contacts.map(contact => ({
        ...contact,
        owner_id: userData?.id
      }));

      // Since there's no bulk endpoint, we'll create them one by one
      const results = await Promise.all(
        contactsWithOwner.map(contact =>
          api.post<ContactResponse>('/contacts', contact, withAuth())
        )
      );

      return results;
    } catch (error) {
      console.error('Bulk create contacts service error:', error);
      throw error;
    }
  }
};
