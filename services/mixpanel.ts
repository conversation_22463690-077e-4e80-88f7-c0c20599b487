import { Mixpanel } from 'mixpanel-react-native';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Get Mixpanel token from app config
const mixpanelToken = Constants.expoConfig?.extra?.mixpanelToken;

class MixpanelService {
  private mixpanel: Mixpanel | null = null;
  private isInitialized = false;

  /**
   * Initialize Mixpanel with the token from environment variables
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || !mixpanelToken) {
      return;
    }

    try {
      this.mixpanel = new Mixpanel(mixpanelToken, true); // true enables autocapture
      await this.mixpanel.init();
      this.isInitialized = true;
      console.log('Mixpanel initialized successfully with autocapture enabled');
    } catch (error) {
      console.error('Failed to initialize Mixpanel:', error);
    }
  }

  /**
   * Track an event with optional properties
   */
  async track(eventName: string, properties?: Record<string, any>): Promise<void> {
    if (!this.isInitialized || !this.mixpanel) {
      console.warn('Mixpanel not initialized, skipping track event:', eventName);
      return;
    }

    try {
      this.mixpanel.track(eventName, properties);
      console.log('Mixpanel event tracked:', eventName, properties);
    } catch (error) {
      console.error('Failed to track Mixpanel event:', eventName, error);
    }
  }

  /**
   * Identify a user and set user properties
   */
  async identify(userId: string, userProperties?: Record<string, any>): Promise<void> {
    if (!this.isInitialized || !this.mixpanel) {
      console.warn('Mixpanel not initialized, skipping identify');
      return;
    }

    try {
      this.mixpanel.identify(userId);
      if (userProperties) {
        this.mixpanel.getPeople().set(userProperties);
      }
      console.log('Mixpanel user identified:', userId, userProperties);
    } catch (error) {
      console.error('Failed to identify Mixpanel user:', userId, error);
    }
  }

  /**
   * Set user properties
   */
  async setUserProperties(properties: Record<string, any>): Promise<void> {
    if (!this.isInitialized || !this.mixpanel) {
      console.warn('Mixpanel not initialized, skipping set user properties');
      return;
    }

    try {
      this.mixpanel.getPeople().set(properties);
      console.log('Mixpanel user properties set:', properties);
    } catch (error) {
      console.error('Failed to set Mixpanel user properties:', error);
    }
  }

  /**
   * Track user sign up with required properties
   */
  async trackSignUp(userEmail: string, userName: string, source: 'email' | 'google'): Promise<void> {
    const signUpProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
      Source: source,
      Email: userEmail,
      Name: userName,
    };

    await this.track('User Sign Up', signUpProperties);
  }

  /**
   * Track user sign in
   */
  async trackSignIn(userEmail: string, userName: string, source: 'email' | 'google'): Promise<void> {
    const signInProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
      Source: source,
      Email: userEmail,
      Name: userName,
    };

    await this.track('Sign In', signInProperties);
  }

  /**
   * Track user log out
   */
  async trackLogOut(): Promise<void> {
    const logOutProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
    };

    await this.track('Log Out', logOutProperties);
  }

  /**
   * Track event creation
   */
  async trackCreateEvent(eventName: string, eventType?: string, participantCount?: number): Promise<void> {
    const createEventProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
      'Event Name': eventName,
      'Event Type': eventType || 'Unknown',
      'Participant Count': participantCount || 0,
    };

    await this.track('Create Event', createEventProperties);
  }

  /**
   * Track joining an event
   */
  async trackJoinEvent(eventId: number, eventName: string, eventType?: string): Promise<void> {
    const joinEventProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
      'Event ID': eventId,
      'Event Name': eventName,
      'Event Type': eventType || 'Unknown',
    };

    await this.track('Join Event', joinEventProperties);
  }

  /**
   * Track sending a message
   */
  async trackSendMessage(eventId?: number, messageType: 'event' | 'direct' = 'event'): Promise<void> {
    const sendMessageProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
      'Message Type': messageType,
      'Event ID': eventId,
    };

    await this.track('Send Message', sendMessageProperties);
  }

  /**
   * Track ticket payment
   */
  async trackPayTicket(eventId: number, eventName: string, amount?: number, currency: string = 'USD'): Promise<void> {
    const payTicketProperties = {
      OS: Platform.OS === 'ios' ? 'iOS' : 'Android',
      'Event ID': eventId,
      'Event Name': eventName,
      'Amount': amount,
      'Currency': currency,
    };

    await this.track('Pay Ticket', payTicketProperties);
  }

  /**
   * Reset user data (useful for logout)
   */
  async reset(): Promise<void> {
    if (!this.isInitialized || !this.mixpanel) {
      console.warn('Mixpanel not initialized, skipping reset');
      return;
    }

    try {
      this.mixpanel.reset();
      console.log('Mixpanel user data reset');
    } catch (error) {
      console.error('Failed to reset Mixpanel user data:', error);
    }
  }

  /**
   * Get the current Mixpanel instance (for advanced usage)
   */
  getInstance(): Mixpanel | null {
    return this.mixpanel;
  }

  /**
   * Check if Mixpanel is initialized
   */
  getIsInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export a singleton instance
export const mixpanelService = new MixpanelService();
