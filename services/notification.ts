import { api, withAuth } from './api';
import { useAuthStore } from '@/stores/auth';
import { NotificationIconType } from '@/components/NotificationIcon';
import { connectRequestService } from './connectRequest';

// Notification interfaces
export interface NotificationAction {
  label: string;
  primary: boolean;
}

export interface NotificationResponse {
  id: number;
  sender_id: number;
  receiver_id: number;
  subject: string;
  notice_type: string;
  message: string;
  read: boolean;
  connect_request_id?: number;
  event_id?: number;
  avatar?: string;
  created_at: string;
  updated_at: string;
  sender?: {
    id: number;
    display_name: string;
    first_name?: string;
    last_name?: string;
    profile_image_url?: string;
  };
}

export interface NotificationViewModel {
  id: number;
  type: 'connection' | 'cancellation' | 'payment' | 'event' | 'date' | 'comment';
  avatar?: string;
  iconType?: NotificationIconType;
  text: string;
  actions?: NotificationAction[];
  boldText?: string[];
  read: boolean;
  created_at: string;
  connect_request_id?: number;
  event_id?: number;
  isAccepted?: boolean;
}

// Helper function to convert API notification to view model
const mapNotificationToViewModel = (notification: NotificationResponse): NotificationViewModel => {
  // Determine notification type based on notice_type
  let type: 'connection' | 'cancellation' | 'payment' | 'event' | 'date' | 'comment' = 'event';
  let iconType: NotificationIconType | undefined;

  switch (notification.notice_type) {
    case 'connection_request':
      type = 'connection';
      break;
    case 'event_cancellation':
      type = 'cancellation';
      iconType = 'calendar-x';
      break;
    case 'payment_due':
    case 'payment_received':
      type = 'payment';
      iconType = 'money';
      break;
    case 'event_quorum_reached':
    case 'event_update':
      type = 'event';
      iconType = 'calendar-check';
      break;
    case 'event_date_confirmed':
      type = 'date';
      iconType = 'calendar-star';
      break;
    case 'new_comment':
      type = 'comment';
      iconType = 'message';
      break;
  }

  // Extract bold text parts from the message
  // This is a simple implementation - in a real app, you might want to use a more sophisticated approach
  const boldTextParts: string[] = [];

  // Look for quoted text or names in the message
  const quotedTextRegex = /"([^"]+)"/g;
  let match;
  while ((match = quotedTextRegex.exec(notification.message)) !== null) {
    boldTextParts.push(match[1]);
  }

  // Add sender name if available
  if (notification.sender?.display_name) {
    boldTextParts.push(notification.sender.display_name);
  }

  // Determine if actions are needed based on notification type
  let actions: NotificationAction[] | undefined;
  let isAccepted: boolean | undefined;

  // For connection requests, show actions only if the connect request has not been processed yet
  if (notification.notice_type === 'connection_request') {
    // Check if the connect request has already been accepted or declined
    const connectRequestStatus = (notification as any).connect_request_status;
    const isProcessed = connectRequestStatus && (connectRequestStatus.is_accepted || connectRequestStatus.is_declined);

    // Check if this is a Connection Accepted notification by looking at the message
    const isConnectionAccepted = notification.message.includes('accepted your connection request');

    if (isConnectionAccepted) {}
    // Only show actions if the connect request has not been processed yet and it's not a Connection Accepted notification
    else if (!isProcessed) {
      actions = [
        { label: 'ACCEPT', primary: true },
        { label: 'IGNORE', primary: false }
      ];
    } else {
      // If the connect request has been processed, set isAccepted based on the status
      if (connectRequestStatus) {
        if (connectRequestStatus.is_accepted) {
          isAccepted = true;
        } else if (connectRequestStatus.is_declined) {
          isAccepted = false;
        }
      }
    }
  } else if (notification.notice_type === 'payment_due' && !notification.read) {
    actions = [{ label: 'PAY NOW', primary: true }];
  } else if ((notification.notice_type === 'event_quorum_reached' ||
              notification.notice_type === 'event_date_confirmed' ||
              notification.notice_type === 'new_comment') &&
              !notification.read) {
    actions = [{ label: 'VIEW', primary: true }];
  }

  // Use the avatar from the notification or from the sender's profile_image_url if available
  // The actual signing of the URL will be done in the component
  const avatarUrl = notification.avatar ||
                   (notification.sender?.profile_image_url) ||
                   undefined;

  return {
    id: notification.id,
    type,
    iconType,
    text: notification.message,
    boldText: boldTextParts,
    actions,
    read: notification.read,
    created_at: notification.created_at,
    connect_request_id: notification.connect_request_id,
    event_id: notification.event_id,
    avatar: avatarUrl,
    isAccepted
  };
};

// Notification service
export const notificationService = {
  // Get all notifications for the current user
  getUserNotifications: async () => {
    try {
      const userData = useAuthStore.getState().userData;
      if (!userData?.id) {
        console.log('User not logged in, cannot get notifications');
        return { data: [], error: 'User not logged in' };
      }

      const response = await api.get<NotificationResponse[]>(
        `/notifications/user/${userData.id}`,
        undefined,
        withAuth()
      );

      if (response.data) {
        // Convert API response to view model
        const viewModels = response.data.map(mapNotificationToViewModel);

        // Sort by creation date (newest first)
        viewModels.sort((a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        return { data: viewModels, error: undefined };
      }

      return { data: [], error: response.error };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return { data: [], error: 'Failed to fetch notifications' };
    }
  },

  // Mark a notification as read
  markAsRead: async (notificationId: number) => {
    try {
      // Use custom method since api doesn't have a patch method
      const response = await api.post<NotificationResponse>(
        `/notifications/${notificationId}/read`,
        {},
        withAuth()
      );

      return { success: response.status === 200, error: response.error };
    } catch (error) {
      console.error(`Error marking notification ${notificationId} as read:`, error);
      return { success: false, error: 'Failed to mark notification as read' };
    }
  },

  // Handle notification action
  handleNotificationAction: async (notification: NotificationViewModel, action: string) => {
    try {
      // Mark the notification as read first
      await notificationService.markAsRead(notification.id);

      // Handle different actions based on notification type
      if (notification.type === 'connection' && notification.actions) {
        // Extract connect request ID from the notification data
        // Use the connect_request_id if available, otherwise fall back to notification ID
        const connectRequestId = notification.connect_request_id || notification.id;

        if (action === 'ACCEPT') {
          // Accept the connect request
          const response = await connectRequestService.acceptConnectRequest(connectRequestId);
          return { success: true, data: response.data, error: response.error };
        } else if (action === 'IGNORE') {
          // Decline the connect request
          const response = await connectRequestService.declineConnectRequest(connectRequestId);
          return { success: true, data: response.data, error: response.error };
        }
      } else if (notification.type === 'payment' && action === 'PAY NOW') {
        // Handle payment action
        // This would navigate to the payment screen or trigger a payment flow
        return { success: true, data: { message: 'Payment flow initiated' }, error: undefined };
      } else if (['event', 'date', 'comment'].includes(notification.type) && action === 'VIEW') {
        // Handle view action
        // This would navigate to the relevant screen
        return { success: true, data: { message: 'View action handled' }, error: undefined };
      }

      return { success: false, data: undefined, error: 'Unsupported action' };
    } catch (error) {
      console.error(`Error handling notification action:`, error);
      return { success: false, data: undefined, error: 'Failed to handle notification action' };
    }
  }
};

// Export both the service and the types
export default notificationService;
