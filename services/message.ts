import { api, withAuth } from './api';
import { mixpanelService } from './mixpanel';

// Message interfaces
export interface MessageResponse {
  id: number;
  text: string;
  user_id: number;
  event_id: number;
  created_at: string;
  updated_at: string;
  user?: {
    id: number;
    display_name: string;
    first_name?: string;
    last_name?: string;
    profile_image_url?: string;
  };
}

export interface CreateMessageRequest {
  text: string;
}

// Message service
export const messageService = {
  // Get all messages for an event
  getEventMessages: async (eventId: number) => {
    try {
      return await api.get<MessageResponse[]>(`/messages/events/${eventId}/messages`, undefined, withAuth());
    } catch (error) {
      console.error(`Get messages for event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Create a new message
  createMessage: async (eventId: number, messageData: CreateMessageRequest) => {
    try {
      const response = await api.post<MessageResponse>(`/messages/events/${eventId}/messages`, messageData, withAuth());

      // Track send message in Mixpanel if successful
      if (response.data) {
        try {
          await mixpanelService.trackSendMessage(eventId, 'event');
        } catch (analyticsError) {
          console.error('Analytics tracking error:', analyticsError);
          // Don't fail the message sending if analytics fails
        }
      }

      return response;
    } catch (error) {
      console.error(`Create message for event ${eventId} service error:`, error);
      throw error;
    }
  }
};
