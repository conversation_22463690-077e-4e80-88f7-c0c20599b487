import { api } from './api';
import { AccessLevel } from '@/stores/auth';

// Interfaces for authentication requests and responses
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  display_name: string;
  first_name?: string;
  last_name?: string;
  email: string;
  password: string;
}

export interface GoogleAuthRequest {
  access_token: string;
  code_verifier?: string;
  redirect_uri?: string;
  platform?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  token: string;
  password: string;
}

export interface AuthResponse {
  message: string;
  token: string;
  refreshToken?: string;
  isNewUser?: boolean;
  user: {
    id: number;
    display_name: string;
    email: string;
    access_level?: AccessLevel;
    profile_image_url?: string;
    email_verified?: boolean;
  };
}

// Authentication service
export const authService = {
  // Login user
  login: async (credentials: LoginRequest) => {
    try {
      return await api.post<AuthResponse>('/auth/login', credentials);
    } catch (error) {
      console.error('Login service error:', error);
      throw error;
    }
  },

  // Register new user
  register: async (userData: RegisterRequest) => {
    try {
      return await api.post<AuthResponse>('/auth/register', userData);
    } catch (error) {
      console.error('Registration service error:', error);
      throw error;
    }
  },

  // Google authentication
  googleAuth: async (tokenData: GoogleAuthRequest) => {
    try {
      return await api.post<AuthResponse>('/auth/google/token', tokenData);
    } catch (error) {
      console.error('Google auth service error:', error);
      throw error;
    }
  },

  // Logout user
  logout: async (token: string) => {
    try {
      return await api.post('/auth/logout', {}, { 'Authorization': `Bearer ${token}` });
    } catch (error) {
      console.error('Logout service error:', error);
      throw error;
    }
  },

  // Refresh token
  refreshToken: async (refreshToken: string) => {
    try {
      return await api.post<{ token: string }>('/auth/refresh-token', { refreshToken });
    } catch (error) {
      console.error('Token refresh service error:', error);
      throw error;
    }
  },

  // Forgot password
  forgotPassword: async (data: ForgotPasswordRequest) => {
    try {
      return await api.post<{ message: string }>('/auth/forgot-password', data);
    } catch (error) {
      console.error('Forgot password service error:', error);
      throw error;
    }
  },

  // Reset password
  resetPassword: async (data: ResetPasswordRequest) => {
    try {
      return await api.post<{ message: string }>('/auth/reset-password', data);
    } catch (error) {
      console.error('Reset password service error:', error);
      throw error;
    }
  },

  // Send verification email
  sendVerificationEmail: async (email: string) => {
    try {
      return await api.post<{ message: string }>('/auth/send-verification', { email });
    } catch (error) {
      console.error('Send verification email service error:', error);
      throw error;
    }
  },

  // Resend verification email (authenticated)
  resendVerificationEmail: async () => {
    try {
      return await api.post<{ message: string }>('/auth/resend-verification');
    } catch (error) {
      console.error('Resend verification email service error:', error);
      throw error;
    }
  },

  // Check email verification status
  checkEmailVerificationStatus: async () => {
    try {
      return await api.get<{ email_verified: boolean; email: string }>('/auth/verification-status');
    } catch (error) {
      console.error('Check verification status service error:', error);
      throw error;
    }
  },
};
