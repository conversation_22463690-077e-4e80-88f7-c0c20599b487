import { useAuthStore } from '@/stores/auth';
import { authService } from './auth';

/**
 * Development version of Google Sign-In that bypasses the actual OAuth flow
 * This is useful for testing in Expo Go where OAuth might be problematic
 * @returns Promise with mock authentication result
 */
export const signInWithGoogleDev = async () => {
  try {
    // Create a mock user with a test email
    const mockUserData = {
      email: '<EMAIL>',
      name: 'Test User',
      given_name: 'Test',
      family_name: 'User',
      sub: 'mock-google-id-123456789'
    };

    // Send the mock data to our backend
    const response = await authService.googleAuth({
      // We're not actually using this token in dev mode
      // The backend will create/find a user with the test email
      access_token: 'mock-token-for-development'
    });

    if (response.data) {
      // Store auth data in zustand store
      useAuthStore.getState().login({
        id: response.data.user.id,
        displayName: response.data.user.display_name,
        email: response.data.user.email,
        accessLevel: response.data.user.access_level,
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        profileImageUrl: response.data.user.profile_image_url,
        emailVerified: response.data.user.email_verified
      });

      return { success: true, data: response.data };
    }

    return {
      success: false,
      error: 'Failed to authenticate with mock data'
    };
  } catch (error) {
    console.error('Development Google sign-in error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
};
