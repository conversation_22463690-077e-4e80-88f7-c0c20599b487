import { api } from './api';
import { useAuthStore } from '@/stores/auth';

// User interfaces
export interface UserUpdateRequest {
  display_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  auto_locate?: boolean;
  profile_image_url?: string;
  enabled?: boolean;
}

export interface UserResponse {
  id: number;
  display_name: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone_number?: string;
  access_level: string;
  auto_locate?: boolean;
  profile_image_url?: string;
  created_at?: string;
  updated_at?: string;
}

// Helper function to add auth headers
const withAuth = () => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// User service
export const userService = {
  // Get user by ID
  getUserById: async (id: number) => {
    try {
      return await api.get<UserResponse>(`/users/${id}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get user by ID ${id} service error:`, error);
      throw error;
    }
  },

  // Update user
  updateUser: async (id: number, userData: UserUpdateRequest) => {
    try {
      const headers = withAuth();
      const response = await api.put<UserResponse>(`/users/${id}`, userData, headers);
      return response;
    } catch (error) {
      console.error(`Update user ${id} service error:`, error);
      throw error;
    }
  },

  // Get current user
  getCurrentUser: async () => {
    try {
      const userData = useAuthStore.getState().userData;
      if (!userData?.id) {
        throw new Error('No user ID found in auth store');
      }
      return await api.get<UserResponse>(`/users/${userData.id}`, undefined, withAuth());
    } catch (error) {
      console.error('Get current user service error:', error);
      throw error;
    }
  }
};
