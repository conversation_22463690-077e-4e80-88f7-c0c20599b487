import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { api, withAuth } from './api';
// cloudfrontService import removed to prevent double-signing

// Interface for direct upload response
interface DirectUploadResponse {
  fileUrl: string;
  key: string;
}

// Real S3 upload service
export const uploadService = {
  // Function to upload an image to S3
  uploadImageToS3: async (uri: string): Promise<string> => {
    try {

      // Get file information
      const fileName = uploadService.getFileNameFromUri(uri);
      const fileExtension = uploadService.getFileExtensionFromUri(uri);
      const fileType = `image/${fileExtension}`;

      // Check if file exists
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist');
      }

      // Create a form data object for direct upload
      const formData = new FormData();

      // Add the file to the form data
      formData.append('file', {
        uri: uri,
        name: fileName,
        type: fileType
      } as any);

      // Use a custom axios config for form data
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...withAuth()
        }
      };

      const response = await api.post<DirectUploadResponse>('/uploads/direct', formData, config.headers);

      if (!response.data || !response.data.fileUrl) {
        throw new Error('Failed to upload file');
      }

      const { fileUrl } = response.data;

      return fileUrl;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw error;
    }
  },

  // Helper function to get the file name from a URI
  getFileNameFromUri: (uri: string): string => {
    return uri.split('/').pop() || 'unknown_file';
  },

  // Helper function to get the file extension from a URI
  getFileExtensionFromUri: (uri: string): string => {
    const fileName = uri.split('/').pop() || '';
    return fileName.includes('.') ? fileName.split('.').pop() || '' : 'jpg';
  }
};
