import axios, { AxiosError, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/auth';
import { Platform } from 'react-native';

const API_URL = 'https://api.qwrm.app/api';

// const API_URL = process.env.NODE_ENV === 'development'
//   ? Platform.select({
//       ios: 'http://localhost:3000/api',
//       android: 'http://********:3000/api', // Android emulator special IP
//       default: 'http://localhost:3000/api',
//     })
//   : 'https://api.qwrm.app/api';

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
});

// Add request interceptor for logging and adding auth token
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Get the current auth token from the store
    const authState = useAuthStore.getState();
    const token = authState.userData?.token;

    // If token exists and Authorization header is not already set, add it
    if (token && !config.headers['Authorization']) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  },
  (error: any) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging and token refresh
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    console.error('Response error:', error.message);

    // Log error details
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);

      // Get the original request config
      const originalRequest = error.config as InternalAxiosRequestConfig & { _isRetry?: boolean };

      // Handle token expiration
      if (error.response.status === 403 &&
          error.response.data &&
          (error.response.data as any).message === 'Invalid or expired token' &&
          !originalRequest._isRetry) { // Prevent infinite refresh loops

        try {
          console.log('Token expired, attempting to refresh...');
          // Get the current auth state
          const authState = useAuthStore.getState();
          const refreshToken = authState.userData?.refreshToken;

          if (refreshToken) {
            // Mark this request as a retry attempt
            originalRequest._isRetry = true;

            // Try to refresh the token using axiosInstance without auth headers
            const refreshInstance = axios.create({
              baseURL: API_URL,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              }
            });

            const response = await refreshInstance.post('/auth/refresh-token', {
              refreshToken
            });

            if (response.data && response.data.token) {
              console.log('Token refreshed successfully');
              // Update the token in the auth store
              useAuthStore.getState().updateUserData({
                token: response.data.token
              });

              // Update the Authorization header for the original request
              originalRequest.headers['Authorization'] = `Bearer ${response.data.token}`;

              // Retry the original request with the new token
              return axiosInstance(originalRequest);
            } else {
              console.error('Token refresh response did not contain a new token');
              throw new Error('Failed to refresh token');
            }
          } else {
            console.error('No refresh token available');
            throw new Error('No refresh token available');
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // If refresh fails, log the user out
          useAuthStore.getState().logout();
          // Note: In React Native, we can't redirect here
          // The app will handle redirection based on auth state
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.request);
    }

    return Promise.reject(error);
  }
);

// Interface for API response
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

// Generic function to handle API requests
async function apiRequest<T>(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any,
  headers: Record<string, string> = {}
): Promise<ApiResponse<T>> {
  try {
    const config: AxiosRequestConfig<any> = {
      method,
      url: endpoint,
      headers,
      data: method !== 'GET' ? data : undefined,
      params: method === 'GET' && data ? data : undefined,
    };

    const response: AxiosResponse<T> = await axiosInstance(config);

    return {
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    console.error(`API ${method} request to ${endpoint} failed:`, error);

    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<any>;
      return {
        error: axiosError.response?.data?.message || axiosError.message || 'An error occurred',
        status: axiosError.response?.status || 500,
      };
    }

    return {
      error: error instanceof Error ? error.message : 'Network error',
      status: 500,
    };
  }
}

// Export API methods
export const api = {
  get: <T>(endpoint: string, params?: any, headers?: Record<string, string>) =>
    apiRequest<T>(endpoint, 'GET', params, headers),

  post: <T>(endpoint: string, data: any, headers?: Record<string, string>) =>
    apiRequest<T>(endpoint, 'POST', data, headers),

  put: <T>(endpoint: string, data: any, headers?: Record<string, string>) =>
    apiRequest<T>(endpoint, 'PUT', data, headers),

  delete: <T>(endpoint: string, headers?: Record<string, string>) =>
    apiRequest<T>(endpoint, 'DELETE', undefined, headers),
};

// Function to add authorization header
export function withAuth(token?: string): Record<string, string> {
  // If no token is provided, try to get it from the auth store
  if (!token) {
    const authState = useAuthStore.getState();
    token = authState.userData?.token;
  }

  return token ? {
    'Authorization': `Bearer ${token}`,
  } : {};
}
