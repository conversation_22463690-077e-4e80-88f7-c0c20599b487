import { api, withAuth } from './api';
import { useAuthStore } from '@/stores/auth';
import { mixpanelService } from './mixpanel';

// Date interface for event dates
export interface EventDate {
  date: string;
  time: string;
  isoDate?: string; // ISO date string for API
  isoTime?: string; // ISO time string for API
  combinedIsoDateTime?: string; // Combined ISO date and time string for API
}

// Event interfaces
export interface CreateEventRequest {
  name: string;
  date: string | string[]; // Can be a string or array of strings (timestamps)
  dates?: EventDate[]; // Optional array of date objects with date and time
  location?: string; // Location name (e.g., "<PERSON>'s house") - optional with default
  address: string; // Full address
  // New address fields
  location_name?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  meeting_point?: string;
  owner?: string;
  owner_id?: number;
  image_url: string;
  event_link?: string; // URL for the event (optional)
  has_options?: boolean;
  quorum_met?: boolean;
  enabled?: boolean;
  cost?: number; // Cost of the event (optional)
  cost_purpose?: string; // Purpose of the cost (optional)
  payment_type?: string; // Type of payment (pay_me_back, chip_in, buy_ticket, bring_wallet)
  description?: string; // Description of the event
  invitees?: number[]; // Array of user IDs who were invited to the event
  quorum?: number; // Minimum number of people needed for the event to become official
  max_participants?: number | null; // Maximum number of participants allowed (null means unlimited)
  multiple_dates?: boolean; // Whether users can select multiple dates
  response_cutoff?: number; // Number of hours before the event when users can no longer sign up
}

export interface EventDateResponse {
  id: number;
  event_id: number;
  date: string; // Timestamp string
  participants: number[]; // Array of user IDs who have signed up for this specific date
  created_at: string;
  updated_at: string;
}

export interface EventResponse {
  id: number;
  name: string;
  date: string[]; // Array of timestamp strings (virtual field from event_dates)
  eventDates?: EventDateResponse[]; // Array of event dates
  location?: string;
  address: string;
  // New address fields
  location_name?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  meeting_point?: string;
  owner: string;
  owner_id?: number;
  image_url: string;
  event_link?: string; // URL for the event (optional)
  has_options: boolean;
  quorum_met: boolean;
  enabled: boolean;
  cost?: number; // Cost of the event (optional)
  cost_purpose?: string; // Purpose of the cost (optional)
  payment_type?: string; // Type of payment (pay_me_back, chip_in, buy_ticket, bring_wallet)
  description?: string; // Description of the event
  invitees: number[]; // Array of user IDs who were invited to the event
  participants?: any[]; // Array of participants
  participants_count?: number; // Number of participants
  paid_participants?: number[]; // Array of user IDs who have paid
  quorum?: number; // Minimum number of people needed for the event to become official
  max_participants?: number | null; // Maximum number of participants allowed (null means unlimited)
  confirmed_date?: string; // The date that met quorum and was confirmed
  multiple_dates?: boolean; // Whether users can select multiple dates
  response_cutoff?: number; // Number of hours before the event when users can no longer sign up
  created_at: string;
  updated_at: string;
}

// Event date service
export const eventDateService = {
  // Get all dates for an event
  getEventDates: async (eventId: number) => {
    try {
      return await api.get<EventDateResponse[]>(`/event-dates/events/${eventId}/dates`, undefined, withAuth());
    } catch (error) {
      console.error(`Get dates for event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Sign up for a specific date
  signUpForDate: async (eventId: number, dateId: number) => {
    try {
      return await api.post<{success: boolean, message: string, participants: number[]}>(
        `/event-dates/events/${eventId}/dates/${dateId}/signup`,
        {},
        withAuth()
      );
    } catch (error) {
      console.error(`Sign up for date ${dateId} of event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Sign up for multiple dates
  signUpForMultipleDates: async (eventId: number, dateIds: number[]) => {
    try {
      const response = await api.post<{success: boolean, message: string, updatedEvent: EventResponse}>(
        `/event-dates/events/${eventId}/signup-multiple`,
        { dateIds },
        withAuth()
      );

      // Track join event in Mixpanel if successful
      if (response.data && response.data.success && response.data.updatedEvent) {
        try {
          await mixpanelService.trackJoinEvent(
            eventId,
            response.data.updatedEvent.name,
            response.data.updatedEvent.payment_type || 'free'
          );
        } catch (analyticsError) {
          console.error('Analytics tracking error:', analyticsError);
          // Don't fail the join if analytics fails
        }
      }

      return response;
    } catch (error) {
      console.error(`Sign up for multiple dates of event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Leave a specific date
  leaveDate: async (eventId: number, dateId: number) => {
    try {
      return await api.delete<{success: boolean, message: string, participants: number[]}>(
        `/event-dates/events/${eventId}/dates/${dateId}/signup`,
        undefined,
        withAuth()
      );
    } catch (error) {
      console.error(`Leave date ${dateId} of event ${eventId} service error:`, error);
      throw error;
    }
  }
};

// Event service
export const eventService = {
  // Get all events
  getAllEvents: async () => {
    try {
      // No need to explicitly pass token, withAuth will handle it
      return await api.get<EventResponse[]>('/events', undefined, withAuth());
    } catch (error) {
      console.error('Get all events service error:', error);
      throw error;
    }
  },

  // Get event by ID
  getEventById: async (id: number) => {
    try {
      return await api.get<EventResponse>(`/events/${id}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get event by ID ${id} service error:`, error);
      throw error;
    }
  },

  // Create a new event
  createEvent: async (eventData: CreateEventRequest) => {
    try {
      const { displayName, id } = useAuthStore.getState().userData || {};

      // Set owner and owner_id if not provided
      const eventWithOwner = {
        ...eventData,
        owner: eventData.owner || displayName || 'Anonymous',
        owner_id: eventData.owner_id || id
      };

      return await api.post<EventResponse>('/events', eventWithOwner, withAuth());
    } catch (error) {
      console.error('Create event service error:', error);
      throw error;
    }
  },

  // Update an event
  updateEvent: async (id: number, eventData: Partial<CreateEventRequest>) => {
    try {
      return await api.put<EventResponse>(`/events/${id}`, eventData, withAuth());
    } catch (error) {
      console.error(`Update event ${id} service error:`, error);
      throw error;
    }
  },

  // Delete an event
  deleteEvent: async (id: number) => {
    try {
      return await api.delete<void>(`/events/${id}`, withAuth());
    } catch (error) {
      console.error(`Delete event ${id} service error:`, error);
      throw error;
    }
  },

  // Extract event information from a link
  extractEventFromLink: async (link: string) => {
    try {
      return await api.post<{
        name: string;
        date: string[];
        location: string;
        description: string;
        cost: number;
        image_url: string;
      }>('/extract-from-link', { link }, withAuth());
    } catch (error) {
      console.error('Extract event from link service error:', error);
      throw error;
    }
  }
};
