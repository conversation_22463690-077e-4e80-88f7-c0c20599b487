import { api } from './api';
import { useAuthStore } from '@/stores/auth';
import { mixpanelService } from './mixpanel';

// Types
export interface EventParticipant {
  user_id: number;
  status: 'going' | 'invited' | 'maybe' | 'declined';
  joined_at: string;
  user?: {
    id: number;
    display_name: string;
    first_name: string;
    last_name: string;
    email: string;
    profile_image_url?: string;
  };
}

// Helper function to add auth headers
const withAuth = (): Record<string, string> => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Event participant service
export const eventParticipantService = {
  // Get all participants for an event
  getEventParticipants: async (eventId: number) => {
    try {
      return await api.get<EventParticipant[]>(`/participants/events/${eventId}/participants`, undefined, withAuth());
    } catch (error) {
      console.error(`Get participants for event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Sign up for an event
  signUpForEvent: async (eventId: number, status: string = 'going') => {
    try {
      const response = await api.post<EventParticipant>(`/participants/events/${eventId}/signup`, { status }, withAuth());

      // Track join event in Mixpanel if successful
      if (response.data && status === 'going') {
        try {
          await mixpanelService.trackJoinEvent(eventId, 'Unknown Event'); // We don't have event name here
        } catch (analyticsError) {
          console.error('Analytics tracking error:', analyticsError);
          // Don't fail the join if analytics fails
        }
      }

      return response;
    } catch (error) {
      console.error(`Sign up for event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Update participation status
  updateParticipationStatus: async (eventId: number, status: string) => {
    try {
      return await api.put<EventParticipant>(`/participants/events/${eventId}/participation`, { status }, withAuth());
    } catch (error) {
      console.error(`Update participation status for event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Leave an event
  leaveEvent: async (eventId: number) => {
    try {
      return await api.delete<void>(`/participants/events/${eventId}/participation`, withAuth());
    } catch (error) {
      console.error(`Leave event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Invite a user to an event
  inviteToEvent: async (eventId: number, userId: number) => {
    try {
      return await api.post<EventParticipant>(`/participants/events/${eventId}/invite`, { userId }, withAuth());
    } catch (error) {
      console.error(`Invite user to event ${eventId} service error:`, error);
      throw error;
    }
  }
};
