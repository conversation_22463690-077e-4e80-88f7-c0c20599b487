import { api } from './api';
import { useAuthStore } from '@/stores/auth';
import { mixpanelService } from './mixpanel';

// Helper function to add auth headers
const withAuth = (): Record<string, string> => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Payment service
export const paymentService = {
  // Mark a user as having paid for an event
  markAsPaid: async (eventId: number, eventName?: string, amount?: number) => {
    try {
      const response = await api.post<{ success: boolean }>(`/events/${eventId}/mark-paid`, {}, withAuth());

      // Track pay ticket in Mixpanel if successful
      if (response.data && response.data.success) {
        try {
          await mixpanelService.trackPayTicket(
            eventId,
            eventName || 'Unknown Event',
            amount
          );
        } catch (analyticsError) {
          console.error('Analytics tracking error:', analyticsError);
          // Don't fail the payment if analytics fails
        }
      }

      return response;
    } catch (error) {
      console.error(`Mark as paid for event ${eventId} service error:`, error);
      throw error;
    }
  },

  // Check if a user has paid for an event
  checkPaymentStatus: async (eventId: number) => {
    try {
      return await api.get<{ hasPaid: boolean }>(`/events/${eventId}/payment-status`, undefined, withAuth());
    } catch (error) {
      console.error(`Check payment status for event ${eventId} service error:`, error);
      throw error;
    }
  }
};
