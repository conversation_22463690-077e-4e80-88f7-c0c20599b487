// Import only what we need

import { api, withAuth } from './api';

// S3 URL signing service
export const cloudfrontService = {
  /**
   * Checks if a URL is an S3 URL
   * @param url - The URL to check
   * @returns True if the URL is an S3 URL, false otherwise
   */
  isS3Url: (url: string): boolean => {
    if (!url) return false;

    // Check if the URL is an S3 URL
    if (url.includes('s3.') && url.includes('amazonaws.com')) {
      return true;
    }

    // Check for specific bucket names used in the app
    if (url.includes('signupsheet-dev-files') ||
        url.includes('signupsheet-prod-files') ||
        url.includes('signupsheet-staging-files')) {
      return true;
    }

    return false;
  },

  /**
   * Gets a signed URL for an S3 object
   * @param url - The original S3 URL or object key
   * @param expiresIn - Expiration time in seconds (default: 1 hour)
   * @returns The signed URL
   */
  getSignedUrl: async (url: string, expiresIn: number = 3600): Promise<string> => {
    if (!url) return '';

    // Check if the URL already contains query parameters (indicating it's already signed)
    if (url.includes('?X-Amz-Algorithm=') || url.includes('&X-Amz-Algorithm=')) {
      return url;
    }

    // Check if the URL is an S3 URL
    const isS3Url = cloudfrontService.isS3Url(url);
    if (!isS3Url) {
      // If it's not an S3 URL, return the original URL without signing
      // console.log('Not an S3 URL, skipping signing:', url);
      return url;
    }

    try {
      // Extract the key from the URL if it's a full URL
      let key = url;

      // If it's a full URL, extract the key
      if (url.includes('http')) {
        // Example: https://bucket-name.s3.region.amazonaws.com/uploads/file.jpg
        const urlParts = url.split('/');

        // Find the bucket name part (contains s3.)
        const bucketIndex = urlParts.findIndex(part => part.includes('s3.'));

        if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {
          // Get everything after the bucket domain
          key = urlParts.slice(bucketIndex + 1).join('/');
          // console.log('Extracted key from S3 URL:', key);
        } else {
          // Try to find common folder names
          const folderKeywords = ['uploads', 'static', 'images', 'assets'];

          for (const keyword of folderKeywords) {
            const keywordIndex = urlParts.findIndex(part => part === keyword);
            if (keywordIndex !== -1) {
              // Get the path starting from the keyword
              key = urlParts.slice(keywordIndex).join('/');
              // console.log(`Extracted key using ${keyword} keyword:`, key);
              break;
            }
          }

          // Special case for default event image
          if (url.includes('/static/event-default.png')) {
            key = 'static/event-default.png';
            console.log('Using default event image key:', key);
          }
        }
      }

      // Call the API to get a signed URL
      // Define the expected response type
      interface SignedUrlResponse {
        signedUrl: string;
        key: string;
      }

      const response = await api.get<SignedUrlResponse>(`/uploads/signed-url?key=${encodeURIComponent(key)}&expiresIn=${expiresIn}`, withAuth());

      if (response.data && response.data.signedUrl) {
        return response.data.signedUrl;
      }

      console.error('Failed to get signed URL:', response);
      return url; // Return original URL on error
    } catch (error) {
      console.error('Error getting signed URL:', error);
      return url; // Return original URL on error
    }
  }
};
