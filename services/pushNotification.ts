import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { api } from './api';
import { useAuthStore } from '@/stores/auth';
import Constants from 'expo-constants';

// Configure how notifications appear when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Helper function to add auth headers
const withAuth = () => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Push notification service
export const pushNotificationService = {
  // Register for push notifications
  registerForPushNotifications: async (): Promise<string | null> => {
    try {
      // Check if this is a physical device (push notifications don't work in simulators)
      if (!Device.isDevice) {
        console.log('Push notifications are not available on emulators/simulators');
        return null;
      }

      // Check if we have permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // If we don't have permission, ask for it
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // If we still don't have permission, return
      if (finalStatus !== 'granted') {
        console.log('Failed to get push notification permissions');
        return null;
      }

      // Get the token
      const expoPushToken = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      // Configure device-specific settings
      if (Platform.OS === 'android') {
        Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      return expoPushToken.data;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return null;
    }
  },

  // Register the push token with the server
  registerPushToken: async (token: string): Promise<boolean> => {
    try {
      const userData = useAuthStore.getState().userData;
      if (!userData || !userData.id) {
        console.log('User not logged in, cannot register push token');
        return false;
      }

      // Get a unique device ID
      const deviceId = Device.deviceName || Device.modelName || 'unknown-device';
      
      // Determine device type
      const deviceType = Platform.OS === 'ios' ? 'ios' : 'android';

      // Register the token with the server
      const response = await api.post(
        '/push-tokens/register',
        {
          token,
          deviceId,
          deviceType
        },
        withAuth()
      );

      return response.status === 200 || response.status === 201;
    } catch (error) {
      console.error('Error registering push token with server:', error);
      return false;
    }
  },

  // Deactivate the push token on the server
  deactivatePushToken: async (): Promise<boolean> => {
    try {
      const userData = useAuthStore.getState().userData;
      if (!userData || !userData.id) {
        console.log('User not logged in, cannot deactivate push token');
        return false;
      }

      // Get a unique device ID
      const deviceId = Device.deviceName || Device.modelName || 'unknown-device';

      // Deactivate the token on the server
      const response = await api.post(
        '/push-tokens/deactivate',
        {
          deviceId
        },
        withAuth()
      );

      return response.status === 200;
    } catch (error) {
      console.error('Error deactivating push token on server:', error);
      return false;
    }
  },

  // Set up notification listeners
  setupNotificationListeners: () => {
    // Handle notifications received while the app is in the foreground
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received in foreground:', notification);
    });

    // Handle notifications that are tapped by the user
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response received:', response);
      
      // Here you can navigate to a specific screen based on the notification data
      // For example:
      // const data = response.notification.request.content.data;
      // if (data.screen) {
      //   navigation.navigate(data.screen, data.params);
      // }
    });

    // Return a cleanup function
    return () => {
      Notifications.removeNotificationSubscription(foregroundSubscription);
      Notifications.removeNotificationSubscription(responseSubscription);
    };
  }
};

export default pushNotificationService;
