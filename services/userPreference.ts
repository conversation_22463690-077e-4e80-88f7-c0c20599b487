import { api } from './api';
import { useAuthStore } from '@/stores/auth';

// UserPreference interfaces
export interface UserPreferenceRequest {
  user_id?: number;
  notifications_enabled?: boolean;
  language?: string;
  push_notifications_enabled?: boolean;
  notify_event_cancellation?: boolean;
  notify_event_changes?: boolean;
  notify_contact_questions?: boolean;
  notify_top_tier_events?: boolean;
  email_notifications_enabled?: boolean;
  participant_reminders_enabled?: boolean;
  notify_dues_owed?: boolean;
  notify_tickets_not_purchased?: boolean;
  venmo_enabled?: boolean;
  paypal_enabled?: boolean;
  venmo_username?: string;
  paypal_username?: string;
}

export interface UserPreferenceResponse {
  id: number;
  user_id: number;
  notifications_enabled: boolean;
  language: string;
  push_notifications_enabled: boolean;
  notify_event_cancellation: boolean;
  notify_event_changes: boolean;
  notify_contact_questions: boolean;
  notify_top_tier_events: boolean;
  email_notifications_enabled: boolean;
  participant_reminders_enabled: boolean;
  notify_dues_owed: boolean;
  notify_tickets_not_purchased: boolean;
  venmo_enabled: boolean;
  paypal_enabled: boolean;
  venmo_username?: string;
  paypal_username?: string;
  created_at: string;
  updated_at: string;
}

// Helper function to add auth headers
const withAuth = () => {
  const token = useAuthStore.getState().userData?.token;
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// UserPreference service
export const userPreferenceService = {
  // Get all user preferences
  getAllUserPreferences: async () => {
    try {
      return await api.get<UserPreferenceResponse[]>('/user-preferences', undefined, withAuth());
    } catch (error) {
      console.error('Get all user preferences service error:', error);
      throw error;
    }
  },

  // Get user preference by ID
  getUserPreferenceById: async (id: number) => {
    try {
      return await api.get<UserPreferenceResponse>(`/user-preferences/${id}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get user preference by ID ${id} service error:`, error);
      throw error;
    }
  },

  // Get user preference by user ID
  getUserPreferenceByUserId: async (userId: number) => {
    try {
      return await api.get<UserPreferenceResponse>(`/user-preferences/user/${userId}`, undefined, withAuth());
    } catch (error) {
      console.error(`Get user preference by user ID ${userId} service error:`, error);
      throw error;
    }
  },

  // Create a new user preference
  createUserPreference: async (preferenceData: UserPreferenceRequest) => {
    try {
      const userData = useAuthStore.getState().userData;
      const preferenceWithUserId = {
        ...preferenceData,
        user_id: preferenceData.user_id || userData?.id
      };

      return await api.post<UserPreferenceResponse>('/user-preferences', preferenceWithUserId, withAuth());
    } catch (error) {
      console.error('Create user preference service error:', error);
      throw error;
    }
  },

  // Update a user preference
  updateUserPreference: async (id: number, preferenceData: UserPreferenceRequest) => {
    try {
      return await api.put<UserPreferenceResponse>(`/user-preferences/${id}`, preferenceData, withAuth());
    } catch (error) {
      console.error(`Update user preference ${id} service error:`, error);
      throw error;
    }
  },

  // Get or create user preference for current user
  getOrCreateForCurrentUser: async () => {
    try {
      const userData = useAuthStore.getState().userData;
      if (!userData?.id) {
        throw new Error('No user ID found in auth store');
      }

      // Try to get existing preference
      const response = await userPreferenceService.getUserPreferenceByUserId(userData.id);

      // If preference exists, return it
      if (response.data) {
        return response;
      }

      // If preference doesn't exist, create a new one with default values
      return await userPreferenceService.createUserPreference({
        user_id: userData.id,
        notifications_enabled: true,
        language: 'en',
        push_notifications_enabled: true,
        notify_event_cancellation: true,
        notify_event_changes: false,
        notify_contact_questions: true,
        notify_top_tier_events: true,
        email_notifications_enabled: true,
        participant_reminders_enabled: true,
        notify_dues_owed: true,
        notify_tickets_not_purchased: false,
        venmo_enabled: true,
        paypal_enabled: false
      });
    } catch (error) {
      console.error('Get or create user preference service error:', error);
      throw error;
    }
  }
};
