{"expo": {"name": "QWRM", "slug": "qwrm", "version": "1.0.0", "owner": "blood-and-treasure", "orientation": "portrait", "icon": "./assets/app/icon_1024x1024.png", "scheme": "qwrm", "userInterfaceStyle": "automatic", "newArchEnabled": true, "jsEngine": "jsc", "splash": {"backgroundColor": "#202326"}, "ios": {"buildNumber": "30", "supportsTablet": true, "bundleIdentifier": "com.signupsheet", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIBackgroundModes": ["remote-notification"], "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to allow you to upload event and profile images."}}, "android": {"versionCode": 8, "adaptiveIcon": {"foregroundImage": "./assets/app/adaptive-icon.png", "backgroundColor": "#00788D"}, "package": "com.signupsheet", "googleServicesFile": "./google-services.json", "permissions": ["NOTIFICATIONS", "VIBRATE", "RECEIVE_BOOT_COMPLETED"], "statusBar": {"backgroundColor": "#202326", "barStyle": "light-content", "translucent": false}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/app/favicon.png"}, "plugins": ["expo-router", "expo-asset", ["expo-image-picker", {"photosPermission": "This app needs access to your photo library to allow you to upload event and profile images."}], ["expo-notifications", {"icon": "./assets/app/notification-icon.png", "color": "#00788D"}], ["expo-font", {"fonts": ["./assets/fonts/WorkSans-Regular.ttf", "./assets/fonts/WorkSans-Light.ttf", "./assets/fonts/WorkSans-Medium.ttf", "./assets/fonts/WorkSans-SemiBold.ttf", "./assets/fonts/WorkSans-Bold.ttf", "./assets/fonts/WorkSans-ExtraBold.ttf"], "android": {"fonts": ["./assets/fonts/WorkSans-Regular.ttf", "./assets/fonts/WorkSans-Light.ttf", "./assets/fonts/WorkSans-Medium.ttf", "./assets/fonts/WorkSans-SemiBold.ttf", "./assets/fonts/WorkSans-Bold.ttf", "./assets/fonts/WorkSans-ExtraBold.ttf"]}, "ios": {"fonts": ["./assets/fonts/WorkSans-Regular.ttf", "./assets/fonts/WorkSans-Light.ttf", "./assets/fonts/WorkSans-Medium.ttf", "./assets/fonts/WorkSans-SemiBold.ttf", "./assets/fonts/WorkSans-Bold.ttf", "./assets/fonts/WorkSans-ExtraBold.ttf"]}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "50eb4be8-5d7c-45b2-937c-54d14dc96076"}}, "build": {"production": {"credentialsSource": "local", "android": {"buildType": "apk"}}}}}