/**
 * Learn more about Light and Dark modes:
 * https://docs.expo.io/guides/color-schemes/
 */

import { Text as DefaultText, View as DefaultView, TextInput as DefaultTextInput } from 'react-native';

import Colors from '@/constants/Colors';
import { useColorScheme } from './useColorScheme';

type ThemeProps = {
  lightColor?: string;
  darkColor?: string;
};

export type TextProps = ThemeProps & DefaultText['props'];
export type ViewProps = ThemeProps & DefaultView['props'];
export type TextInputProps = ThemeProps & DefaultTextInput['props'];

export function useThemeColor(
  props: { light?: string; dark?: string },
  colorName: keyof typeof Colors.light & keyof typeof Colors.dark
) {
  const theme = useColorScheme() ?? 'light';
  const colorFromProps = props[theme];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    return Colors[theme][colorName];
  }
}

export function Text(props: TextProps) {
  const { style, lightColor, darkColor, ...otherProps } = props;
  // const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  // Extract fontWeight from style if it exists
  let fontFamily = 'WorkSans_400Regular';

  const styleObj = Array.isArray(style)
  ? Object.assign({}, ...style.filter(Boolean))
  : typeof style === 'object' ? style : {};

  // If style contains fontWeight, map it to the appropriate font family
  if (style) {
    if (styleObj.fontWeight) {
      // Map fontWeight to the appropriate font family
      const weightString = styleObj.fontWeight.toString();
      switch (weightString) {
        case '300':
        case 'light':
          fontFamily = 'WorkSans_300Light';
          break;
        case '400':
        case 'normal':
          fontFamily = 'WorkSans_400Regular';
          break;
        case '500':
        case 'medium':
          fontFamily = 'WorkSans_500Medium';
          break;
        case '600':
        case 'semibold':
          fontFamily = 'WorkSans_600SemiBold';
          break;
        case '700':
        case 'bold':
          fontFamily = 'WorkSans_700Bold';
          break;
        case '800':
        case 'extrabold':
          fontFamily = 'WorkSans_800ExtraBold';
          break;
        default:
          fontFamily = 'WorkSans_400Regular';
      }
    } else if (styleObj.fontFamily) {
      // If fontFamily is already specified, use that
      fontFamily = styleObj.fontFamily;
    }
  }

  // Create a base style with the font family and color
  const baseStyle = { fontFamily };

  return <DefaultText
    style={[ { ...styleObj, fontWeight: undefined }, baseStyle ]}
    {...otherProps}
  />;
}

export function TextInput(props: TextInputProps) {
  const { style, lightColor, darkColor, placeholderTextColor, ...otherProps } = props;

  // Always use WorkSans_400Regular for TextInput
  const fontFamily = 'WorkSans_400Regular';

  const styleObj = Array.isArray(style)
    ? Object.assign({}, ...style.filter(Boolean))
    : typeof style === 'object' ? style : {};

  // Create a base style with the font family
  const baseStyle = { fontFamily };

  return <DefaultTextInput
    style={[{ ...styleObj, fontWeight: undefined }, baseStyle]}
    placeholderTextColor={placeholderTextColor || '#CACACA'}
    {...otherProps}
  />;
}

export function View(props: ViewProps) {
  const { style, lightColor, darkColor, ...otherProps } = props;
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');

  return <DefaultView style={[{ backgroundColor }, style]} {...otherProps} />;
}
