import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Platform, Modal, Pressable } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { Calendar, Clock, HelpCircle } from 'lucide-react-native';
import Checkbox from '@/components/ui/checkbox';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';
import Colors from '@/constants/Colors';
import { formatDateToLocal } from '@/utils/dateHelpers';

interface DateTimeEntry {
  date: string;
  time: string;
  isoDate?: string; // ISO date string for API
  isoTime?: string; // ISO time string for API
  combinedIsoDateTime?: string; // Combined ISO date and time string for API
}

interface WhenStepProps {
  date: string;
  time: string;
  isFlexible: boolean;
  isMultiple: boolean;
  onDateChange: (value: string) => void;
  onTimeChange: (value: string) => void;
  onFlexibleChange: (value: boolean) => void;
  onMultipleChange: (value: boolean) => void;
  dates?: DateTimeEntry[];
  onDatesChange?: (dates: DateTimeEntry[]) => void;
  quorumMet?: boolean;
}

export const WhenStep = forwardRef<{ validate: () => boolean }, WhenStepProps>((
  {
    date,
    time,
    isFlexible,
    isMultiple,
    onDateChange,
    onTimeChange,
    onFlexibleChange,
    onMultipleChange,
    dates = [{date, time}],
    onDatesChange,
    quorumMet = false,
  }: WhenStepProps,
  ref
) => {
  // State for managing multiple dates
  const [localDates, setLocalDates] = useState<DateTimeEntry[]>(dates);
  const [activeDateIndex, setActiveDateIndex] = useState<number>(0);

  // State for date picker
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  // State for time picker
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedTime, setSelectedTime] = useState<Date>(() => {
    const now = new Date();
    now.setMinutes(0, 0, 0); // Set minutes, seconds, and milliseconds to 0 for full hour
    return now;
  });

  // State for tooltips
  const [activeTooltip, setActiveTooltip] = useState<'flexible' | 'multiple' | null>(null);

  // Add error state
  const [error, setError] = useState<string | null>(null);

  const closeTooltip = () => setActiveTooltip(null);

  // Helper function to combine date and time into a single ISO string
  const combineDateTime = (isoDateStr: string, isoTimeStr: string): string => {
    try {
      // Parse the date and time
      const dateObj = new Date(isoDateStr);
      const timeObj = new Date(isoTimeStr);

      // Create a new date with the date from dateObj and time from timeObj
      const combined = new Date(dateObj);
      combined.setHours(timeObj.getHours());
      combined.setMinutes(timeObj.getMinutes());
      combined.setSeconds(timeObj.getSeconds());
      combined.setMilliseconds(timeObj.getMilliseconds());

      return combined.toISOString();
    } catch (error) {
      console.error('Error combining date and time:', error);
      return isoDateStr; // Return the date as fallback
    }
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    try {
      // Handle ISO date format (YYYY-MM-DD)
      if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        const parsedDate = new Date(dateString + 'T00:00:00');
        if (isNaN(parsedDate.getTime())) return dateString;

        return parsedDate.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric'
        });
      }

      // Try to parse other date formats
      const parsedDate = new Date(dateString);
      if (isNaN(parsedDate.getTime())) return dateString; // Return original if invalid

      return parsedDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (error) {
      return dateString; // Return original on error
    }
  };

  // Format time for display
  const formatTime = (timeString: string): string => {
    if (!timeString) return '';
    try {
      // Handle HH:MM format (24-hour)
      if (timeString.match(/^\d{2}:\d{2}$/)) {
        const [hours, minutes] = timeString.split(':').map(Number);
        const today = new Date();
        today.setHours(hours, minutes, 0, 0);

        return today.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      }

      // Handle legacy format "3:30 PM"
      const [timePart, ampm] = timeString.split(' ');
      if (timePart && ampm) {
        const [hours, minutes] = timePart.split(':').map(Number);
        let hour = hours;
        if (ampm.toLowerCase() === 'pm' && hour < 12) hour += 12;
        if (ampm.toLowerCase() === 'am' && hour === 12) hour = 0;

        const today = new Date();
        today.setHours(hour, minutes || 0, 0);
        return today.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      }

      return timeString; // Return original if format doesn't match
    } catch (error) {
      return timeString; // Return original on error
    }
  };

  // Update local dates and propagate changes to parent
  const updateDates = (newDates: DateTimeEntry[]) => {
    setLocalDates(newDates);
    if (onDatesChange) {
      // Only pass non-empty dates to parent
      const filteredDates = newDates.filter(entry => entry.date !== '' || entry.time !== '');
      onDatesChange(filteredDates);
    }
    // For backward compatibility
    if (newDates[0]) {
      if (newDates[0].date !== date) onDateChange(newDates[0].date);
      if (newDates[0].time !== time) onTimeChange(newDates[0].time);
    }
  };

  // Handle date change for a specific index
  const handleDateChange = (_event: any, selectedDate?: Date, index: number = activeDateIndex) => {
    if (selectedDate) {
      const currentDate = selectedDate;
      setShowDatePicker(Platform.OS === 'ios'); // Only hide on Android
      setSelectedDate(currentDate);

      // For Android, immediately update the date since Android picker closes after selection
      if (Platform.OS === 'android') {
        // Create ISO date string (YYYY-MM-DD format) using local date to avoid timezone issues
        const isoDateString = formatDateToLocal(currentDate);

        // Update the date at the specified index
        const newDates = [...localDates];
        if (!newDates[index]) newDates[index] = { date: '', time: '' };
        newDates[index].date = isoDateString;
        newDates[index].isoDate = currentDate.toISOString();

        // If we already have a time for this entry, combine date and time
        if (newDates[index].time && newDates[index].isoTime) {
          newDates[index].combinedIsoDateTime = combineDateTime(newDates[index].isoDate, newDates[index].isoTime);
        }

        updateDates(newDates);
      }
      // For iOS, only update selectedDate state - actual date will be committed when "Done" is pressed
    } else {
      setShowDatePicker(false);
    }
  };

  // Handle committing the date change (for iOS "Done" button)
  const commitDateChange = (index: number = activeDateIndex) => {
    // Create ISO date string (YYYY-MM-DD format) using local date to avoid timezone issues
    const isoDateString = formatDateToLocal(selectedDate);

    // Update the date at the specified index
    const newDates = [...localDates];
    if (!newDates[index]) newDates[index] = { date: '', time: '' };
    newDates[index].date = isoDateString;
    newDates[index].isoDate = selectedDate.toISOString();

    // If we already have a time for this entry, combine date and time
    if (newDates[index].time && newDates[index].isoTime) {
      newDates[index].combinedIsoDateTime = combineDateTime(newDates[index].isoDate, newDates[index].isoTime);
    }

    updateDates(newDates);
  };

  // Handle time change for a specific index
  const handleTimeChange = (_event: any, selectedTime?: Date, index: number = activeDateIndex) => {
    if (selectedTime) {
      const currentTime = selectedTime;
      setShowTimePicker(Platform.OS === 'ios'); // Only hide on Android
      setSelectedTime(currentTime);

      // For Android, immediately update the time since Android picker closes after selection
      if (Platform.OS === 'android') {
        // Create time string in HH:MM format for consistent storage
        const timeString = currentTime.toTimeString().slice(0, 5); // Gets "HH:MM"

        // Update the time at the specified index
        const newDates = [...localDates];
        if (!newDates[index]) newDates[index] = { date: '', time: '' };
        newDates[index].time = timeString;
        newDates[index].isoTime = currentTime.toISOString();

        // If we already have a date for this entry, combine date and time
        if (newDates[index].date && newDates[index].isoDate) {
          newDates[index].combinedIsoDateTime = combineDateTime(newDates[index].isoDate, newDates[index].isoTime);
          console.log(`Combined date and time: ${newDates[index].combinedIsoDateTime}`);
        }

        updateDates(newDates);
      }
      // For iOS, only update selectedTime state - actual time will be committed when "Done" is pressed
    } else {
      setShowTimePicker(false);
    }
  };

  // Handle committing the time change (for iOS "Done" button)
  const commitTimeChange = (index: number = activeDateIndex) => {
    // Create time string in HH:MM format for consistent storage
    const timeString = selectedTime.toTimeString().slice(0, 5); // Gets "HH:MM"

    // Update the time at the specified index
    const newDates = [...localDates];
    if (!newDates[index]) newDates[index] = { date: '', time: '' };
    newDates[index].time = timeString;
    newDates[index].isoTime = selectedTime.toISOString();

    // If we already have a date for this entry, combine date and time
    if (newDates[index].date && newDates[index].isoDate) {
      newDates[index].combinedIsoDateTime = combineDateTime(newDates[index].isoDate, newDates[index].isoTime);
      console.log(`Combined date and time: ${newDates[index].combinedIsoDateTime}`);
    }

    updateDates(newDates);
  };

  // Check if we should show a new empty date entry or remove extra entries
  useEffect(() => {
    if (isFlexible) { // Only Flexible dates checkbox should add more date inputs
      if (localDates.length > 0) {
        // Check if the last entry has data and we need to add a new empty entry
        const lastEntry = localDates[localDates.length - 1];
        if (lastEntry && (lastEntry.date || lastEntry.time)) {
          // Add a new empty entry
          const newDates = [...localDates, { date: '', time: '' }];
          updateDates(newDates);
        }
      }
    } else {
      // If flexible dates is not checked, keep only the first date entry
      if (localDates.length > 1) {
        updateDates([localDates[0]]);
      }
    }
  }, [isFlexible, localDates]);

  // Open date picker using the Android imperative API
  const showAndroidDatePicker = (index: number = activeDateIndex) => {
    setActiveDateIndex(index);

    // Initialize selectedDate with current date from the form, or today if none
    const currentDateEntry = localDates[index];
    let initialDate = new Date();

    if (currentDateEntry && currentDateEntry.date) {
      try {
        // Parse the existing date
        if (currentDateEntry.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
          initialDate = new Date(currentDateEntry.date + 'T00:00:00');
        } else {
          initialDate = new Date(currentDateEntry.date);
        }

        // Fallback to today if parsing failed
        if (isNaN(initialDate.getTime())) {
          initialDate = new Date();
        }
      } catch (error) {
        initialDate = new Date();
      }
    }

    setSelectedDate(initialDate);

    if (Platform.OS === 'android') {
      DateTimePickerAndroid.open({
        value: initialDate,
        onChange: (event, date) => handleDateChange(event, date, index),
        mode: 'date',
      });
    } else {
      setShowDatePicker(true);
    }
  };

  // Open time picker using the Android imperative API
  const showAndroidTimePicker = (index: number = activeDateIndex) => {
    setActiveDateIndex(index);

    // Initialize selectedTime with current time from the form, or default time if none
    const currentDateEntry = localDates[index];
    let initialTime = new Date();
    initialTime.setMinutes(0, 0, 0); // Default to full hour

    if (currentDateEntry && currentDateEntry.time) {
      try {
        // Parse the existing time (HH:MM format)
        if (currentDateEntry.time.match(/^\d{2}:\d{2}$/)) {
          const [hours, minutes] = currentDateEntry.time.split(':').map(Number);
          initialTime = new Date();
          initialTime.setHours(hours, minutes, 0, 0);
        }
      } catch (error) {
        // Keep default time if parsing failed
      }
    }

    setSelectedTime(initialTime);

    if (Platform.OS === 'android') {
      DateTimePickerAndroid.open({
        value: initialTime,
        onChange: (event, time) => handleTimeChange(event, time, index),
        mode: 'time',
        is24Hour: false,
      });
    } else {
      setShowTimePicker(true);
    }
  };

  // Validate if at least one date is selected
  const hasValidDate = () => {
    return localDates.some(entry => entry.date !== '');
  };

  // Filter out empty dates when returning dates to parent
  const getFilteredDates = () => {
    // Filter out dates where both date and time are empty
    const filtered = localDates.filter(entry => entry.date !== '' || entry.time !== '');
    return filtered;
  };

  // Clear error when dates change
  useEffect(() => {
    if (hasValidDate()) {
      setError(null);
    }
  }, [localDates]);

  // Export validation function for parent component
  useImperativeHandle(ref, () => ({
    validate: () => {
      if (!hasValidDate()) {
        setError('Please select at least one date');
        return false;
      }

      // Make sure we're only passing non-empty dates to parent
      if (onDatesChange) {
        const filteredDates = getFilteredDates();
        console.log('Filtered dates on validation:', filteredDates);
        onDatesChange(filteredDates);
      }

      return true;
    }
  }));

  return (
    <View>
      <Text style={styles.title}>WHEN</Text>

      {/* Show quorum met message if editing is disabled */}
      {quorumMet && (
        <View style={styles.quorumMetContainer}>
          <Text style={styles.quorumMetText}>
            The event date and time cannot be changed because quorum has been reached.
          </Text>
        </View>
      )}

      {/* Show error message if exists */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      <View style={styles.form}>
        {localDates.map((dateEntry, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <Text style={styles.orDivider}>OR</Text>
            )}
            <View style={styles.dateTimeInputs}>
              <TouchableOpacity
                style={[styles.inputWrapper, quorumMet && styles.disabledInputWrapper]}
                onPress={quorumMet ? undefined : () => showAndroidDatePicker(index)}
                disabled={quorumMet}
              >
                <Calendar style={styles.inputIcon} size={20} />
                <TextInput
                  value={formatDate(dateEntry.date)}
                  editable={false}
                  style={[styles.dateTimeInput, quorumMet && styles.disabledInput]}
                  placeholder="Select date"
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.inputWrapper, quorumMet && styles.disabledInputWrapper]}
                onPress={quorumMet ? undefined : () => showAndroidTimePicker(index)}
                disabled={quorumMet}
              >
                <Clock style={styles.inputIcon} size={20} color={quorumMet ? "#999" : Colors.primary} />
                <TextInput
                  value={formatTime(dateEntry.time)}
                  editable={false}
                  style={[styles.dateTimeInput, quorumMet && styles.disabledInput]}
                  placeholder="Select time"
                />
              </TouchableOpacity>
            </View>
          </React.Fragment>
        ))}

        {/* No ADD DATE button - new date inputs appear automatically */}

        {/* Date Picker - iOS only */}
        {Platform.OS === 'ios' && showDatePicker && (
          <Modal
            animationType="slide"
            transparent={true}
            visible={showDatePicker}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                    <Text style={styles.modalCancel}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => {
                    commitDateChange();
                    setShowDatePicker(false);
                  }}>
                    <Text style={styles.modalDone}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  value={selectedDate}
                  mode="date"
                  display="inline"
                  onChange={(_event, date) => {
                    if (date) {
                      setSelectedDate(date);
                    }
                  }}
                  style={[styles.datePicker, { alignSelf: 'center' }]}
                />
              </View>
            </View>
          </Modal>
        )}

        {/* Time Picker - iOS only */}
        {Platform.OS === 'ios' && showTimePicker && (
          <Modal
            animationType="slide"
            transparent={true}
            visible={showTimePicker}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <TouchableOpacity onPress={() => setShowTimePicker(false)}>
                    <Text style={styles.modalCancel}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => {
                    commitTimeChange();
                    setShowTimePicker(false);
                  }}>
                    <Text style={styles.modalDone}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  value={selectedTime}
                  mode="time"
                  display="spinner"
                  onChange={(_event, time) => {
                    if (time) {
                      setSelectedTime(time);
                    }
                  }}
                  style={styles.datePicker}
                />
              </View>
            </View>
          </Modal>
        )}
        <View style={[styles.checkboxGroup, quorumMet && styles.disabledInputWrapper]}>
          <TouchableOpacity
            style={styles.checkboxWrapper}
            onPress={quorumMet ? undefined : () => onFlexibleChange(!isFlexible)}
            disabled={quorumMet}
          >
            <View style={styles.checkboxLabel}>
              <Checkbox
                checked={isFlexible}
                onCheckedChange={quorumMet ? () => {} : onFlexibleChange}
                style={styles.checkbox}
              />
              <Text style={[styles.checkboxText, quorumMet && styles.disabledInput]}>Flexible dates</Text>
              <TouchableOpacity
                onPress={quorumMet ? undefined : (e) => {
                  e.stopPropagation();
                  setActiveTooltip(activeTooltip === 'flexible' ? null : 'flexible');
                }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                disabled={quorumMet}
              >
                <HelpCircle style={styles.helpIcon} size={16} color={quorumMet ? "#999" : Colors.primary} />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.checkboxWrapper}
            onPress={quorumMet ? undefined : () => onMultipleChange(!isMultiple)}
            disabled={quorumMet}
          >
            <View style={styles.checkboxLabel}>
              <Checkbox
                checked={isMultiple}
                onCheckedChange={quorumMet ? () => {} : onMultipleChange}
                style={styles.checkbox}
              />
              <Text style={[styles.checkboxText, quorumMet && styles.disabledInput]}>Multiple dates</Text>
              <TouchableOpacity
                onPress={quorumMet ? undefined : (e) => {
                  e.stopPropagation();
                  setActiveTooltip(activeTooltip === 'multiple' ? null : 'multiple');
                }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                disabled={quorumMet}
              >
                <HelpCircle style={styles.helpIcon} size={16} color={quorumMet ? "#999" : Colors.primary} />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
        <Modal
          visible={activeTooltip !== null}
          transparent={true}
          animationType="fade"
          onRequestClose={closeTooltip}
        >
          <Pressable
            style={styles.modalOverlay}
            onPress={closeTooltip}
          >
            <View
              style={[
                styles.tooltip,
                activeTooltip === 'flexible' && styles.tooltipFlexible,
                activeTooltip === 'multiple' && styles.tooltipMultiple
              ]}
            >
              <Text style={styles.tooltipText}>
                {activeTooltip === 'flexible'
                  ? "Allow your friends to pick between a selection of dates"
                  : "Allow your friends to select multiple dates for a recurring event"
                }
              </Text>
            </View>
          </Pressable>
        </Modal>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 40,
    marginLeft: 20,
  },
  form: {
    flexDirection: 'column',
    paddingHorizontal: 20,
    gap: 10, // Reduced from 32 to 20 to make the form more compact
  },
  dateTimeInputs: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 2, // Further reduced to just 2 pixels
  },
  inputWrapper: {
    flex: 3,
    position: 'relative',
    height: 40, // Reduced from 48 to 40 for more compact layout
    justifyContent: 'center', // Center children vertically
  },
  inputIcon: {
    position: 'absolute',
    left: 0,
    zIndex: 1,
    // Remove top property to allow icon to be centered
  },
  dateTimeInput: {
    width: '100%',
    height: 40,
    paddingVertical: 6,
    paddingLeft: 28,
    paddingRight: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    fontSize: 16,
    color: Colors.white,
    backgroundColor: 'transparent',
  },
  orDivider: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.white,
    textAlign: 'center',
    marginVertical: 2, // Further reduced spacing to just 2 pixels
  },
  addDateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 4,
    padding: 12,
    marginTop: 16,
    marginBottom: 24,
  },
  addDateButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  checkboxGroup: {
    flexDirection: 'column',
    gap: 12,
    marginTop: 20,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkboxLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkboxText: {
    fontSize: 16,
    color: Colors.white,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
  },
  helpIcon: {
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  modalCancel: {
    color: Colors.black,
    fontSize: 16,
  },
  modalDone: {
    color: Colors.hyperBlue,
    fontSize: 16,
    fontWeight: 'bold',
  },
  datePicker: {
    height: 200,
    width: '100%',
  },
  tooltip: {
    position: 'absolute',
    backgroundColor: '#333',
    padding: 10,
    borderRadius: 6,
    width: 200,
    zIndex: 1000,
  },
  tooltipFlexible: {
    top: '30%', // Adjust these values based on your layout
    right: 30,
  },
  tooltipMultiple: {
    top: '40%', // Adjust these values based on your layout
    right: 30,
  },
  tooltipText: {
    color: 'white',
    fontSize: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  errorText: {
    color: '#FF4444',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 10,
    marginTop: -5,
  },
  quorumMetContainer: {
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  quorumMetText: {
    color: '#856404',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  disabledInputWrapper: {
    opacity: 0.5,
  },
  disabledInput: {
    color: '#999',
  },

});
