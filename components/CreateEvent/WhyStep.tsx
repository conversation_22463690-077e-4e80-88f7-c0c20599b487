import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';
import Colors from '@/constants/Colors';

interface WhyStepProps {
  description: string;
  cutoffTime: string;
  onDescriptionChange: (value: string) => void;
  onCutoffTimeChange: (value: string) => void;
}

export const WhyStep: React.FC<WhyStepProps> = ({
  description,
  cutoffTime,
  onDescriptionChange,
  onCutoffTimeChange,
}) => {
  return (
    <View>
      <Text style={styles.title}>WHY</Text>
      <View style={styles.form}>
        <View style={styles.whySection}>
          <Text style={styles.sectionTitle}>GET PEOPLE EXCITED (OPTIONAL)</Text>
          <TextInput
            value={description}
            onChangeText={onDescriptionChange}
            style={styles.textarea}
            placeholder="Add a description or more notes"
            multiline
          />
        </View>

        <View style={styles.whySection}>
          <Text style={styles.sectionTitle}>RESPONSE CUTOFF</Text>
          <View style={styles.cutoffContainer}>
            <Text style={styles.cutoffText}>People can sign up until</Text>
            <Select value={cutoffTime} onValueChange={onCutoffTimeChange}>
              <SelectTrigger style={styles.cutoffSelect}>
                <Text style={styles.selectValue}>
                  {cutoffTime ? `${cutoffTime} ${parseInt(cutoffTime) === 1 ? 'hour' : 'hours'}` : 'Select time'}
                </Text>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 hour</SelectItem>
                <SelectItem value="2">2 hours</SelectItem>
                <SelectItem value="4">4 hours</SelectItem>
                <SelectItem value="8">8 hours</SelectItem>
                <SelectItem value="12">12 hours</SelectItem>
                <SelectItem value="24">24 hours</SelectItem>
                <SelectItem value="48">48 hours</SelectItem>
              </SelectContent>
            </Select>
            <Text style={styles.cutoffText}>before the event starts.</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 40,
    marginLeft: 20,
  },
  form: {
    flexDirection: 'column',
    gap: 32,
    paddingHorizontal: 20,
  },
  whySection: {
    flexDirection: 'column',
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '500',
    marginBottom: 16,
  },
  textarea: {
    width: '100%',
    minHeight: 24,
    paddingVertical: 4,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    backgroundColor: 'transparent',
    fontSize: 16,
    color: Colors.white,
    fontFamily: 'System',
  },
  cutoffContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  cutoffText: {
    fontSize: 16,
    color: Colors.white,
    marginVertical: 0,
  },
  cutoffSelect: {
    width: 120,
    height: 32,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 4,
    backgroundColor: Colors.background,
    color: Colors.white,
    fontSize: 16,
    paddingHorizontal: 12,
    marginLeft: 8,
    paddingTop: Platform.OS === 'android' ? 3 : 6
  },
  selectValue: {
    flex: 1,
    fontSize: 16,
    color: Colors.white,
  },
});