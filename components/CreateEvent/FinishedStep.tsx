import React, { useState, useEffect } from 'react';
import { View, Image, StyleSheet, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { Text } from "@/components/Themed";
import { Button } from '@/components/ui/button';
import { Calendar, MapPin, Pencil } from 'lucide-react-native';
import { cloudfrontService } from '@/services/cloudfront';

interface FinishedStepProps {
  eventName: string;
  minPeople: string;
  maxPeople: string;
  date: string;
  time: string;
  locationName: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zip: string;
  amount: string;
  costDescription: string;
  description: string;
  imageUri?: string;
  onEdit: () => void;
  onCancel: () => void;
}

export const FinishedStep: React.FC<FinishedStepProps> = ({
  onEdit,
  onCancel,
  imageUri,
  description,
  eventName,
  minPeople,
  maxPeople,
  amount,
  costDescription,
}) => {
  // State for the signed image URL
  const [signedImageUri, setSignedImageUri] = useState<string | undefined>(undefined);

  // Get a signed URL for the image when the component mounts or imageUri changes
  useEffect(() => {
    const getSignedUrl = async () => {
      if (imageUri) {
        try {
          const signedUrl = await cloudfrontService.getSignedUrl(imageUri);
          setSignedImageUri(signedUrl);
        } catch (error) {
          console.error('Error getting signed URL for finished step:', error);
          // Use the original URL if signing fails
          setSignedImageUri(imageUri);
        }
      }
    };

    getSignedUrl();
  }, [imageUri]);
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>Your event is live.</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={onEdit}>
            <Text style={styles.headerLink}>Edit</Text>
          </TouchableOpacity>
          <Text style={styles.headerText}> or </Text>
          <TouchableOpacity onPress={onCancel}>
            <Text style={styles.headerLink}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.previewContent}>
        <View style={styles.previewImage}>
          <Image
            source={{ uri: signedImageUri || imageUri || "https://i.imgur.com/1apUkfr.png" }}
            style={styles.eventImage}
            onError={(e) => console.error('Finished step image error:', e.nativeEvent.error)}
          />
          <Button
            variant="ghost"
            style={styles.editButton}
            onPress={onEdit}
          >
            <Pencil size={16} color="#FFFF00" />
          </Button>
        </View>

        <View style={styles.previewSection}>
          <Text style={styles.eventTitle}>David Byrne's American Utopia</Text>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.dateTimeList}>
            <View style={styles.dateTimeItem}>
              <Calendar size={20} color="#FFFF00" />
              <View style={styles.dateTimeContent}>
                <Text style={styles.dateTimeText}>Saturday, November 16</Text>
                <Text style={styles.dateTimeText}>5:30 PM</Text>
              </View>
            </View>
            <View style={styles.dateTimeItem}>
              <View style={styles.orDivider}>
                <Text>OR</Text>
              </View>
              <View style={styles.dateTimeContent}>
                <Text style={styles.dateTimeText}>Thursday, December 5</Text>
                <Text style={styles.dateTimeText}>8:00 PM</Text>
              </View>
            </View>
            <View style={styles.dateTimeItem}>
              <View style={styles.orDivider}>
                <Text>OR</Text>
              </View>
              <View style={styles.dateTimeContent}>
                <Text style={styles.dateTimeText}>Friday, December 13</Text>
                <Text style={styles.dateTimeText}>8:00 PM</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.locationInfo}>
            <View style={styles.locationHeader}>
              <MapPin size={20} color="#FFFF00" />
              <Text style={styles.locationName}>Hudson Theater</Text>
            </View>
            <Text style={styles.address}>146 Broadway, Brooklyn, NY 10006</Text>
          </View>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.quorumInfo}>
            <View style={styles.quorumItem}>
              <Text style={styles.quorumLabel}>Quorum</Text>
              <Text style={styles.quorumNumber}>4</Text>
            </View>
            <View style={styles.quorumItem}>
              <Text style={styles.quorumLabel}>Maximum</Text>
              <Text style={styles.quorumNumber}>4</Text>
            </View>
          </View>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.detailsSection}>
            <Text style={styles.detailsTitle}>DETAILS</Text>
            <Text style={styles.detailsText}>
              {description || 'No description provided'}
            </Text>
          </View>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.detailsSection}>
            <Text style={styles.detailsTitle}>TICKET LINK</Text>
            <TouchableOpacity
              onPress={() => Linking.openURL('http://www.thehudsonbroadway.com/whatson/david-byrnes-american-utopia/')}
            >
              <Text style={styles.ticketLink}>
                http://www.thehudsonbroadway.com/whatson/david-byrnes-american-utopia/
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.detailsSection}>
            <Text style={styles.costsTitle}>COSTS</Text>
            <Text style={styles.costsAmount}>
              {!amount || amount === "0"
                ? 'Free'
                : `$${amount}${costDescription ? ` for ${costDescription}` : ''}`}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'white',
  },
  header: {
    backgroundColor: '#FFFF00',
    padding: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerText: {
    color: 'white',
    fontSize: 16,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerLink: {
    color: 'white',
    textDecorationLine: 'underline',
    fontSize: 14,
  },
  previewContent: {
    padding: 0,
    margin: 0,
  },
  previewImage: {
    position: 'relative',
    width: '100%',
    height: 240,
    margin: 0,
  },
  eventImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  editButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  previewSection: {
    position: 'relative',
    padding: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(23, 150, 172, 0.2)',
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#222222',
    marginVertical: 0,
    lineHeight: 28,
  },
  dateTimeList: {
    flexDirection: 'column',
    gap: 8,
  },
  dateTimeItem: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  dateTimeContent: {
    flexDirection: 'column',
    gap: 4,
  },
  dateTimeText: {
    marginVertical: 0,
    fontSize: 16,
    color: '#222222',
    fontWeight: '600',
  },
  orDivider: {
    fontSize: 16,
    color: '#222222',
    fontWeight: '500',
    marginLeft: 0,
    paddingLeft: 0,
    width: 20,
    justifyContent: 'center',
  },
  locationInfo: {
    //color: '#222222',
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    marginVertical: 0,
    color: '#222222',
  },
  address: {
    marginVertical: 0,
    marginLeft: 29,
    fontSize: 16,
    color: '#FFFF00',
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  quorumInfo: {
    flexDirection: 'row',
    gap: 32,
    alignItems: 'center',
  },
  quorumItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quorumLabel: {
    fontSize: 16,
    color: '#222222',
    fontWeight: '600',
  },
  quorumNumber: {
    textAlign: 'center',
    textAlignVertical: 'center',
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#FFFF00',
    borderRadius: 12,
    color: '#222222',
    fontSize: 16,
    lineHeight: 24,
  },
  detailsSection: {
    marginTop: 24,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222222',
    marginVertical: 0,
    marginBottom: 16,
  },
  detailsText: {
    fontSize: 16,
    color: '#222222',
    marginVertical: 0,
    lineHeight: 24,
  },
  ticketLink: {
    color: '#FFFF00',
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  costsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222222',
    marginVertical: 0,
    marginBottom: 8,
  },
  costsAmount: {
    fontSize: 16,
    color: '#222222',
    marginVertical: 0,
  },
});
