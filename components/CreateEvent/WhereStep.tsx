import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { US_STATES } from '@/constants/states';
import Colors from '@/constants/Colors';

interface WhereStepProps {
  locationName: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zip: string;
  meetingPoint: string;
  onLocationNameChange: (value: string) => void;
  onAddressLine1Change: (value: string) => void;
  onAddressLine2Change: (value: string) => void;
  onCityChange: (value: string) => void;
  onStateChange: (value: string) => void;
  onZipChange: (value: string) => void;
  onMeetingPointChange: (value: string) => void;
}

export const WhereStep: React.FC<WhereStepProps> = ({
  locationName,
  addressLine1,
  addressLine2,
  city,
  state,
  zip,
  meetingPoint,
  onLocationNameChange,
  onAddressLine1Change,
  onAddressLine2Change,
  onCityChange,
  onStateChange,
  onZipChange,
  onMeetingPointChange,
}) => {
  return (
    <View>
      <Text style={styles.title}>WHERE</Text>
      <View style={styles.form}>
        <View style={styles.locationForm}>
          <TextInput
            value={locationName}
            onChangeText={onLocationNameChange}
            style={styles.locationInput}
            placeholder="Location name (Dan's house...)"
            placeholderTextColor="#999999"
          />

          <TextInput
            value={addressLine1}
            onChangeText={onAddressLine1Change}
            style={styles.locationInput}
            placeholder="Address line 1"
            placeholderTextColor="#999999"
          />

          <TextInput
            value={addressLine2}
            onChangeText={onAddressLine2Change}
            style={styles.locationInput}
            placeholder="Address line 2 (Optional)"
            placeholderTextColor="#999999"
          />

          <TextInput
            value={city}
            onChangeText={onCityChange}
            style={styles.locationInput}
            placeholder="City"
            placeholderTextColor="#999999"
          />

          <View style={styles.stateZipContainer}>
            <View style={styles.stateInput}>
              <Select value={state} onValueChange={onStateChange}>
                <SelectTrigger style={styles.selectTrigger}>
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  {US_STATES.map((state) => (
                    <SelectItem key={state.value} value={state.value}>
                      {state.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </View>
            <TextInput
              value={zip}
              onChangeText={onZipChange}
              style={[styles.locationInput, styles.zipInput]}
              placeholder="ZIP"
              placeholderTextColor="#999999"
            />
          </View>

          <TextInput
            value={meetingPoint}
            onChangeText={onMeetingPointChange}
            style={styles.locationInput}
            placeholder="Meeting point, if different from location"
            placeholderTextColor="#999999"
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  placeholderText: {
    color: '#999999',
  },
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 40,
    marginLeft: 20,
  },
  form: {
    flexDirection: 'column',
    paddingHorizontal: 20,
  },
  locationForm: {
    flexDirection: 'column',
    gap: 16,
  },
  locationInput: {
    width: '100%',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    backgroundColor: 'transparent',
    fontSize: 16,
    color: Colors.white,
    fontFamily: 'System',
    marginBottom: 16,
  },
  stateZipContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 20,
    width: '100%',
    marginBottom: 8
  },
  stateInput: {
    flex: 1,
  },
  zipInput: {
    flex: 1,
  },
  selectTrigger: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    backgroundColor: 'transparent',
    borderWidth: 0,
    height: 40,
    paddingVertical: 8,
    elevation: 0,
  },
});
