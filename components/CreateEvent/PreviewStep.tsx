import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { View, Image, StyleSheet, ScrollView, Linking, Platform, TouchableOpacity } from 'react-native';
import { Text } from "@/components/Themed";
import { Button } from '@/components/ui/button';
import { CalendarIcon, EditPenIcon, LocationPinIcon, FriendsIcon } from '@/assets/icons';
import { cloudfrontService } from '@/services/cloudfront';
import Colors from '@/constants/Colors';

interface DateTimeEntry {
  date: string;
  time: string;
  isoDate?: string; // ISO date string for API
  isoTime?: string; // ISO time string for API
  combinedIsoDateTime?: string; // Combined ISO date and time string for API
}

interface PreviewStepProps {
  eventName: string;
  minPeople: string;
  maxPeople: string;
  date: string;
  time: string;
  locationName: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zip: string;
  meetingPoint?: string;
  amount: string;
  costDescription: string;
  paymentType?: string;
  description: string;
  imageUri?: string;
  onNext: () => void;
  onEditImage: () => void;
  onEditBasics: () => void;
  onEditDateTime: () => void;
  onEditLocation: () => void;
  onEditQuorum: () => void;
  onEditDetails: () => void;
  onEditCosts: () => void;
  dates?: DateTimeEntry[];
  eventLink?: string;
  quorumMet?: boolean;
  isEditing?: boolean;
}

export const PreviewStep: React.FC<PreviewStepProps> = ({
  eventName,
  minPeople,
  maxPeople,
  locationName,
  addressLine1,
  addressLine2,
  city,
  state,
  zip,
  meetingPoint,
  amount,
  costDescription,
  paymentType,
  description,
  imageUri,
  onNext,
  onEditImage,
  onEditBasics,
  onEditDateTime,
  onEditLocation,
  onEditQuorum,
  onEditDetails,
  onEditCosts,
  dates,
  eventLink,
  quorumMet = false,
  isEditing = false
}) => {
  const [signedImageUri, setSignedImageUri] = useState<string | undefined>(undefined);

  useEffect(() => {
    const getSignedUrl = async () => {
      // Use imageUri if available, otherwise use default image
      const urlToSign = imageUri || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png';

      // Check if this is an S3 URL that needs signing
      const isS3Url = cloudfrontService.isS3Url(urlToSign);

      if (isS3Url) {
        try {
          const signedUrl = await cloudfrontService.getSignedUrl(urlToSign);
          setSignedImageUri(signedUrl);
        } catch (error) {
          console.error('Error getting signed URL for preview:', error);
          // Use the original URL if signing fails
          setSignedImageUri(urlToSign);
        }
      } else {
        // For non-S3 URLs (like those from event links), use the original URL
        setSignedImageUri(urlToSign);
      }
    };

    getSignedUrl();
  }, [imageUri]);

  // Helper function to format quorum numbers
  const formatQuorumNumber = (value: string) => {
    return value && value !== '' ? value : '•';
  };

  // Format cost display - show "Free" if amount is "0" or empty
  const formattedCost = !amount || amount === "0" ? "Free" : `$${amount}`;

  // Helper function to get human-readable payment type
  const getPaymentTypeText = (type?: string) => {
    if (!type) return '';

    switch (type) {
      case 'pay_me_back': return 'Pay me back';
      case 'chip_in': return 'Chip in';
      case 'buy_ticket': return 'Buy a ticket';
      case 'bring_wallet': return 'Bring your wallet';
      default: return '';
    }
  };

  const openInMaps = () => {
    const address = [addressLine1, addressLine2, city, state, zip]
      .filter(Boolean)
      .join(', ');

    const encodedAddress = encodeURIComponent(address);
    const mapsUrl = Platform.select({
      ios: `maps:0,0?q=${encodedAddress}`,
      android: `geo:0,0?q=${encodedAddress}`,
      default: `https://maps.google.com/?q=${encodedAddress}`
    });

    Linking.canOpenURL(mapsUrl).then(supported => {
      if (supported) {
        Linking.openURL(mapsUrl);
      } else {
        // Fallback to Google Maps web URL if native maps app cannot be opened
        Linking.openURL(`https://maps.google.com/?q=${encodedAddress}`);
      }
    });
  };

  console.log('Preview Step Props:', {
    eventName,
    minPeople,
    maxPeople,
    locationName,
    addressLine1,
    addressLine2,
    city,
    state,
    zip,
    amount,
    costDescription,
    description,
    imageUri,
    dates,
    eventLink
  });

  return (
    <ScrollView>
      <Text style={styles.previewTitle}>PREVIEW</Text>
      <View style={styles.previewContent}>
        <View style={styles.previewImage}>
          <Image
            source={{ uri: signedImageUri}}
            style={styles.eventImage}
            onError={(e) => console.error('Preview image error:', e.nativeEvent.error)}
          />
          <Button
            variant="ghost"
            style={styles.editImageButton}
            onPress={onEditImage}
          >
            <EditPenIcon size={16} color={Colors.black} />
          </Button>
        </View>

        <View style={styles.previewSection}>
          <Text style={styles.eventTitle}>{eventName}</Text>
          <Button
            variant="ghost"
            style={styles.editButton}
            onPress={onEditBasics}
          >
            <EditPenIcon size={16} color={Colors.primary} />
          </Button>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.dateTimeList}>
            {dates?.map((dateTime, index) => (
              <View key={index} style={styles.dateTimeItem}>
                {index === 0 ? (
                  <CalendarIcon size={20} color={Colors.secondary} />
                ) : (
                  <View style={styles.orContainer}>
                    <Text style={styles.orText}>OR</Text>
                  </View>
                )}
                <View style={styles.dateTimeContent}>
                  <Text style={[styles.dateTimeText, styles.dateText]}>
                    {moment(dateTime.date).format('dddd, MMMM D, YYYY')}
                  </Text>
                  <Text style={styles.dateTimeText}>
                    {moment(dateTime.time, 'HH:mm').format('h:mm A')}
                  </Text>
                </View>
              </View>
            ))}
          </View>
          <Button
            variant="ghost"
            style={[styles.editButton, (isEditing && quorumMet) && styles.disabledButton]}
            onPress={(isEditing && quorumMet) ? undefined : onEditDateTime}
            disabled={isEditing && quorumMet}
          >
            <EditPenIcon size={16} color={(isEditing && quorumMet) ? "#999" : Colors.primary} />
          </Button>
        </View>

        {/* Only show location section if we have location data */}
        {(locationName || addressLine1 || (city && state)) && (
          <View style={styles.previewSection}>
            <TouchableOpacity
              style={styles.locationInfo}
              onPress={openInMaps}
            >
              <View style={styles.locationHeader}>
                <LocationPinIcon size={18} color={Colors.secondary} />
                <Text style={styles.locationName}>{locationName || 'Location'}</Text>
              </View>
              {(addressLine1 || city || state) && (
                <Text style={[styles.address, styles.linkText]}>
                  {[addressLine1, addressLine2, city, state, zip]
                    .filter(Boolean)
                    .join(', ')}
                </Text>
              )}
              {meetingPoint && (
                <Text style={styles.meetingPoint}>
                  Meeting point: {meetingPoint}
                </Text>
              )}
            </TouchableOpacity>
            <Button
              variant="ghost"
              style={styles.editButton}
              onPress={onEditLocation}
            >
              <EditPenIcon size={16} color={Colors.primary} />
            </Button>
          </View>
        )}

        <View style={styles.previewSection}>
          <View style={styles.quorumInfo}>
            <FriendsIcon size={20} color={Colors.secondary} />
            <View style={styles.quorumContent}>
              <View style={styles.quorumItem}>
                <Text style={styles.quorumLabel}>Quorum</Text>
                <Text style={styles.quorumNumber}>
                  {formatQuorumNumber(minPeople)}
                </Text>
              </View>
              <View style={styles.quorumItem}>
                <Text style={styles.quorumLabel}>Maximum</Text>
                <Text style={styles.quorumNumber}>
                  {maxPeople === 'unlimited' ? '∞' : formatQuorumNumber(maxPeople)}
                </Text>
              </View>
            </View>
          </View>
          <Button
            variant="ghost"
            style={styles.editButton}
            onPress={onEditQuorum}
          >
            <EditPenIcon size={16} color={Colors.primary} />
          </Button>
        </View>

        <View style={styles.previewSection}>
          <View style={styles.detailsSection}>
            <Text style={styles.detailsTitle}>DETAILS</Text>
            <Text style={styles.detailsText}>{description}</Text>
          </View>
          <Button
            variant="ghost"
            style={styles.editButton}
            onPress={onEditDetails}
          >
            <EditPenIcon size={16} color={Colors.primary} />
          </Button>
        </View>

        {eventLink && (
          <View style={styles.previewSection}>
            <View style={styles.detailsSection}>
              <Text style={styles.detailsTitle}>TICKET LINK</Text>
              <Text
                style={styles.ticketLink}
                onPress={() => Linking.openURL(eventLink)}
              >
                {eventLink}
              </Text>
            </View>
            <Button
              variant="ghost"
              style={styles.editButton}
              onPress={onEditDetails}
            >
              <EditPenIcon size={16} color={Colors.primary} />
            </Button>
          </View>
        )}

        <View style={styles.previewSection}>
          <View style={styles.detailsSection}>
            <Text style={styles.costsTitle}>COSTS</Text>
            <Text style={styles.costsAmount}>
              {formattedCost}
              {formattedCost !== "Free" && costDescription && ` for ${costDescription}`}
            </Text>
            {formattedCost !== "Free" && paymentType && (
              <Text style={styles.paymentTypeText}>
                {getPaymentTypeText(paymentType)}
              </Text>
            )}
          </View>
          <Button
            variant="ghost"
            style={styles.editButton}
            onPress={onEditCosts}
          >
            <EditPenIcon size={16} color={Colors.primary} />
          </Button>
        </View>

        <View style={styles.inviteContainer}>
          <Text style={styles.inviteText}>Your event is ready to post.</Text>
          <Text style={styles.inviteSubtext}>Let's invite people!</Text>
          <Button
            style={styles.inviteButton}
            textStyle={styles.inviteButtonText}
            onPress={onNext}
          >
            INVITE
          </Button>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  previewTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.white,
    marginTop: 20,
    marginBottom: 24,
    marginLeft: 20,
  },
  previewContent: {
    padding: 0,
    margin: 0,
  },
  previewImage: {
    position: 'relative',
    width: '100%',
    height: 240,
    margin: 0,
  },
  eventImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 32,
    height: 32,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  previewSection: {
    position: 'relative',
    padding: 16,
    paddingHorizontal: 20,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    marginVertical: 0,
    lineHeight: 28,
  },
  editButton: {
    position: 'absolute',
    top: 16,
    right: 20,
    width: 32,
    height: 32,
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  dateTimeList: {
    flexDirection: 'column',
    gap: 8,
  },
  dateTimeItem: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  dateTimeContent: {
    flexDirection: 'column',
    gap: 4,
  },
  dateTimeText: {
    marginVertical: 0,
    fontSize: 16,
    color: Colors.white,
    fontWeight: 'normal',
  },
  dateText: {
    fontWeight: '600',
  },
  orContainer: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  orText: {
    fontSize: 12,
    color: Colors.secondary,
    fontWeight: '500',
  },
  locationInfo: {
    flex: 1,
    paddingRight: 40,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    marginVertical: 0,
    color: Colors.white,
  },
  address: {
    marginVertical: 0,
    marginLeft: 29,
    fontSize: 16,
    color: Colors.primary,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  meetingPoint: {
    marginVertical: 8,
    marginLeft: 29,
    fontSize: 14,
    color: '#999999',
  },
  linkText: {
    color: Colors.primary,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  quorumInfo: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  quorumContent: {
    flexDirection: 'row',
    gap: 32,
    alignItems: 'center',
  },
  quorumItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quorumLabel: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '600',
  },
  quorumNumber: {
    textAlign: 'center',
    textAlignVertical: 'center',
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 12,
    color: Colors.primary,
    fontSize: 16,
    lineHeight: 24,
  },
  detailsSection: {
    marginTop: 24,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginVertical: 0,
    marginBottom: 16,
  },
  detailsText: {
    fontSize: 16,
    color: Colors.white,
    marginVertical: 0,
    lineHeight: 24,
  },
  ticketLink: {
    color: Colors.primary,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  costsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginVertical: 0,
    marginBottom: 8,
  },
  costsAmount: {
    fontSize: 16,
    color: Colors.white,
    marginVertical: 0,
  },
  paymentTypeText: {
    fontSize: 14,
    color: '#999999',
    marginTop: 4,
    marginVertical: 0,
  },
  inviteContainer: {
    backgroundColor: Colors.hyperBlue,
    padding: 24,
    paddingTop: 40,
    paddingHorizontal: 20,
    marginTop: 20,
    alignItems: 'center',
  },
  inviteText: {
    color: Colors.white,
    fontSize: 20,
    fontWeight: '600',
    marginVertical: 0,
    marginBottom: 8,
    lineHeight: 28,
    textAlign: 'center',
  },
  inviteSubtext: {
    color: Colors.white,
    fontSize: 20,
    fontWeight: '500',
    marginVertical: 0,
    marginBottom: 24,
    textAlign: 'center',
  },
  inviteButton: {
    backgroundColor: Colors.primary,
    height: 50,
    width: 160,
    marginBottom: 70,
    borderWidth: 0,
    padding: 16,
    borderRadius: 0,
    fontWeight: '500',
    alignItems: 'center',
    justifyContent: 'center',
  },
  inviteButtonText: {
    color: Colors.secondaryDark,
    fontSize: 18,
    fontWeight: '700',
  },
});
