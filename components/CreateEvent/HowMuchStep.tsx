import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import Checkbox from '@/components/ui/checkbox';
import Colors from '@/constants/Colors';

interface HowMuchStepProps {
  amount: string;
  description: string;
  paymentType: string;
  onAmountChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
  onPaymentTypeChange: (value: string) => void;
}

export const HowMuchStep: React.FC<HowMuchStepProps> = ({
  amount,
  description,
  paymentType,
  onAmountChange,
  onDescriptionChange,
  onPaymentTypeChange,
}) => {
  return (
    <View>
      <Text style={styles.title}>HOW MUCH</Text>
      <View style={styles.form}>
        <View style={styles.costSection}>
          <Text style={styles.costTitle}>COSTS (OPTIONAL)</Text>
          <View style={styles.costInputs}>
            <View style={styles.inputWrapper}>
              <Text style={styles.dollarSign}>$</Text>
              <TextInput
                value={amount}
                onChangeText={onAmountChange}
                style={[styles.costInput, styles.amountInput]}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
            <TextInput
              value={description}
              onChangeText={onDescriptionChange}
              style={[styles.costInput, styles.descriptionInput]}
              placeholder="for what (tickets, entry fees)"
            />
          </View>
        </View>

        <View style={styles.checkboxGroup}>
          <TouchableOpacity
            style={styles.checkboxWrapper}
            onPress={() => onPaymentTypeChange('pay_me_back')}
          >
            <View style={styles.checkboxLabel}>
              <Checkbox
                checked={paymentType === 'pay_me_back'}
                onCheckedChange={() => onPaymentTypeChange('pay_me_back')}
                style={styles.checkbox}
              />
              <Text style={styles.checkboxText}>Pay me back</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkboxWrapper}
            onPress={() => onPaymentTypeChange('chip_in')}
          >
            <View style={styles.checkboxLabel}>
              <Checkbox
                checked={paymentType === 'chip_in'}
                onCheckedChange={() => onPaymentTypeChange('chip_in')}
                style={styles.checkbox}
              />
              <Text style={styles.checkboxText}>Chip in</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkboxWrapper}
            onPress={() => onPaymentTypeChange('buy_ticket')}
          >
            <View style={styles.checkboxLabel}>
              <Checkbox
                checked={paymentType === 'buy_ticket'}
                onCheckedChange={() => onPaymentTypeChange('buy_ticket')}
                style={styles.checkbox}
              />
              <Text style={styles.checkboxText}>Buy a ticket</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkboxWrapper}
            onPress={() => onPaymentTypeChange('bring_wallet')}
          >
            <View style={styles.checkboxLabel}>
              <Checkbox
                checked={paymentType === 'bring_wallet'}
                onCheckedChange={() => onPaymentTypeChange('bring_wallet')}
                style={styles.checkbox}
              />
              <Text style={styles.checkboxText}>Bring your wallet</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 40,
    marginLeft: 20,
  },
  form: {
    flexDirection: 'column',
    paddingHorizontal: 20,
    gap: 32,
  },
  costSection: {
    flexDirection: 'column',
    marginBottom: 16,
  },
  costTitle: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '500',
    marginBottom: 16,
  },
  costInputs: {
    flexDirection: 'row',
    width: '100%',
    gap: 16,
  },
  inputWrapper: {
    position: 'relative',
    width: '30%',
  },
  costInput: {
    width: '100%',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    backgroundColor: 'transparent',
    fontSize: 16,
    color: Colors.white,
    fontFamily: 'System',
  },
  amountInput: {
    paddingLeft: 12,
  },
  descriptionInput: {
    width: '70%',
  },
  dollarSign: {
    position: 'absolute',
    left: 0,
    top: 8,
    color: Colors.white,
    fontSize: 16,
  },
  checkboxGroup: {
    flexDirection: 'column',
    gap: 12,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkboxLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkboxText: {
    fontSize: 16,
    color: Colors.white,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
  },
});