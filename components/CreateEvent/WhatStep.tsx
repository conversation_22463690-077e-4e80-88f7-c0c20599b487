import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Image, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { Button } from '@/components/ui/button';
import * as ImagePicker from 'expo-image-picker';
import { uploadService } from '@/services/upload';
import { eventService } from '@/services/event';
import { Link } from 'lucide-react-native';
import { cloudfrontService } from '@/services/cloudfront';
import Colors from '@/constants/Colors';

interface WhatStepProps {
  eventName: string;
  onEventNameChange: (value: string) => void;
  imageUri?: string;
  onImageUriChange?: (value: string) => void;
  onDescriptionChange?: (value: string) => void;
  onLocationChange?: (value: string) => void;
  onCostChange?: (value: string | number) => void;
  onDateChange?: (value: string) => void;
  eventLink?: string;
  onEventLinkChange: (value: string) => void;
}

export const WhatStep: React.FC<WhatStepProps> = ({
  eventLink,
  onEventLinkChange,
  eventName,
  onEventNameChange,
  imageUri,
  onImageUriChange,
  onDescriptionChange,
  onLocationChange,
  onCostChange,
  onDateChange,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [localImageUri, setLocalImageUri] = useState<string | undefined>(imageUri);
  const [signedImageUri, setSignedImageUri] = useState<string | undefined>(undefined);
  const [isExtractingLink, setIsExtractingLink] = useState(false);

  // Function to extract event information from a link
  const extractEventFromLink = async (text?: string) => {
    const linkToExtract = text || eventLink;

    if (!linkToExtract) {
      Alert.alert('Error', 'Please enter a valid event link');
      return;
    }

    setIsExtractingLink(true);

    try {
      const response = await eventService.extractEventFromLink(linkToExtract);

      if (response.error) {
        Alert.alert('Error', response.error);
        return;
      }

      if (response.data) {
        const { name, date, location, description, cost, image_url } = response.data;

        // Update the form fields with the extracted information
        // Helper function to clean location string
        const cleanLocationString = (loc: string): string => {
          return loc
            .replace(/\s*,\s*,+\s*/g, ', ') // Replace multiple commas with a single comma
            .replace(/^\s*,\s*|\s*,\s*$/g, '') // Remove commas at beginning or end
            .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
            .trim();
        };

        if (name) onEventNameChange(name);
        if (description && onDescriptionChange) onDescriptionChange(description);
        if (location && onLocationChange) onLocationChange(cleanLocationString(location));
        if (cost !== null && cost !== undefined && onCostChange) onCostChange(cost.toString());
        if (date && date.length > 0 && onDateChange) onDateChange(date[0]);

        if (image_url) {
          setLocalImageUri(image_url);
          if (onImageUriChange) {
            onImageUriChange(image_url);
          }

          // For images from event links, use the original URL directly
          // No need to sign URLs that come from external sources
          setSignedImageUri(image_url);
        }
      }
    } catch (error) {
      console.error('Error extracting event information:', error);
      Alert.alert('Error', 'Failed to extract event information. Please try again.');
    } finally {
      setIsExtractingLink(false);
    }
  };

  // Function to pick an image from the library
  const pickImage = async () => {
    try {
      // Request permission to access the media library
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photos');
        return;
      }

      // Launch the image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImageUri = result.assets[0].uri;
        setLocalImageUri(selectedImageUri);

        // Start the upload process
        setIsUploading(true);
        try {
          // Upload the image to S3 and get the URL
          const uploadedUrl = await uploadService.uploadImageToS3(selectedImageUri);

          // Update the parent component with the uploaded URL
          if (onImageUriChange) {
            onImageUriChange(uploadedUrl);
          }

          // For S3 uploads, we need to get a signed URL
          try {
            const signedUrl = await cloudfrontService.getSignedUrl(uploadedUrl);
            setSignedImageUri(signedUrl);
          } catch (error) {
            console.error('Error getting signed URL for uploaded image:', error);
            setSignedImageUri(uploadedUrl);
          }
        } catch (error) {
          console.error('Error uploading image:', error);
          Alert.alert('Upload Error', 'Failed to upload image. Please try again.');
        } finally {
          setIsUploading(false);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  // Update useEffect to sync localImageUri with imageUri prop and get signed URL if needed
  useEffect(() => {
    if (imageUri) {
      setLocalImageUri(imageUri);

      // Check if this is an S3 URL that needs signing
      const isS3Url = imageUri.includes('s3.') && imageUri.includes('amazonaws.com');

      if (isS3Url) {
        // Get a signed URL for S3 images
        const getSignedUrl = async () => {
          try {
            const signedUrl = await cloudfrontService.getSignedUrl(imageUri);
            setSignedImageUri(signedUrl);
          } catch (error) {
            console.error('Error getting signed URL:', error);
            // Use the original URL if signing fails
            setSignedImageUri(imageUri);
          }
        };

        getSignedUrl();
      } else {
        // For non-S3 URLs (like those from event links), use the original URL
        setSignedImageUri(imageUri);
      }
    }
  }, [imageUri]);

  return (
    <View>
      <Text style={styles.title}>WHAT</Text>
      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Name your event
          </Text>
          <View style={{ position: 'relative' }}>
            <TextInput
              style={styles.input}
              value={eventName}
              onChangeText={onEventNameChange}
              maxLength={75}
            />
            <Text style={styles.counter}>
              {eventName.length}/75
            </Text>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Have an event link?</Text>
          <Text style={styles.description}>
            We'll pull in as much information as possible
          </Text>
          <View style={styles.linkInputContainer}>
            <TextInput
              style={styles.linkInput}
              placeholder="Paste the link here"
              value={eventLink}
              onChangeText={(text) => {
                onEventLinkChange(text);
                extractEventFromLink(text);
              }}
              onKeyPress={({ nativeEvent }) => {
                if (nativeEvent.key === 'Enter' && eventLink) {
                  extractEventFromLink();
                }
              }}
              // Note: onPaste is not supported in react-native's TextInput
              // For web compatibility, consider handling paste events at a higher level if needed
            />
            <TouchableOpacity
              style={[styles.extractButton, isExtractingLink && styles.extractButtonDisabled]}
              onPress={() => extractEventFromLink()}
              disabled={isExtractingLink || !eventLink}
            >
              <Link size={20} color={Colors.black} />
            </TouchableOpacity>
          </View>
          {isExtractingLink && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.loadingText}>Extracting event information...</Text>
            </View>
          )}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Upload a photo?</Text>
          <Text style={styles.description}>
            Add a screenshot with the event details, an advertisement, or a photo of your choosing
          </Text>
          <Button
            variant="default"
            style={styles.uploadButton}
            textStyle={{ color: Colors.black }}
            onPress={pickImage}
            disabled={isUploading}
          >
            {isUploading ? 'UPLOADING...' : 'UPLOAD PHOTO'}
          </Button>

          {isUploading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.loadingText}>Uploading image...</Text>
            </View>
          )}

          {(localImageUri || imageUri) && !isUploading && (
            <View style={styles.imagePreviewContainer}>
              <Image
                source={{ uri: signedImageUri || localImageUri || imageUri }}
                style={styles.imagePreview}
                resizeMode="cover"
                onError={(e) => {
                  console.error('Image preview error:', e.nativeEvent.error);
                }}
              />
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 40,
    marginLeft: 20,
  },
  form: {
    flexDirection: 'column',
    paddingHorizontal: 20,
    gap: 32,
  },
  inputGroup: {
    flexDirection: 'column',
    marginBottom: 32,
  },
  label: {
    fontSize: 24,
    color: Colors.white,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    width: '100%',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    backgroundColor: 'transparent',
    fontSize: 16,
    color: Colors.white,
  },
  description: {
    fontSize: 16,
    color: Colors.white,
    opacity: 0.7,
    marginBottom: 8,
  },
  uploadButton: {
    backgroundColor: Colors.primary,
    color: Colors.black,
    paddingVertical: 8,
    height: 36,
    paddingHorizontal: 24,
    borderRadius: 4,
    fontWeight: '500',
    alignSelf: 'flex-start',
  },
  counter: {
    position: 'absolute',
    top: 8,
    right: 0,
    color: Colors.white,
    opacity: 0.5,
    fontSize: 14,
  },
  imagePreviewContainer: {
    marginTop: 16,
    borderRadius: 8,
    overflow: 'hidden',
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  loadingText: {
    marginLeft: 8,
    color: Colors.primary,
    fontSize: 14,
  },
  linkInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  linkInput: {
    flex: 1,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    backgroundColor: 'transparent',
    fontSize: 16,
    color: Colors.white,
    marginRight: 8,
  },
  extractButton: {
    backgroundColor: Colors.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  extractButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
});
