import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { Text } from "@/components/Themed";
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react-native';
import { ContactsModal } from '@/components/ContactsModal/ContactsModal';
import { eventService } from '@/services/event';
import { groupService, GroupResponse } from '@/services/group';
import { connectRequestService } from '@/services/connectRequest';
import { api, withAuth } from '@/services/api';
import { useAuthStore } from '@/stores/auth';
import { useApiContactsStore } from '@/stores/apiContacts';
import Colors from '@/constants/Colors';
import { mixpanelService } from '@/services/mixpanel';

// Import the DisplayContact type from ContactsModal
interface DisplayContact {
  id: string;
  name: string;
  email: string;
  isExistingContact: boolean;
  phoneContact?: any;
}

interface DateTimeEntry {
  date: string;
  time: string;
  isoDate?: string; // ISO date string for API
  isoTime?: string; // ISO time string for API
  combinedIsoDateTime?: string; // Combined ISO date and time string for API
}

interface ChangeTracker {
  [key: string]: boolean;
}

interface InviteStepProps {
  eventTitle: string;
  eventData: {
    name: string;
    date: string;
    time: string;
    locationName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    zip: string;
    meetingPoint?: string; // Add meetingPoint field
    minPeople: string;
    maxPeople: string;
    description?: string;
    imageUri?: string;
    dates?: DateTimeEntry[];
    amount?: string;
    costDescription?: string;
    payment_type?: string;
    event_link?: string;
    isMultiple?: boolean;
    cutoffTime?: string; // Add cutoffTime field
    invitees?: number[]; // Array of user IDs who were invited to this event
  };
  onNext: () => void;
  onEventCreated?: (eventId: number) => void;
  onResetForm?: () => void; // New prop for resetting the form
  isEditing?: boolean; // Whether we're editing an existing event
  eventId?: number | null; // ID of the event being edited
  changedFields?: ChangeTracker; // Track which fields have changed (for editing)
  originalData?: any; // Original event data (for editing)
}

export const InviteStep: React.FC<InviteStepProps> = ({ eventTitle, eventData, onNext, onEventCreated, onResetForm, isEditing = false, eventId = null, changedFields, originalData }) => {
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [isContactsModalOpen, setIsContactsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingGroups, setIsLoadingGroups] = useState(true);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [selectedDisplayContacts, setSelectedDisplayContacts] = useState<DisplayContact[]>([]);
  const [inviteeCount, setInviteeCount] = useState<number>(0);
  const [groups, setGroups] = useState<GroupResponse[]>([]);
  const [error, setError] = useState<string | null>(null);
  const currentUser = useAuthStore(state => state.userData);
  const { contacts } = useApiContactsStore();

  const toggleGroup = (group: string) => {
    if (selectedGroups.includes(group)) {
      setSelectedGroups(selectedGroups.filter(g => g !== group));
    } else {
      setSelectedGroups([...selectedGroups, group]);
    }
  };

  // Fetch user's groups when component mounts
  useEffect(() => {
    const fetchGroups = async () => {
      try {
        setIsLoadingGroups(true);
        setError(null);

        const response = await groupService.getAllGroups();

        if (response.error) {
          setError(response.error);
          return;
        }

        if (response.data) {
          setGroups(response.data);
        }
      } catch (err) {
        console.error('Error fetching groups:', err);
        setError('Failed to load groups. Please try again.');
      } finally {
        setIsLoadingGroups(false);
      }
    };

    fetchGroups();
  }, []);

  // Update invitee count when groups or contacts change
  useEffect(() => {
    // Calculate the total number of invitees from selected groups and contacts
    let groupMembersCount = 0;

    // Count actual members from selected groups
    selectedGroups.forEach(groupName => {
      const group = groups.find(g => g.name === groupName);
      if (group && group.members) {
        groupMembersCount += group.members.length;
      }
    });

    const totalInvitees = groupMembersCount + selectedDisplayContacts.length;
    setInviteeCount(totalInvitees);
  }, [selectedGroups, selectedDisplayContacts, groups]);

  // Fetch contacts when component mounts
  useEffect(() => {
    // Prefetch contacts so they're ready when the modal opens
    useApiContactsStore.getState().fetchContacts();
  }, []);

  // Pre-populate selected contacts and groups when editing an event
  useEffect(() => {
    if (isEditing && eventData.invitees && eventData.invitees.length > 0 && contacts.length > 0) {
      console.log('Pre-populating invitees for editing:', eventData.invitees);

      // Convert user IDs to contact IDs
      const invitedContactIds: string[] = [];
      const invitedUserIds = eventData.invitees;

      // Find contacts that correspond to the invited user IDs
      invitedUserIds.forEach(userId => {
        const contact = contacts.find(c => c.user_id === userId);
        if (contact) {
          invitedContactIds.push(contact.id.toString());
        }
      });

      console.log('Found contact IDs for invited users:', invitedContactIds);
      setSelectedContacts(invitedContactIds);

      // Check if any groups contain all the invited users and pre-select those groups
      const groupsToSelect: string[] = [];
      groups.forEach(group => {
        if (group.members && group.members.length > 0) {
          // Get user IDs for all members in this group
          const groupUserIds = group.members.map(contactId => {
            const contact = contacts.find(c => c.id === contactId);
            return contact?.user_id;
          }).filter(Boolean) as number[];

          // Check if all group members are in the invitees list
          const allMembersInvited = groupUserIds.length > 0 &&
            groupUserIds.every(userId => invitedUserIds.includes(userId));

          if (allMembersInvited) {
            groupsToSelect.push(group.name);
          }
        }
      });

      console.log('Pre-selecting groups:', groupsToSelect);
      setSelectedGroups(groupsToSelect);
    }
  }, [isEditing, eventData.invitees, contacts, groups]);

  const handleContactsSelected = (displayContacts: DisplayContact[]) => {
    setSelectedDisplayContacts(displayContacts);

    // Extract contact IDs for backward compatibility with existing logic
    const contactIds = displayContacts.map(contact => contact.id);
    setSelectedContacts(contactIds);

    console.log('Selected display contacts:', displayContacts);
    console.log('Selected contact IDs:', contactIds);

    // Log the names of selected contacts for debugging
    const selectedContactNames = displayContacts.map(contact => contact.name);
    console.log('Selected contact names:', selectedContactNames);
  };

  const handlePostEvent = async () => {
    try {
      setIsLoading(true);

      // Helper function to check if a string is not empty
      const isNotEmpty = (str?: string) => str && str.trim() !== '';

      // Format the address (without the location name) for display purposes
      // First check if we have any non-empty address data
      const hasAddressData = isNotEmpty(eventData.addressLine1) || isNotEmpty(eventData.city) || isNotEmpty(eventData.state);

      let fullAddress = '';
      if (hasAddressData) {
        // Create city-state-zip part only if we have non-empty city or state
        let cityStateZip = '';
        if (isNotEmpty(eventData.city) || isNotEmpty(eventData.state)) {
          cityStateZip = [
            isNotEmpty(eventData.city) ? eventData.city : null,
            isNotEmpty(eventData.state) ?
              (isNotEmpty(eventData.city) ? eventData.state : eventData.state) : null,
            isNotEmpty(eventData.zip) ? eventData.zip : null
          ].filter(Boolean).join(' ');
        }

        // Combine all parts, filtering out empty strings
        fullAddress = [
          isNotEmpty(eventData.addressLine1) ? eventData.addressLine1 : null,
          isNotEmpty(eventData.addressLine2) ? eventData.addressLine2 : null,
          cityStateZip !== '' ? cityStateZip : null
        ].filter(Boolean).join(', ');
      }

      // Format the date and time
      // If we have multiple dates from the parent component, use those
      let dateTime: string | string[] = eventData.time ? `${eventData.date} at ${eventData.time}` : eventData.date;

      // Check if we have multiple dates in the eventData
      if (eventData.dates && eventData.dates.length > 0) {
        // Filter out any empty dates first
        const validDates = eventData.dates.filter(d => d.date || d.time);
        console.log('Valid dates:', validDates);

        if (validDates.length > 0) {
          // For the API, we need to pass an array of ISO date strings
          dateTime = validDates.map(d => {
            // Use the combined ISO date and time if available
            if (d.combinedIsoDateTime) {
              return d.combinedIsoDateTime;
            }
            // Otherwise use the ISO date string if available
            else if (d.isoDate) {
              return d.isoDate;
            }
            // Fallback to formatted date and time
            else {
              return d.time ? `${d.date} at ${d.time}` : d.date;
            }
          });
          console.log('ISO dates for API:', dateTime);
        }
      }

      // Log the event data for debugging
      console.log(`Event data before ${isEditing ? 'updating' : 'creating'}:`, {
        name: eventData.name || eventTitle,
        date: dateTime,
        locationName: eventData.locationName,
        address: fullAddress,
        dates: eventData.dates,
        payment_type: eventData.payment_type // Log payment_type
      });

      // Process selected contacts to get user IDs and create connect requests for non-contacts
      let inviteeIds: number[] = [];
      const connectRequestsToCreate: Array<{email: string, first_name: string, last_name?: string}> = [];

      // Process display contacts
      for (const displayContact of selectedDisplayContacts) {
        if (displayContact.isExistingContact) {
          // This is an existing contact - find the corresponding API contact to get user_id
          const apiContact = contacts.find(contact =>
            contact.email?.toLowerCase() === displayContact.email.toLowerCase() && !contact.is_blocked
          );
          if (apiContact && apiContact.user_id) {
            inviteeIds.push(apiContact.user_id);
          }
        } else {
          // This is a phone contact only - we need to create a connect request
          // Parse the name into first and last name
          const nameParts = displayContact.name.trim().split(' ');
          const firstName = nameParts[0] || 'Unknown';
          const lastName = nameParts.slice(1).join(' ') || undefined;

          connectRequestsToCreate.push({
            email: displayContact.email,
            first_name: firstName,
            last_name: lastName
          });
        }
      }

      console.log('Selected existing contacts user_ids:', inviteeIds);
      console.log('Connect requests to create:', connectRequestsToCreate);

      // Add members from selected groups, excluding blocked contacts
      selectedGroups.forEach(groupName => {
        const group = groups.find(g => g.name === groupName);
        if (group && group.members && group.members.length > 0) {
          // For each member in the group (which is a contact ID), get the corresponding user_id
          const groupUserIds = group.members.map(contactId => {
            // Find the contact with this ID
            const contact = contacts.find(c => c.id === contactId);
            // Return the user_id if the contact exists and is not blocked
            return contact && !contact.is_blocked ? contact.user_id : null;
          }).filter(Boolean) as number[];

          console.log(`Group ${groupName} user_ids:`, groupUserIds);

          // Add them to inviteeIds if they're not already included
          inviteeIds = [...new Set([...inviteeIds, ...groupUserIds])];
        }
      });

      console.log('Final invitee user_ids:', inviteeIds);

      // Convert amount to number if provided
      const costAmount = eventData.amount ? parseFloat(eventData.amount) : undefined;

      // Create event data object
      // Helper function to check if a string is not empty

      const newEventData = {
        name: eventData.name || eventTitle,
        date: dateTime,
        // Location fields
        location: isNotEmpty(eventData.locationName) ? eventData.locationName : '', // Use the location name directly or empty string if not provided
        address: isNotEmpty(fullAddress) ? fullAddress : '',
        // New address fields
        location_name: isNotEmpty(eventData.locationName) ? eventData.locationName : '',
        address_line1: isNotEmpty(eventData.addressLine1) ? eventData.addressLine1 : '',
        address_line2: isNotEmpty(eventData.addressLine2) ? eventData.addressLine2 : '',
        city: isNotEmpty(eventData.city) ? eventData.city : '',
        state: isNotEmpty(eventData.state) ? eventData.state : '',
        zip: isNotEmpty(eventData.zip) ? eventData.zip : '',
        meeting_point: isNotEmpty(eventData.meetingPoint) ? eventData.meetingPoint : '',
        // Other fields
        image_url: eventData.imageUri || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png', // Use provided image or default - this will be signed later in the app
        has_options: false,
        quorum_met: false,
        cost: costAmount,
        cost_purpose: eventData.costDescription,
        payment_type: eventData.payment_type,
        event_link: eventData.event_link, // Add event_link field
        description: eventData.description, // Make sure this is included
        invitees: inviteeIds,
        owner: currentUser?.displayName || 'Unknown User',
        owner_id: currentUser?.id,
        quorum: parseInt(eventData.minPeople) || 1,
        max_participants: eventData.maxPeople === 'unlimited' ? null : parseInt(eventData.maxPeople) || null,
        multiple_dates: eventData.isMultiple || false,
        response_cutoff: eventData.cutoffTime ? parseInt(eventData.cutoffTime) : undefined // Add response cutoff
      };

      // Log the final event data being sent to the API
      console.log(`Final event data for ${isEditing ? 'update' : 'create'}:`, {
        ...newEventData,
        date: dateTime,
        dateType: typeof dateTime,
        isArray: Array.isArray(dateTime),
        originalDates: eventData.dates,
        payment_type: newEventData.payment_type, // Log payment_type
        event_link: newEventData.event_link // Log event_link to verify it's included
      });

      let response;

      if (isEditing && eventId) {
        // Update existing event with only changed fields
        console.log(`Updating event with ID: ${eventId}`);

        // Build partial update payload using changedFields
        const updatePayload: any = {};

        if (changedFields && originalData) {
          // Only include fields that have actually changed
          if (changedFields.name) updatePayload.name = newEventData.name;
          if (changedFields.description) updatePayload.description = newEventData.description;
          if (changedFields.image_url) updatePayload.image_url = newEventData.image_url;
          if (changedFields.location_name) updatePayload.location_name = newEventData.location_name;
          if (changedFields.address_line1) updatePayload.address_line1 = newEventData.address_line1;
          if (changedFields.address_line2) updatePayload.address_line2 = newEventData.address_line2;
          if (changedFields.city) updatePayload.city = newEventData.city;
          if (changedFields.state) updatePayload.state = newEventData.state;
          if (changedFields.zip) updatePayload.zip = newEventData.zip;
          if (changedFields.meeting_point) updatePayload.meeting_point = newEventData.meeting_point;
          if (changedFields.quorum) updatePayload.quorum = newEventData.quorum;
          if (changedFields.max_participants) updatePayload.max_participants = newEventData.max_participants;
          if (changedFields.cost) updatePayload.cost = newEventData.cost;
          if (changedFields.cost_purpose) updatePayload.cost_purpose = newEventData.cost_purpose;
          if (changedFields.payment_type) updatePayload.payment_type = newEventData.payment_type;
          if (changedFields.event_link) updatePayload.event_link = newEventData.event_link;
          if (changedFields.response_cutoff) updatePayload.response_cutoff = newEventData.response_cutoff;
          if (changedFields.multiple_dates) updatePayload.multiple_dates = newEventData.multiple_dates;

          // Handle dates if they changed
          if (changedFields.date || changedFields.time || changedFields.dates) {
            updatePayload.date = newEventData.date;
            updatePayload.multiple_dates = newEventData.multiple_dates;
          }

          // Always include invitees since this step is specifically for managing invitees
          updatePayload.invitees = newEventData.invitees;

          console.log('Partial update payload:', updatePayload);
        } else {
          // Fallback to full update if changedFields not available
          console.log('No changedFields available, using full update');
          Object.assign(updatePayload, newEventData);
        }

        response = await eventService.updateEvent(eventId, updatePayload);

        if (response.error) {
          Alert.alert('Error', response.error || 'Failed to update event');
          setIsLoading(false);
          return;
        }

        Alert.alert('Success', 'Event updated successfully');
      } else {
        // Create new event
        console.log('Creating new event');
        response = await eventService.createEvent(newEventData);

        if (response.error) {
          Alert.alert('Error', response.error || 'Failed to create event');
          setIsLoading(false);
          return;
        }
      }

      if (response.data && response.data.id) {
        const eventId = response.data.id;

        // Create connect requests for phone contacts that aren't existing contacts
        if (connectRequestsToCreate.length > 0) {
          console.log(`Creating ${connectRequestsToCreate.length} connect requests...`);

          try {
            const connectRequestPromises = connectRequestsToCreate.map(requestData =>
              connectRequestService.createConnectRequest(requestData)
            );

            const connectRequestResults = await Promise.allSettled(connectRequestPromises);

            // Log results
            connectRequestResults.forEach((result, index) => {
              if (result.status === 'fulfilled') {
                console.log(`Connect request created for ${connectRequestsToCreate[index].email}`);
              } else {
                console.error(`Failed to create connect request for ${connectRequestsToCreate[index].email}:`, result.reason);
              }
            });
          } catch (error) {
            console.error('Error creating connect requests:', error);
            // Don't fail the event creation if connect request creation fails
          }
        }

        // Send event invitation emails to phone contacts (non-existing contacts)
        if (connectRequestsToCreate.length > 0) {
          console.log(`Sending event invitation emails to ${connectRequestsToCreate.length} phone contacts...`);

          try {
            // Extract email addresses from connect requests
            const phoneContactEmails = connectRequestsToCreate.map(request => request.email);

            // Send event invitation emails directly to these email addresses
            const invitationEmailResponse = await api.post(
              `/events/${eventId}/invite-emails`,
              { emailAddresses: phoneContactEmails },
              withAuth()
            );

            if (invitationEmailResponse.data) {
              console.log(`Event invitation emails sent to ${phoneContactEmails.length} phone contacts`);
            } else if (invitationEmailResponse.error) {
              console.error('Failed to send event invitation emails to phone contacts:', invitationEmailResponse.error);
            }
          } catch (error) {
            console.error('Error sending event invitation emails to phone contacts:', error);
            // Don't fail the event creation if email sending fails
          }
        }

        // Track event creation in Mixpanel (only for new events, not updates)
        if (!isEditing) {
          try {
            await mixpanelService.trackCreateEvent(
              eventData.name || eventTitle,
              eventData.payment_type || 'free',
              inviteeIds.length + connectRequestsToCreate.length // Include phone contacts in count
            );
          } catch (analyticsError) {
            console.error('Analytics tracking error:', analyticsError);
            // Don't fail the event creation if analytics fails
          }
        }

        // Call the onEventCreated callback if provided
        if (onEventCreated) {
          // If we're editing, use the existing event ID
          // If we're creating, use the new event ID
          const finalEventId = isEditing && eventId ? eventId : response.data.id;
          console.log(`Event ${isEditing ? 'updated' : 'created'} with ID: ${finalEventId}`);
          onEventCreated(finalEventId);
        }

        // Reset the form if the callback is provided
        // Always reset the form, even when editing
        if (onResetForm) {
          onResetForm();
        }

        // Continue to the next step
        onNext();
      }
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} event:`, error);
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'create'} event. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{isEditing ? 'UPDATE EVENT' : 'INVITE'}</Text>

      <ScrollView style={styles.content}>
        <Text style={styles.eventTitle}>{eventTitle}</Text>

        <View style={styles.inviteCount}>
          <Text style={styles.number}>{inviteeCount}</Text>
          <Text style={styles.label}>invited</Text>
        </View>

        <View style={styles.groupsSection}>
          <Text style={styles.groupsTitle}>ADD GROUPS</Text>

          {isLoadingGroups ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading groups...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : groups.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No groups found</Text>
            </View>
          ) : (
            <View style={styles.groupsGrid}>
              {groups.map((group) => (
                <TouchableOpacity
                  key={group.id}
                  style={styles.groupButton}
                  onPress={() => toggleGroup(group.name)}
                >
                  <View style={[styles.checkbox, selectedGroups.includes(group.name) ? styles.checked : {}]}>
                    {selectedGroups.includes(group.name) && <Check size={14} strokeWidth={3} color={Colors.black} />}
                  </View>
                  <Text style={styles.groupText}>{group.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        <Button
          variant="outline"
          style={styles.addContactsButton}
          onPress={() => setIsContactsModalOpen(true)}
          textStyle={{ color: Colors.white }}
        >
          Add Individual Contacts
        </Button>

        <Button
          variant="default"
          style={styles.postButton}
          textStyle={styles.postButtonText}
          onPress={handlePostEvent}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            isEditing ? 'UPDATE EVENT' : 'POST EVENT'
          )}
        </Button>
      </ScrollView>

      <ContactsModal
        open={isContactsModalOpen}
        onOpenChange={setIsContactsModalOpen}
        onContactsSelected={handleContactsSelected}
        initialSelectedContacts={selectedContacts}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: Colors.background,
  },
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 24,
    marginLeft: 20,
  },
  content: {
    paddingHorizontal: 20,
    flex: 1,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 0,
    marginBottom: 12,
    lineHeight: 28,
  },
  inviteCount: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 8,
    marginBottom: 24,
  },
  number: {
    fontSize: 28,
    fontWeight: '600',
    color: Colors.primary,
  },
  label: {
    fontSize: 24,
    color: Colors.white,
  },
  groupsSection: {
    marginBottom: 32,
  },
  groupsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 0,
    marginBottom: 16,
  },
  groupsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  groupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '48%',
    marginBottom: 16,
  },
  groupText: {
    fontSize: 16,
    color: Colors.white,
  },
  checkbox: {
    width: 15,
    height: 15,
    borderWidth: 1.5,
    borderColor: Colors.primary,
    borderRadius: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checked: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  addContactsButton: {
    height: 40,
    borderWidth: 1,
    borderColor: Colors.primary,
    color: Colors.primary,
    fontWeight: '500',
    alignSelf: 'center',
    marginBottom: 40,
    fontSize: 14,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  postButton: {
    backgroundColor: Colors.primary,
    color: Colors.black,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    alignSelf: 'center',
    marginBottom: 20,
    fontSize: 19,
    paddingVertical: 20,
    paddingHorizontal: 80,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 0,
    height: 60,
  },
  postButtonText: {
    color: Colors.black,
    fontWeight: '700',
  },
  // Loading and error states
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: '#ffeeee',
    borderRadius: 4,
    marginBottom: 16,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
  },
  emptyContainer: {
    padding: 20,
    backgroundColor: `${Colors.primary}1A`,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  emptyText: {
    color: Colors.white,
    fontSize: 14,
  },
});
