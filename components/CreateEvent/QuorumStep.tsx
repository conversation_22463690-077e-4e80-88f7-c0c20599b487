import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Text } from "@/components/Themed";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/components/ui/select';
import Colors from '@/constants/Colors';

interface QuorumStepProps {
  minPeople: string;
  maxPeople: string;
  onMinPeopleChange: (value: string) => void;
  onMaxPeopleChange: (value: string) => void;
}

export const QuorumStep: React.FC<QuorumStepProps> = ({
  minPeople,
  maxPeople,
  onMinPeopleChange,
  onMaxPeopleChange,
}) => {
  return (
    <View>
      <Text style={styles.title}>QUORUM</Text>
      <View style={styles.form}>
        <View style={styles.selectGroup}>
          <View style={styles.selectWrapper}>
            <Text style={styles.quorumQuestion}>
              What is the minimum number of people needed for the event to become <Text style={styles.emphasis}>official</Text>?
            </Text>
            <Select value={minPeople} onValueChange={onMinPeopleChange}>
              <SelectTrigger style={styles.selectTrigger}>
                <SelectValue placeholder="Select minimum" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 19 }, (_, i) => i + 2).map(num => (
                  <SelectItem key={num} value={num.toString()}>
                    {num.toString()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </View>

          <View style={styles.selectWrapper}>
            <Text style={styles.label}>Maximum</Text>
            <Select value={maxPeople} onValueChange={onMaxPeopleChange}>
              <SelectTrigger style={styles.selectTrigger}>
                <SelectValue placeholder="unlimited" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unlimited">unlimited</SelectItem>
                {Array.from({ length: 50 }, (_, i) => i + 1).map(num => (
                  <SelectItem key={num} value={num.toString()}>
                    {num.toString()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.primary,
    textShadowColor: Colors.primaryDark,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    marginTop: 20,
    marginBottom: 40,
    marginLeft: 20,
  },
  form: {
    flexDirection: 'column',
    paddingHorizontal: 20,
  },
  selectGroup: {
    flexDirection: 'column',
    gap: 16,
  },
  selectWrapper: {
    flexDirection: 'column',
    marginBottom: 32,
  },
  selectTrigger: {
    width: 160,
    // height: 80,
    borderColor: Colors.primary,
    padding: 12,
    paddingTop: Platform.OS === 'android' ? 3 : 6,
    borderRadius: 4,
    backgroundColor: Colors.background,
    fontSize: 16,
  },
  quorumQuestion: {
    fontSize: 16,
    color: Colors.white,
    lineHeight: 24,
    maxWidth: 300,
    marginBottom: 8,
  },
  emphasis: {
    fontWeight: '600',
  },
  label: {
    fontSize: 16,
    color: Colors.white,
    marginBottom: 8,
    fontWeight: '600',
  },
});