import React, { useEffect, useRef, useState } from 'react';
import { View, Image, StyleSheet, Animated, Dimensions } from 'react-native';
import Colors from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationComplete?: () => void;
  minimumDisplayTime?: number;
}

export default function SplashScreen({
  onAnimationComplete,
  minimumDisplayTime = 1500
}: SplashScreenProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const containerFadeAnim = useRef(new Animated.Value(1)).current;
  const [animationCompleted, setAnimationCompleted] = useState(false);
  const [minimumTimeElapsed, setMinimumTimeElapsed] = useState(false);
  const [shouldFadeOut, setShouldFadeOut] = useState(false);

  useEffect(() => {
    // Start the animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setAnimationCompleted(true);
    });

    // Set minimum display time
    const timer = setTimeout(() => {
      setMinimumTimeElapsed(true);
    }, minimumDisplayTime);

    return () => clearTimeout(timer);
  }, [fadeAnim, scaleAnim, minimumDisplayTime]);

  // Start fade out when both animation and minimum time are done
  useEffect(() => {
    if (animationCompleted && minimumTimeElapsed && !shouldFadeOut) {
      setShouldFadeOut(true);
    }
  }, [animationCompleted, minimumTimeElapsed, shouldFadeOut]);

  // Handle fade out animation
  useEffect(() => {
    if (shouldFadeOut) {
      Animated.timing(containerFadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        if (onAnimationComplete) {
          onAnimationComplete();
        }
      });
    }
  }, [shouldFadeOut, containerFadeAnim, onAnimationComplete]);

  return (
    <Animated.View style={[styles.container, { opacity: containerFadeAnim }]}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Image
          source={require('@/assets/images/splash-icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: width * 0.9, // 60% of screen width
    height: height * 0.45, // 30% of screen height
  },
});
