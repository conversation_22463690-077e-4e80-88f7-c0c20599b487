import React, { useEffect, useState } from 'react';
import Svg, { Path } from 'react-native-svg';
import { Image, View, StyleSheet } from 'react-native';
import { Text } from "@/components/Themed";
import { cloudfrontService } from '@/services/cloudfront';
import Colors from '@/constants/Colors';

interface IconProps {
  color: string;
  size?: number;
}

export const HomeIcon = ({ color, size = 27 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 23/27)} viewBox="0 0 27 23" fill="none">
      <Path
        d="M13.9967 0.468132C13.7155 0.233157 13.2889 0.233157 13.003 0.468132L0.252988 11.1085C-0.0563866 11.3657 -0.0891991 11.8179 0.187363 12.1105C0.463926 12.4031 0.937363 12.4341 1.24674 12.1726L2.99986 10.7095V19.4435C2.99986 21.4031 4.67799 22.9903 6.74986 22.9903H20.2499C22.3217 22.9903 23.9999 21.4031 23.9999 19.4435V10.7095L25.753 12.1726C26.0624 12.4297 26.5358 12.4031 26.8124 12.1105C27.0889 11.8179 27.0561 11.3701 26.7467 11.1085L13.9967 0.468132ZM4.49986 19.4435V9.45483L13.4999 1.94449L22.4999 9.45483V19.4435C22.4999 20.6184 21.4921 21.5716 20.2499 21.5716H17.2499V14.478C17.2499 13.6933 16.5796 13.0593 15.7499 13.0593H11.2499C10.4202 13.0593 9.74986 13.6933 9.74986 14.478V21.5716H6.74986C5.50768 21.5716 4.49986 20.6184 4.49986 19.4435ZM11.2499 21.5716V14.478H15.7499V21.5716H11.2499Z"
        fill={color}
      />
    </Svg>
  );
};

export const PeopleIcon = ({ color, size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 21/30)} viewBox="0 0 30 21" fill="none">
      <Path d="M4.125 5.3201C4.125 4.9009 4.2123 4.48581 4.38191 4.09853C4.55152 3.71124 4.80012 3.35934 5.11351 3.06293C5.42691 2.76651 5.79897 2.53138 6.20844 2.37096C6.61792 2.21054 7.05679 2.12798 7.5 2.12798C7.94321 2.12798 8.38208 2.21054 8.79156 2.37096C9.20103 2.53138 9.57309 2.76651 9.88649 3.06293C10.1999 3.35934 10.4485 3.71124 10.6181 4.09853C10.7877 4.48581 10.875 4.9009 10.875 5.3201C10.875 5.73929 10.7877 6.15438 10.6181 6.54167C10.4485 6.92895 10.1999 7.28085 9.88649 7.57726C9.57309 7.87368 9.20103 8.10881 8.79156 8.26923C8.38208 8.42965 7.94321 8.51221 7.5 8.51221C7.05679 8.51221 6.61792 8.42965 6.20844 8.26923C5.79897 8.10881 5.42691 7.87368 5.11351 7.57726C4.80012 7.28085 4.55152 6.92895 4.38191 6.54167C4.2123 6.15438 4.125 5.73929 4.125 5.3201ZM12.375 5.3201C12.375 4.09723 11.8614 2.92444 10.9471 2.05974C10.0329 1.19504 8.79293 0.709259 7.5 0.709259C6.20707 0.709259 4.96709 1.19504 4.05285 2.05974C3.13861 2.92444 2.625 4.09723 2.625 5.3201C2.625 6.54297 3.13861 7.71575 4.05285 8.58045C4.96709 9.44515 6.20707 9.93093 7.5 9.93093C8.79293 9.93093 10.0329 9.44515 10.9471 8.58045C11.8614 7.71575 12.375 6.54297 12.375 5.3201ZM19.125 5.3201C19.125 4.47349 19.4806 3.66157 20.1135 3.06293C20.7465 2.46429 21.6049 2.12798 22.5 2.12798C23.3951 2.12798 24.2535 2.46429 24.8865 3.06293C25.5194 3.66157 25.875 4.47349 25.875 5.3201C25.875 6.1667 25.5194 6.97863 24.8865 7.57726C24.2535 8.1759 23.3951 8.51221 22.5 8.51221C21.6049 8.51221 20.7465 8.1759 20.1135 7.57726C19.4806 6.97863 19.125 6.1667 19.125 5.3201ZM27.375 5.3201C27.375 4.09723 26.8614 2.92444 25.9471 2.05974C25.0329 1.19504 23.7929 0.709259 22.5 0.709259C21.2071 0.709259 19.9671 1.19504 19.0529 2.05974C18.1386 2.92444 17.625 4.09723 17.625 5.3201C17.625 6.54297 18.1386 7.71575 19.0529 8.58045C19.9671 9.44515 21.2071 9.93093 22.5 9.93093C23.7929 9.93093 25.0329 9.44515 25.9471 8.58045C26.8614 7.71575 27.375 6.54297 27.375 5.3201ZM9.75 13.4777C12.2344 13.4777 14.25 15.3841 14.25 17.7339V18.4432C14.25 18.8334 13.9125 19.1526 13.5 19.1526H2.25C1.8375 19.1526 1.5 18.8334 1.5 18.4432V17.7339C1.5 15.3841 3.51562 13.4777 6 13.4777H9.75ZM6 12.059C2.68594 12.059 0 14.5994 0 17.7339V18.4432C0 19.6181 1.00781 20.5713 2.25 20.5713H13.5C14.7422 20.5713 15.75 19.6181 15.75 18.4432V17.7339C15.75 16.2974 15.1875 14.9896 14.2594 13.992C13.1578 12.8083 11.5453 12.059 9.75 12.059H6ZM20.25 20.5713H24C27.3141 20.5713 30 18.0309 30 14.8965V14.1871C30 13.0122 28.9922 12.059 27.75 12.059H16.5C15.9047 12.059 15.3656 12.2763 14.9625 12.6354C15.3234 12.9634 15.6516 13.3314 15.9375 13.7216C16.0734 13.5708 16.275 13.4777 16.5047 13.4777H27.7547C28.1672 13.4777 28.5047 13.7969 28.5047 14.1871V14.8965C28.5047 17.2462 26.4891 19.1526 24.0047 19.1526H20.2547C19.1016 19.1526 18.0516 18.7447 17.2547 18.0708V18.4432C17.2547 18.8777 17.1703 19.2945 17.0203 19.6758C17.9531 20.2432 19.0641 20.5713 20.2547 20.5713H20.25Z" fill={color}/>
    </Svg>
  );
};

export const CreateEventIcon = ({ color, size = 21 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 23/21)} viewBox="0 0 21 23" fill="none">
      <Path d="M5.25 0.290527C5.6625 0.290527 6 0.609739 6 0.999887V3.12797H15V0.999887C15 0.609739 15.3375 0.290527 15.75 0.290527C16.1625 0.290527 16.5 0.609739 16.5 0.999887V3.12797H18C19.6547 3.12797 21 4.40038 21 5.9654V7.38412V8.80284V20.1526C21 21.7176 19.6547 22.99 18 22.99H3C1.34531 22.99 0 21.7176 0 20.1526V8.80284V7.38412V5.9654C0 4.40038 1.34531 3.12797 3 3.12797H4.5V0.999887C4.5 0.609739 4.8375 0.290527 5.25 0.290527ZM19.5 8.80284H1.5V20.1526C1.5 20.9373 2.17031 21.5713 3 21.5713H18C18.8297 21.5713 19.5 20.9373 19.5 20.1526V8.80284ZM18 4.54668H3C2.17031 4.54668 1.5 5.18068 1.5 5.9654V7.38412H19.5V5.9654C19.5 5.18068 18.8297 4.54668 18 4.54668ZM15 15.1871C15 15.5772 14.6625 15.8964 14.25 15.8964H11.25V18.7339C11.25 19.124 10.9125 19.4432 10.5 19.4432C10.0875 19.4432 9.75 19.124 9.75 18.7339V15.8964H6.75C6.3375 15.8964 6 15.5772 6 15.1871C6 14.7969 6.3375 14.4777 6.75 14.4777H9.75V11.6403C9.75 11.2501 10.0875 10.9309 10.5 10.9309C10.9125 10.9309 11.25 11.2501 11.25 11.6403V14.4777H14.25C14.6625 14.4777 15 14.7969 15 15.1871Z" fill={color}/>
    </Svg>
  );
};

interface NotificationsIconProps extends IconProps {
  unreadCount?: number;
}

export const NotificationsIcon = ({ color, size = 21, unreadCount = 0 }: NotificationsIconProps) => {
  return (
    <View style={styles.notificationIconContainer}>
      <Svg width={size} height={Math.floor(size * 25/21)} viewBox="0 0 21 25" fill="none">
        <Path d="M9.75 1.04053C9.75 0.628027 10.0875 0.290527 10.5 0.290527C10.9125 0.290527 11.25 0.628027 11.25 1.04053V1.82803C15.0422 2.20303 18 5.3999 18 9.29053V10.6546C18 12.703 18.8156 14.6671 20.2641 16.1202L20.3953 16.2515C20.7844 16.6405 21.0047 17.1702 21.0047 17.7187C21.0047 18.8671 20.0766 19.7952 18.9281 19.7952L2.07656 19.7905C0.928125 19.7905 0 18.8624 0 17.714C0 17.1655 0.220312 16.6358 0.609375 16.2468L0.740625 16.1155C2.18438 14.6671 3 12.703 3 10.6546V9.29053C3 5.3999 5.95781 2.20303 9.75 1.82803V1.04053ZM10.5 3.29053C7.18594 3.29053 4.5 5.97647 4.5 9.29053V10.6546C4.5 13.1015 3.52969 15.4499 1.79531 17.1796L1.66875 17.3062C1.56094 17.414 1.5 17.5593 1.5 17.714C1.5 18.0327 1.75781 18.2905 2.07656 18.2905H18.9234C19.2422 18.2905 19.5 18.0327 19.5 17.714C19.5 17.5593 19.4391 17.414 19.3312 17.3062L19.2 17.1749C17.4703 15.4452 16.4953 13.0968 16.4953 10.6499V9.28584C16.4953 5.97178 13.8094 3.28584 10.4953 3.28584L10.5 3.29053ZM9.08438 21.7921C9.29063 22.3733 9.84844 22.7905 10.5 22.7905C11.1516 22.7905 11.7094 22.3733 11.9156 21.7921C12.0516 21.403 12.4828 21.1968 12.8719 21.3327C13.2609 21.4687 13.4672 21.8999 13.3312 22.289C12.9187 23.4562 11.8078 24.2905 10.5 24.2905C9.19219 24.2905 8.08125 23.4562 7.66875 22.289C7.53281 21.8999 7.73437 21.4687 8.12812 21.3327C8.52187 21.1968 8.94844 21.3983 9.08438 21.7921Z" fill={color}/>
      </Svg>
      {unreadCount > 0 && (
        <View style={styles.badgeContainer}>
          {unreadCount < 10 ? (
            <Text style={styles.badgeText}>{unreadCount}</Text>
          ) : (
            <View style={styles.badgeContainer} />
          )}
        </View>
      )}
    </View>
  );
};

export const SettingsIcon = ({ color, size = 21 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 23/21)} viewBox="0 0 21 23" fill="none">
      <Path d="M15 5.9654C15 5.40648 14.8836 4.85302 14.6575 4.33664C14.4313 3.82026 14.0998 3.35107 13.682 2.95585C13.2641 2.56063 12.768 2.24712 12.2221 2.03323C11.6761 1.81934 11.0909 1.70925 10.5 1.70925C9.90905 1.70925 9.32389 1.81934 8.77792 2.03323C8.23196 2.24712 7.73588 2.56063 7.31802 2.95585C6.90016 3.35107 6.56869 3.82026 6.34254 4.33664C6.1164 4.85302 6 5.40648 6 5.9654C6 6.52433 6.1164 7.07778 6.34254 7.59416C6.56869 8.11055 6.90016 8.57974 7.31802 8.97496C7.73588 9.37018 8.23196 9.68369 8.77792 9.89758C9.32389 10.1115 9.90905 10.2216 10.5 10.2216C11.0909 10.2216 11.6761 10.1115 12.2221 9.89758C12.768 9.68369 13.2641 9.37018 13.682 8.97496C14.0998 8.57974 14.4313 8.11055 14.6575 7.59416C14.8836 7.07778 15 6.52433 15 5.9654ZM4.5 5.9654C4.5 4.46033 5.13214 3.01691 6.25736 1.95266C7.38258 0.888415 8.9087 0.290527 10.5 0.290527C12.0913 0.290527 13.6174 0.888415 14.7426 1.95266C15.8679 3.01691 16.5 4.46033 16.5 5.9654C16.5 7.47048 15.8679 8.9139 14.7426 9.97815C13.6174 11.0424 12.0913 11.6403 10.5 11.6403C8.9087 11.6403 7.38258 11.0424 6.25736 9.97815C5.13214 8.9139 4.5 7.47048 4.5 5.9654ZM1.5 21.5713H19.5C19.4438 18.0378 16.3969 15.1871 12.6422 15.1871H8.35781C4.60781 15.1871 1.56094 18.0378 1.5 21.5713ZM0 21.6733C0 17.3063 3.74063 13.7684 8.35781 13.7684H12.6422C17.2594 13.7684 21 17.3063 21 21.6733C21 22.4004 20.3766 22.99 19.6078 22.99H1.39219C0.623438 22.99 0 22.4004 0 21.6733Z" fill={color}/>
    </Svg>
  );
};

interface ProfileIconProps extends IconProps {
  profileImageUrl?: string;
}

export const ProfileIcon = ({ color, size = 21, profileImageUrl }: ProfileIconProps) => {
  const [signedImageUrl, setSignedImageUrl] = useState<string | undefined>(undefined);

  useEffect(() => {
    const getSignedUrl = async () => {
      if (profileImageUrl) {
        try {
          const signedUrl = await cloudfrontService.getSignedUrl(profileImageUrl);
          setSignedImageUrl(signedUrl);
        } catch (error) {
          console.error('Error signing profile image URL:', error);
          setSignedImageUrl(profileImageUrl);
        }
      }
    };

    getSignedUrl();
  }, [profileImageUrl]);

  if (signedImageUrl) {
    return (
      <View style={styles.profileIconContainer}>
        <Image
          source={{ uri: signedImageUrl }}
          style={[styles.profileImage, { width: size, height: size }]}
          resizeMode="cover"
        />
      </View>
    );
  }

  // Fallback to the settings icon if no profile image is available
  return <SettingsIcon color={color} size={size} />;
};

const styles = StyleSheet.create({
  profileIconContainer: {
    overflow: 'hidden',
    borderRadius: 100,
  },
  profileImage: {
    borderRadius: 100,
  },
  notificationIconContainer: {
    position: 'relative',
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeContainer: {
    position: 'absolute',
    top: 0,  // Moved down by 3 pixels from -3
    right: 0, // Moved left by 3 pixels from -3
    backgroundColor: '#FFFF00',
    borderRadius: 6,
    width: 12,
    height: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: Colors.background,
    fontSize: 9,
    fontWeight: 'normal',
    textAlign: 'center',
    lineHeight: 12,
  },
});
