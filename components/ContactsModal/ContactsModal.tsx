import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet, Pressable, ActivityIndicator } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Svg, Path } from 'react-native-svg';
import { useContactsStore, PhoneContact } from '@/stores/contacts';
import { useApiContactsStore } from '@/stores/apiContacts';
import { connectRequestService, ConnectRequestResponse } from '@/services/connectRequest';
import { Check } from 'lucide-react-native';
import Colors from '@/constants/Colors';
import { AvatarIcon } from '@/assets/icons';

// Combined contact type for display
interface DisplayContact {
  id: string;
  name: string;
  email: string;
  isExistingContact: boolean; // true if already in user's contacts, false if phone contact only
  phoneContact?: PhoneContact; // original phone contact data if applicable
}

// Helper function to format contact name from phone contact
const formatPhoneContactName = (contact: PhoneContact): string => {
  return contact.name || 'Unknown';
};

interface ContactsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContactsSelected: (selectedContacts: DisplayContact[]) => void;
  initialSelectedContacts?: string[]; // Pre-selected contact IDs
}

export const ContactsModal: React.FC<ContactsModalProps> = ({
  open,
  onOpenChange,
  onContactsSelected,
  initialSelectedContacts = [],
}) => {
  const { deviceContacts, isLoading: isLoadingPhoneContacts, fetchContacts: fetchPhoneContacts, checkPermission } = useContactsStore();
  const { contacts: apiContacts, fetchContacts: fetchApiContacts } = useApiContactsStore();
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [displayContacts, setDisplayContacts] = useState<DisplayContact[]>([]);
  const [pendingConnectRequests, setPendingConnectRequests] = useState<ConnectRequestResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all necessary data when the modal opens
  useEffect(() => {
    if (open) {
      loadContactsData();
    }
  }, [open]);

  const loadContactsData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check and request permission for phone contacts first
      await checkPermission();

      // Fetch phone contacts, API contacts, and pending connect requests in parallel
      await Promise.all([
        fetchPhoneContacts(),
        fetchApiContacts(),
        fetchPendingConnectRequests()
      ]);
    } catch (err) {
      console.error('Error loading contacts data:', err);
      setError('Failed to load contacts. Please ensure contacts permission is granted.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPendingConnectRequests = async () => {
    try {
      const response = await connectRequestService.getAllConnectRequests();
      if (response.data) {
        // Filter to only include pending requests (not accepted or declined)
        const pending = response.data.filter(req => !req.is_accepted && !req.is_declined);
        setPendingConnectRequests(pending);
      }
    } catch (error) {
      console.error('Error fetching pending connect requests:', error);
    }
  };

  // Process and combine contacts when data changes
  useEffect(() => {
    if (!deviceContacts || !apiContacts) return;

    const processedContacts: DisplayContact[] = [];
    const pendingRequestEmails = new Set(pendingConnectRequests.map(req => req.email).filter(Boolean));

    // Get phone contacts that have email addresses
    const phoneContactsWithEmails = deviceContacts.filter(contact =>
      contact.emails && contact.emails.length > 0
    );

    phoneContactsWithEmails.forEach(phoneContact => {
      // For each email in the phone contact
      phoneContact.emails?.forEach(emailObj => {
        const email = emailObj.email;
        if (!email) return;

        // Skip if there's already a pending connect request for this email
        if (pendingRequestEmails.has(email)) return;

        // Check if this email belongs to an existing API contact
        const existingApiContact = apiContacts.find(apiContact =>
          apiContact.email?.toLowerCase() === email.toLowerCase() && !apiContact.is_blocked
        );

        if (existingApiContact) {
          // This is an existing contact
          processedContacts.push({
            id: `api-${existingApiContact.id}`,
            name: `${existingApiContact.first_name} ${existingApiContact.last_name || ''}`.trim(),
            email: email,
            isExistingContact: true
          });
        } else {
          // This is a phone contact only (not yet in user's contacts)
          processedContacts.push({
            id: `phone-${phoneContact.id}-${email}`,
            name: formatPhoneContactName(phoneContact),
            email: email,
            isExistingContact: false,
            phoneContact: phoneContact
          });
        }
      });
    });

    // Remove duplicates based on email (keep the first occurrence)
    const uniqueContacts = processedContacts.filter((contact, index, self) =>
      index === self.findIndex(c => c.email.toLowerCase() === contact.email.toLowerCase())
    );

    // Sort contacts alphabetically by name
    uniqueContacts.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));

    setDisplayContacts(uniqueContacts);
  }, [deviceContacts, apiContacts, pendingConnectRequests]);

  // Filter contacts based on search query
  const filteredContacts = displayContacts.filter(contact => {
    return contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           contact.email.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const toggleContact = (contactId: string) => {
    setSelectedContacts(prev =>
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  // Initialize selected contacts with initial values when modal opens
  useEffect(() => {
    if (open) {
      setSelectedContacts(initialSelectedContacts);
    } else {
      setSelectedContacts([]);
    }
  }, [open, initialSelectedContacts]);

  const handleDone = () => {
    // Get the selected contact objects
    const selectedContactObjects = displayContacts.filter(contact =>
      selectedContacts.includes(contact.id)
    );
    onContactsSelected(selectedContactObjects);
    onOpenChange(false);
  };

  const renderContactItem = ({ item }: { item: DisplayContact }) => {
    const contactId = item.id;

    return (
      <Pressable
        key={contactId}
        style={styles.contactItem}
        onPress={() => toggleContact(contactId)}
      >
        <TouchableOpacity
          style={[
            styles.checkbox,
            selectedContacts.includes(contactId) && styles.checkboxChecked
          ]}
          onPress={() => toggleContact(contactId)}
        >
          {selectedContacts.includes(contactId) && (
            <Check size={14} strokeWidth={3} color={Colors.black} />
          )}
        </TouchableOpacity>
        <AvatarIcon size={18} color={"#ABD4DD"} />
        {/* <Svg width={18} height={18} viewBox="0 0 11 18" fill="none" style={styles.avatar}>
          <Path
            d="M5.5 10.818C5.5 10.818 4.5924 10.818 3.34233 11.1223C-1.11897 12.2082 0.681981 17.6825 5.27271 17.7697C5.34845 17.7711 5.42422 17.7722 5.5 17.773C5.57578 17.7722 5.65155 17.7711 5.72729 17.7697C10.318 17.6825 12.119 12.2082 7.65767 11.1223C6.4076 10.818 5.50001 10.818 5.5 10.818ZM5.5 10.045C7.818 10.045 9.364 7.727 9.364 3.863C9.364 0.0102766 5.52252 2.70816e-05 5.5001 7.08092e-08C5.49941 -7.60687e-07 5.50059 -9.13743e-07 5.4999 9.62835e-08C5.47748 3.29037e-05 1.636 0.0112756 1.636 3.864C1.636 7.728 3.182 10.045 5.5 10.045Z"
            fill="#ABD4DD"
          />
        </Svg> */}
        <View style={styles.contactDetails}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.email}>{item.email}</Text>
          {/* {!item.isExistingContact && (
            <Text style={styles.newContactLabel}>Will send connect request</Text>
          )} */}
        </View>
      </Pressable>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0">
        <View style={styles.modal}>
          <DialogHeader>
            <DialogTitle style={styles.title}>Add Individual Contacts</DialogTitle>
          </DialogHeader>

          <View style={styles.searchContainer}>
            <TextInput
              placeholder="Start typing a friend's name"
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#999999"
            />
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading contacts...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <Button
                onPress={loadContactsData}
                style={styles.retryButton}
              >
                <Text style={styles.retryButtonText}>Retry</Text>
              </Button>
            </View>
          ) : filteredContacts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {searchQuery ? 'No contacts found matching your search' : 'No contacts with email addresses found'}
              </Text>
            </View>
          ) : (
            <FlatList
              data={filteredContacts}
              renderItem={renderContactItem}
              keyExtractor={(item) => item.id}
              style={styles.contactsList}
              contentContainerStyle={styles.contactsListContent}
            />
          )}

          <View style={styles.buttonContainer}>
            <Button
              onPress={handleDone}
              style={styles.doneButton}
            >
              <Text style={styles.doneButtonText}>DONE</Text>
            </Button>
          </View>
        </View>
      </DialogContent>
    </Dialog>
  );
};

const styles = StyleSheet.create({
  modal: {
    width: '100%',
    maxHeight: 500,
    padding: 20,
    backgroundColor: Colors.background,
    borderRadius: 0,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.white,
    textAlign: 'left',
    marginBottom: 12,
  },
  searchContainer: {
    marginBottom: 12,
  },
  searchInput: {
    width: '100%',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    fontSize: 16,
    color: Colors.white,
  },
  contactsList: {
    maxHeight: 300,
  },
  contactsListContent: {
    paddingVertical: 5,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
  },
  checkbox: {
    width: 15,
    height: 15,
    borderWidth: 1.5,
    borderColor: Colors.primary,
    borderRadius: 2,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
  },
  avatar: {
    marginRight: 10,
  },
  name: {
    fontSize: 16,
    color: Colors.white,
  },
  email: {
    fontSize: 12,
    color: '#999999',
    marginTop: 2,
  },
  newContactLabel: {
    fontSize: 11,
    color: Colors.primary,
    marginTop: 2,
    fontStyle: 'italic',
  },
  contactDetails: {
    flex: 1,
    justifyContent: 'center',
    marginLeft: 10
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    height: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  errorContainer: {
    padding: 20,
    backgroundColor: '#ffeeee',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    height: 200,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#d32f2f',
    borderRadius: 4,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  retryButtonText: {
    color: '#d32f2f',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 20,
    backgroundColor: `${Colors.primary}1A`,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    height: 200,
  },
  emptyText: {
    color: Colors.white,
    fontSize: 14,
    textAlign: 'center',
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  doneButton: {
    backgroundColor: Colors.primary,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 4,
    paddingHorizontal: 26,
    paddingVertical: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  doneButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '500',
  }
});