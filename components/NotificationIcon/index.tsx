import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { <PERSON>, G, Defs, ClipPath, Rect } from 'react-native-svg';

export type NotificationIconType = 'calendar-x' | 'money' | 'calendar-check' | 'calendar-star' | 'message';

interface NotificationIconProps {
  type: NotificationIconType;
}

export const NotificationIcon: React.FC<NotificationIconProps> = ({ type }) => {
  switch (type) {
    case 'calendar-x':
      return (
        <Svg width="30" height="35" viewBox="0 0 30 35" fill="none">
          <G clipPath="url(#clip0_50_2718)">
            <Path d="M8.57143 0C9.46205 0 10.1786 0.731445 10.1786 1.64062V4.375H19.8214V1.64062C19.8214 0.731445 20.5379 0 21.4286 0C22.3192 0 23.0357 0.731445 23.0357 1.64062V4.375H25.7143C28.0781 4.375 30 6.33691 30 8.75V9.84375V13.125V30.625C30 33.0381 28.0781 35 25.7143 35H4.28571C1.92188 35 0 33.0381 0 30.625V13.125V9.84375V8.75C0 6.33691 1.92188 4.375 4.28571 4.375H6.96429V1.64062C6.96429 0.731445 7.6808 0 8.57143 0ZM26.7857 13.125H3.21429V30.625C3.21429 31.2266 3.69643 31.7188 4.28571 31.7188H25.7143C26.3036 31.7188 26.7857 31.2266 26.7857 30.625V13.125ZM20.4241 19.209L17.2768 22.4219L20.4241 25.6348C21.0536 26.2773 21.0536 27.3164 20.4241 27.9521C19.7946 28.5879 18.7768 28.5947 18.154 27.9521L15.0067 24.7393L11.8594 27.9521C11.2299 28.5947 10.2121 28.5947 9.58928 27.9521C8.96652 27.3096 8.95982 26.2705 9.58928 25.6348L12.7366 22.4219L9.58928 19.209C8.95982 18.5664 8.95982 17.5273 9.58928 16.8916C10.2187 16.2559 11.2366 16.249 11.8594 16.8916L15.0067 20.1045L18.154 16.8916C18.7835 16.249 19.8013 16.249 20.4241 16.8916C21.0469 17.5342 21.0536 18.5732 20.4241 19.209Z" fill="#E35D5D"/>
          </G>
          <Defs>
            <ClipPath id="clip0_50_2718">
              <Rect width="30" height="35" fill="white"/>
            </ClipPath>
          </Defs>
        </Svg>
      );
    case 'money':
      return (
        <Svg width="30" height="30" viewBox="0 0 30 30" fill="none">
          <G clipPath="url(#clip0_50_2725)">
            <Path d="M27.1875 15C27.1875 11.7677 25.9035 8.66774 23.6179 6.38214C21.3323 4.09654 18.2323 2.8125 15 2.8125C11.7677 2.8125 8.66774 4.09654 6.38214 6.38214C4.09654 8.66774 2.8125 11.7677 2.8125 15C2.8125 18.2323 4.09654 21.3323 6.38214 23.6179C8.66774 25.9035 11.7677 27.1875 15 27.1875C18.2323 27.1875 21.3323 25.9035 23.6179 23.6179C25.9035 21.3323 27.1875 18.2323 27.1875 15ZM0 15C0 11.0218 1.58035 7.20644 4.3934 4.3934C7.20644 1.58035 11.0218 0 15 0C18.9782 0 22.7936 1.58035 25.6066 4.3934C28.4196 7.20644 30 11.0218 30 15C30 18.9782 28.4196 22.7936 25.6066 25.6066C22.7936 28.4196 18.9782 30 15 30C11.0218 30 7.20644 28.4196 4.3934 25.6066C1.58035 22.7936 0 18.9782 0 15ZM16.2188 7.82813V8.66016C16.7871 8.73047 17.3555 8.88867 17.918 9.04688C18.0293 9.07617 18.1348 9.10547 18.2461 9.14062C18.9199 9.32812 19.3184 10.0254 19.1309 10.6992C18.9434 11.373 18.2461 11.7656 17.5723 11.584C17.4785 11.5605 17.3906 11.5312 17.2969 11.5078C16.8867 11.3906 16.4766 11.2793 16.0605 11.1973C15.2871 11.0508 14.3906 11.1211 13.6699 11.4316C13.0254 11.7129 12.4922 12.3926 13.2246 12.8613C13.7988 13.2305 14.502 13.418 15.1699 13.5996C15.3105 13.6348 15.4453 13.6758 15.5801 13.7109C16.4941 13.9688 17.6602 14.3027 18.5332 14.9004C19.6699 15.6797 20.2031 16.9453 19.9512 18.3047C19.7109 19.6172 18.7969 20.4785 17.7012 20.9238C17.2441 21.1113 16.7461 21.2285 16.2246 21.2871V22.1777C16.2246 22.875 15.6562 23.4434 14.959 23.4434C14.2617 23.4434 13.6934 22.875 13.6934 22.1777V21.1582C12.8438 20.9648 12.0117 20.6953 11.1855 20.4258C10.5234 20.209 10.1602 19.4883 10.3828 18.8262C10.6055 18.1641 11.3203 17.8008 11.9824 18.0234C12.1289 18.0703 12.2754 18.123 12.4219 18.1699C13.084 18.3926 13.7637 18.6211 14.4434 18.7324C15.4395 18.8789 16.2363 18.791 16.7578 18.5801C17.4609 18.2988 17.7949 17.4609 17.1035 16.9922C16.5117 16.5879 15.7793 16.3887 15.082 16.2012C14.9473 16.166 14.8184 16.1309 14.6836 16.0898C13.7988 15.8379 12.6914 15.5273 11.8594 14.9941C10.7168 14.2617 10.1367 13.043 10.3828 11.6895C10.6172 10.4121 11.6133 9.5625 12.668 9.10547C12.9902 8.96484 13.3359 8.85352 13.6934 8.77148V7.82813C13.6934 7.13086 14.2617 6.5625 14.959 6.5625C15.6562 6.5625 16.2246 7.13086 16.2246 7.82813H16.2188Z" fill="#32DBBD"/>
          </G>
          <Defs>
            <ClipPath id="clip0_50_2725">
              <Rect width="30" height="30" fill="white"/>
            </ClipPath>
          </Defs>
        </Svg>
      );
    case 'calendar-check':
      return (
        <Svg width="30" height="34" viewBox="0 0 30 34" fill="none">
          <G clipPath="url(#clip0_50_2740)">
            <Path d="M8.57143 0C9.46205 0 10.1786 0.710547 10.1786 1.59375V4.25H19.8214V1.59375C19.8214 0.710547 20.5379 0 21.4286 0C22.3192 0 23.0357 0.710547 23.0357 1.59375V4.25H25.7143C28.0781 4.25 30 6.15586 30 8.5V9.5625V12.75V29.75C30 32.0941 28.0781 34 25.7143 34H4.28571C1.92188 34 0 32.0941 0 29.75V12.75V9.5625V8.5C0 6.15586 1.92188 4.25 4.28571 4.25H6.96429V1.59375C6.96429 0.710547 7.6808 0 8.57143 0ZM26.7857 12.75H3.21429V29.75C3.21429 30.3344 3.69643 30.8125 4.28571 30.8125H25.7143C26.3036 30.8125 26.7857 30.3344 26.7857 29.75V12.75ZM22.0312 19.7227L14.5312 27.1602C13.9018 27.7844 12.8839 27.7844 12.2612 27.1602L7.97545 22.9102C7.34598 22.2859 7.34598 21.2766 7.97545 20.659C8.60491 20.0414 9.62277 20.0348 10.2455 20.659L13.3929 23.7801L19.7545 17.4715C20.3839 16.8473 21.4018 16.8473 22.0246 17.4715C22.6473 18.0957 22.654 19.1051 22.0246 19.7227H22.0312Z" fill="#3257DB"/>
          </G>
          <Defs>
            <ClipPath id="clip0_50_2740">
              <Rect width="30" height="34" fill="white"/>
            </ClipPath>
          </Defs>
        </Svg>
      );
    case 'calendar-star':
      return (
        <Svg width="30" height="34" viewBox="0 0 30 34" fill="none">
          <G clipPath="url(#clip0_46_682)">
            <Path d="M8.57143 0C9.46205 0 10.1786 0.710547 10.1786 1.59375V4.25H19.8214V1.59375C19.8214 0.710547 20.5379 0 21.4286 0C22.3192 0 23.0357 0.710547 23.0357 1.59375V4.25H25.7143C28.0781 4.25 30 6.15586 30 8.5V9.5625V12.75V29.75C30 32.0941 28.0781 34 25.7143 34H4.28571C1.92188 34 0 32.0941 0 29.75V12.75V9.5625V8.5C0 6.15586 1.92188 4.25 4.28571 4.25H6.96429V1.59375C6.96429 0.710547 7.6808 0 8.57143 0ZM26.7857 12.75H3.21429V29.75C3.21429 30.3344 3.69643 30.8125 4.28571 30.8125H25.7143C26.3036 30.8125 26.7857 30.3344 26.7857 29.75V12.75ZM15.6228 16.1301L17.2031 19.3043L20.7321 19.8156C21.3013 19.8953 21.529 20.5926 21.1205 20.991L18.5625 23.4613L19.1652 26.9477C19.2656 27.5121 18.6629 27.9437 18.154 27.6781L15 26.0312L11.8393 27.6781C11.3304 27.9437 10.7344 27.5121 10.8281 26.9477L11.4308 23.4613L8.87277 20.991C8.45759 20.5926 8.68527 19.8953 9.26116 19.8156L12.7902 19.3043L14.3705 16.1301C14.625 15.6187 15.3616 15.6187 15.6228 16.1301Z" fill="#3257DB"/>
          </G>
          <Defs>
            <ClipPath id="clip0_46_682">
              <Rect width="30" height="34" fill="white"/>
            </ClipPath>
          </Defs>
        </Svg>
      );
    case 'message':
      return (
        <Svg width="30" height="30" viewBox="0 0 30 30" fill="none">
          <G clipPath="url(#clip0_50_2743)">
            <Path d="M7.24222 22.9277C7.99808 22.377 8.97659 22.2363 9.8555 22.5527C11.4082 23.1152 13.1485 23.4375 15 23.4375C22.3067 23.4375 27.1875 18.7207 27.1875 14.0625C27.1875 9.4043 22.3067 4.6875 15 4.6875C7.69339 4.6875 2.81253 9.4043 2.81253 14.0625C2.81253 15.9375 3.53909 17.7422 4.90433 19.2891C5.40823 19.8574 5.65433 20.6074 5.59573 21.3691C5.5137 22.4297 5.26175 23.4023 4.93362 24.2637C5.92972 23.8008 6.75589 23.2852 7.24222 22.9336V22.9277ZM1.24222 25.3066C1.34768 25.1484 1.44729 24.9902 1.54104 24.832C2.12698 23.8594 2.68362 22.582 2.79495 21.1465C1.03714 19.1484 2.86186e-05 16.7051 2.86186e-05 14.0625C2.86186e-05 7.33008 6.71487 1.875 15 1.875C23.2852 1.875 30 7.33008 30 14.0625C30 20.7949 23.2852 26.25 15 26.25C12.8262 26.25 10.7637 25.875 8.90042 25.2012C8.20315 25.7109 7.06643 26.4082 5.71878 26.9941C4.83401 27.3809 3.8262 27.7324 2.78323 27.9375C2.73636 27.9492 2.68948 27.9551 2.64261 27.9668C2.38479 28.0137 2.13284 28.0547 1.86917 28.0781C1.85745 28.0781 1.83987 28.084 1.82815 28.084C1.52933 28.1133 1.2305 28.1309 0.931669 28.1309C0.55081 28.1309 0.210966 27.9023 0.0644818 27.5508C-0.0820026 27.1992 2.86065e-05 26.8008 0.2637 26.5312C0.503935 26.2852 0.720732 26.0215 0.92581 25.7402C1.02542 25.6055 1.11917 25.4707 1.20706 25.3359L1.22464 25.3066H1.24222Z" fill="#328DDB"/>
          </G>
          <Defs>
            <ClipPath id="clip0_50_2743">
              <Rect width="30" height="30" fill="white"/>
            </ClipPath>
          </Defs>
        </Svg>
      );
  }
};