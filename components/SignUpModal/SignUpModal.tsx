import React, { useState, useEffect } from 'react';
import { Modal, View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from "@/components/Themed";
import { Svg, Path } from 'react-native-svg';
import Colors from '@/constants/Colors';

interface DateOption {
  date: string;
  time: string;
  selected?: boolean;
}

interface SignUpModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  eventTitle: string;
  dates: DateOption[];
  onDone: (selectedDates: DateOption[]) => void;
  showQuorum?: boolean;
  multipleDatesAllowed?: boolean; // Whether users can select multiple dates
}

// Check icon component replacement for lucide-react Check
const CheckIcon = () => (
  <Svg width={16} height={16} viewBox="0 0 24 24" fill="none" stroke={Colors.black} strokeWidth={2} strokeLinecap="round" strokeLinejoin="round">
    <Path d="M20 6L9 17l-5-5" />
  </Svg>
);

export const SignUpModal: React.FC<SignUpModalProps> = ({
  open,
  onOpenChange,
  eventTitle,
  dates,
  onDone,
  showQuorum = true,
  multipleDatesAllowed = true,
}) => {
  // Initialize with the dates from props and update when props change
  const [selectedDates, setSelectedDates] = useState<DateOption[]>([]);

  // Update selectedDates when dates prop changes or when modal opens
  useEffect(() => {
    if (open && dates.length > 0) {
      // If there's only one date, automatically select it and call onDone
      if (dates.length === 1) {
        const selectedDate = { ...dates[0], selected: true };
        onDone([selectedDate]);
        onOpenChange(false);
        return;
      }

      // Create a new array with all dates and set selected to false
      const newDates = dates.map(date => ({
        ...date,
        selected: false
      }));
      console.log('Setting selectedDates in modal to:', newDates);
      setSelectedDates(newDates);
    }
  }, [dates, open, onDone, onOpenChange]);

  const toggleDate = (index: number) => {
    setSelectedDates(prev => {
      // If multiple dates are not allowed, deselect all other dates first
      if (!multipleDatesAllowed) {
        // If the date is already selected, just toggle it off
        if (prev[index].selected) {
          return prev.map((date, i) =>
            i === index ? { ...date, selected: false } : date
          );
        }
        // Otherwise, deselect all and select only this one
        return prev.map((date, i) =>
          i === index ? { ...date, selected: true } : { ...date, selected: false }
        );
      }

      // If multiple dates are allowed, just toggle the selected date
      return prev.map((date, i) =>
        i === index ? { ...date, selected: !date.selected } : date
      );
    });
  };

  const handleDone = () => {
    onDone(selectedDates);
    onOpenChange(false);
  };

  return (
    <Modal
      visible={open}
      transparent={true}
      animationType="fade"
      onRequestClose={() => onOpenChange(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Text style={styles.title}>{eventTitle}</Text>
          </View>

          <View style={styles.content}>
            <Text style={styles.subtitle}>Which dates work for you?</Text>

            <View style={styles.datesList}>
              {selectedDates.map((date, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dateItem}
                  onPress={() => toggleDate(index)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.checkbox, date.selected && styles.checkedBox]}>
                    {date.selected && <CheckIcon />}
                  </View>
                  <View style={styles.dateInfo}>
                    <Text style={styles.date}>{date.date}</Text>
                    <Text style={styles.time}>{date.time}</Text>
                    {showQuorum && index === 0 && (
                      <View style={styles.quorumBadge}>
                        <Text style={styles.quorumText}>+3 TO QUORUM</Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.doneButtonContainer}>
              <TouchableOpacity
                style={styles.doneButton}
                onPress={handleDone}
              >
                <Text style={styles.doneButtonText}>DONE</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modal: {
    width: '90%',
    maxWidth: 335,
    backgroundColor: Colors.background,
    borderRadius: 8,
    overflow: 'hidden',
  },
  header: {
    alignItems: 'center',
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.white,
    textAlign: 'center',
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.white,
    marginBottom: 24,
  },
  datesList: {
    marginBottom: 24,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
    marginTop: 3,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: Colors.primary,
  },
  dateInfo: {
    flex: 1,
  },
  date: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 4,
  },
  time: {
    fontSize: 16,
    color: Colors.white,
  },
  quorumBadge: {
    backgroundColor: Colors.error,
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 8,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  quorumText: {
    color: Colors.white,
    fontSize: 13,
    fontWeight: '500',
  },
  doneButtonContainer: {
    alignItems: 'center',
  },
  doneButton: {
    height: 45,
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
    paddingHorizontal: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  doneButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '700',
  },
});