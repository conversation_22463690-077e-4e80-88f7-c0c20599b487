import React from "react";
import { Bell } from "lucide-react-native";
import { Button } from "../ui/button";
import styles from "./Navigation.module.css";
import { Image, View } from "react-native";
import { useRouter } from "expo-router";

export const Navigation = (): JSX.Element => {
  const { navigate } = useRouter();

  return (
    <View className={styles.navigation}>
      <Button 
        variant="ghost" 
        size="icon" 
        className={styles.navButton}
        onClick={() => navigate('/')}
      >
        <Image
          className="w-[27px] h-[23px]"
          alt="Home"
          src="/home-icon.svg"
        />
      </Button>

      <Button 
        variant="ghost" 
        size="icon" 
        className={styles.navButton}
        onClick={() => navigate('/people')}
      >
        <Image
          className="w-[30px] h-[23px]"
          alt="People"
          src="/friends-icon.svg"
        />
      </Button>

      <Button 
        variant="ghost" 
        size="icon" 
        className={styles.navButton}
        onClick={() => navigate('/calendar/create')}
      >
        <Image
          className={styles.navIcon}
          alt="Messages"
          src="/messages-icon.svg"
        />
      </Button>

      <Button 
        variant="ghost" 
        size="icon" 
        className={styles.navButton}
        onClick={() => navigate('/notifications')}
      >
        <Bell className="w-[21px] h-[23px] text-[#FFFF00]" />
      </Button>

      <Button 
        variant="ghost" 
        size="icon" 
        className={styles.navButton}
        onClick={() => navigate('/settings')}
      >
        <Image
          className={styles.navIcon}
          alt="Settings"
          src="/profile-icon.svg"
        />
      </Button>
    </View>
  );
};