import { PhoneContact } from '@/stores/contacts';
import { ContactResponse } from '@/services/contact';

/**
 * Filters out phone contacts that have the same email as any of the user's actual contacts
 * @param deviceContacts Array of device contacts to filter
 * @param userContacts Array of user's actual contacts from the database
 * @returns Object containing filtered contacts and count of filtered out contacts
 */
export const filterContactsByEmail = (
  deviceContacts: PhoneContact[],
  userContacts: ContactResponse[]
): { 
  filteredContacts: PhoneContact[],
  filteredOutCount: number,
  processedContacts: PhoneContact[]
} => {
  // Create a set of emails from user's actual contacts for faster lookup
  // Make sure to normalize emails (lowercase, trim whitespace)
  const userContactEmails = new Set();
  userContacts.forEach(contact => {
    if (contact.email) {
      const normalizedEmail = contact.email.toLowerCase().trim();
      userContactEmails.add(normalizedEmail);
    }
  });

  console.log(`Number of unique user contact emails: ${userContactEmails.size}`);

  // Mark contacts that have the same email as any of the user's actual contacts
  const processedContacts = deviceContacts.map(contact => {
    // If the contact has no emails, keep it as is
    if (!contact.emails || contact.emails.length === 0) {
      return { ...contact, hasMatchingEmail: false };
    }

    // Normalize and filter the contact's emails
    const contactEmails = [];
    contact.emails.forEach(emailObj => {
      if (emailObj.email) {
        const normalizedEmail = emailObj.email.toLowerCase().trim();
        contactEmails.push(normalizedEmail);
      }
    });

    // If no valid emails after normalization, keep the contact
    if (contactEmails.length === 0) {
      return { ...contact, hasMatchingEmail: false };
    }

    // Check if any of the contact's emails match any of the user's actual contact emails
    let hasMatchingEmail = false;
    for (const email of contactEmails) {
      if (userContactEmails.has(email)) {
        hasMatchingEmail = true;
        console.log(`Contact has matching email: ${contact.name} with email: ${email}`);
        break;
      }
    }

    return { ...contact, hasMatchingEmail };
  });

  // Filter out contacts with matching emails
  const filteredContacts = processedContacts.filter(contact => !contact.hasMatchingEmail);

  // Count how many contacts were filtered out due to matching emails
  const filteredOutCount = processedContacts.length - filteredContacts.length;

  if (filteredOutCount > 0) {
    console.log(`Filtered out ${filteredOutCount} contacts with matching emails`);
  }

  return {
    filteredContacts,
    filteredOutCount,
    processedContacts
  };
};
