import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator, ScrollView, Alert } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { StyleSheet } from 'react-native';
import { Search, Plus } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import Svg, { Path } from 'react-native-svg';
import { Image } from 'react-native';
import { useContactsStore, PhoneContact } from '@/stores/contacts';
import { connectRequestService } from '@/services/connectRequest';
import { contactService, ContactResponse } from '@/services/contact';
import { filterContactsByEmail } from './contactFilterUtils';
import Colors from '@/constants/Colors';

export const EmailTab = () => {
  const { deviceContacts, isLoading, toggleAdded, error } = useContactsStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredContacts, setFilteredContacts] = useState<PhoneContact[]>([]);
  const [addingContact, setAddingContact] = useState<string | null>(null);
  const [userContacts, setUserContacts] = useState<ContactResponse[]>([]);
  const [loadingUserContacts, setLoadingUserContacts] = useState(false);
  // We still track filtered out count for debugging purposes
  const [, setFilteredOutCount] = useState(0);

  // Fetch user's actual contacts
  useEffect(() => {
    const fetchUserContacts = async () => {
      try {
        setLoadingUserContacts(true);
        const response = await contactService.getAllContacts();
        if (response.data) {
          setUserContacts(response.data);
          console.log(`Fetched ${response.data.length} user contacts for EmailTab`);
        } else {
          console.log('No contacts data returned from API in EmailTab');
        }
      } catch (error) {
        console.error('Error fetching user contacts in EmailTab:', error);
      } finally {
        setLoadingUserContacts(false);
      }
    };

    fetchUserContacts();
  }, []);

  useEffect(() => {
    // Filter contacts based on search query and only include those with emails
    const contactsWithEmails = deviceContacts.filter(contact =>
      contact.emails && contact.emails.length > 0
    );

    console.log(`Total email contacts: ${contactsWithEmails.length}`);

    // Filter out contacts with matching emails
    const { filteredContacts: emailsWithoutMatches, filteredOutCount } = filterContactsByEmail(
      contactsWithEmails,
      userContacts
    );

    setFilteredOutCount(filteredOutCount);

    // Apply search filter if needed
    if (searchQuery.trim() === '') {
      setFilteredContacts(emailsWithoutMatches);
    } else {
      const query = searchQuery.toLowerCase();
      const searchFiltered = emailsWithoutMatches.filter(contact =>
        contact.name.toLowerCase().includes(query) ||
        contact.emails?.some(email => email.email.toLowerCase().includes(query))
      );
      setFilteredContacts(searchFiltered);
    }
  }, [deviceContacts, searchQuery, userContacts]);

  useEffect(() => {
    // Show error if there is one
    if (error) {
      Alert.alert('Error', error);
    }
  }, [error]);

  const handleToggleAdded = async (contactId: string) => {
    // If the contact is already added, just toggle the UI state
    const contact = deviceContacts.find(c => c.id === contactId);
    if (contact?.added) {
      toggleAdded(contactId);
      return;
    }

    // Otherwise, create a connect request
    setAddingContact(contactId);
    try {
      // Get the contact details
      if (!contact) {
        Alert.alert('Error', 'Contact not found');
        return;
      }

      // Check if the contact has an email
      const email = contact.emails && contact.emails.length > 0 ? contact.emails[0].email : undefined;

      // If no email, show an error message and return
      if (!email) {
        Alert.alert('Error', 'Invited contacts must have an email address');
        return;
      }

      const response = await connectRequestService.createConnectRequest({
        first_name: contact.name.split(' ')[0] || 'Unknown',
        last_name: contact.name.split(' ').slice(1).join(' ') || undefined,
        email
      });

      // Log whether we found a user with this email
      if (response.data) {
        console.log(`Connect request sent for email: ${email}`);
      } else {
        console.log(`Failed to send connect request for email: ${email}`);
      }

      if (response.error) {
        Alert.alert('Error', response.error);
        return;
      }

      // Mark the contact as added in the UI
      toggleAdded(contactId);

      // Show success message
      Alert.alert(
        'Connect Request Sent',
        'A connect request has been sent to the user.'
      );
    } catch (err) {
      console.error('Error creating connect request:', err);
      Alert.alert('Error', 'Failed to create connect request');
    } finally {
      setAddingContact(null);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Search style={styles.searchIcon} size={20} color={Colors.white} />
        <TextInput
          style={styles.searchInput}
          placeholderTextColor="#666666"
          placeholder="Search email contacts"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {isLoading || loadingUserContacts ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading contacts...</Text>
        </View>
      ) : filteredContacts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {searchQuery ? 'No email contacts found matching your search' : 'No email contacts found'}
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.contactsList}>
          {filteredContacts.map((contact) => (
            <View key={contact.id} style={styles.contactItem}>
              {contact.image ? (
                <Image source={{ uri: contact.image }} style={styles.avatarImage} />
              ) : (
                <Svg style={styles.avatar} width={17} height={17} viewBox="0 0 17 18" fill="none">
                  <Path d="M8.5 10.818C8.5 10.818 2.318 10.818 0 16.227C2.72509 17.2209 5.59947 17.7437 8.5 17.773C11.4005 17.7437 14.2749 17.2209 17 16.227C14.682 10.818 8.5 10.818 8.5 10.818ZM8.5 10.045C10.818 10.045 12.364 7.727 12.364 3.863C12.364 -0.000999928 8.5 2.67573e-10 8.5 2.67573e-10C8.5 2.67573e-10 4.636 0 4.636 3.864C4.636 7.728 6.182 10.045 8.5 10.045Z" fill={Colors.hyperBlue}/>
                </Svg>
              )}
              <Text style={styles.contactInfo}>
                {contact.name} {contact.emails && contact.emails[0] ? `(${contact.emails[0].email})` : ''}
              </Text>
              <Button
                variant="outline"
                size="icon"
                style={[styles.actionButton, contact.added && styles.added]}
                onPress={() => handleToggleAdded(contact.id)}
                disabled={addingContact === contact.id}
              >
                {addingContact === contact.id ? (
                  <ActivityIndicator size="small" color="white" />
                ) : contact.added ? (
                  <Image
                    source={require('../../assets/images/clock-icon.png')}
                    style={styles.clockIcon}
                  />
                ) : (
                  <Plus size={16} color={Colors.black} />
                )}
              </Button>
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  searchContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  searchIcon: {
    position: 'absolute',
    left: 16,
    top: '50%',
    marginTop: -10,
    zIndex: 1,
  },
  searchInput: {
    width: '100%',
    height: 40,
    paddingHorizontal: 16,
    paddingLeft: 48,
    borderWidth: 1,
    borderColor: Colors.white,
    borderRadius: 4,
    fontSize: 16,
    color: Colors.white,
  },
  contactsList: {
    display: 'flex',
    flexDirection: 'column',
    maxHeight: '85%',
  },
  contactItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: 45,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.primary}33`,
  },
  avatar: {
    width: 17,
    height: 17,
    marginRight: 12,
  },
  avatarImage: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
    fontSize: 16,
    color: Colors.white,
  },
  actionButton: {
    width: 39,
    height: 23,
    minWidth: 39,
    padding: 0,
    borderWidth: 0,
    borderRadius: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.secondary,
  },
  added: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  clockIcon: {
    width: 13,
    height: 14,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 8,
  },
  filteredOutText: {
    fontSize: 14,
    color: '#E35D5D',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});