import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator, Alert } from 'react-native';
import { Text } from "@/components/Themed";
import { StyleSheet } from 'react-native';
import { Button } from '@/components/ui/button';
import Svg, { Path } from 'react-native-svg';
import { useContactsStore } from '@/stores/contacts';
import Colors from '@/constants/Colors';

interface ConnectContactsScreenProps {
  onConnect: () => void;
}

export const ConnectContactsScreen: React.FC<ConnectContactsScreenProps> = ({ onConnect }) => {
  const { fetchContacts, isLoading, error, hasPermission, clearError } = useContactsStore();
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    // If there was an error, show it
    if (error) {
      Alert.alert('Error', error, [{ text: 'OK', onPress: clearError }]);
    }
  }, [error, clearError]);

  const handleConnectContacts = async () => {
    setIsConnecting(true);
    try {
      await fetchContacts();
      // If we have contacts or permission was granted, proceed
      if (hasPermission) {
        onConnect();
      }
    } catch (err) {
      console.error('Error connecting contacts:', err);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <View style={styles.mainContent}>
      <Svg width={85} height={85} viewBox="0 0 24 25" fill="none" style={styles.icon}>
        <Path d="M11.859 15.093C11.859 15.093 3.234 15.093 0 22.639C3.80204 24.0254 7.8123 24.7544 11.859 24.795C15.9057 24.7544 19.916 24.0254 23.718 22.639C20.484 15.093 11.859 15.093 11.859 15.093ZM11.859 14.015C15.093 14.015 17.25 10.781 17.25 5.39002C17.25 -0.000976562 11.859 -0.000976562 11.859 -0.000976562C11.859 -0.000976562 6.467 2.38419e-05 6.467 5.39102C6.467 10.782 8.625 14.015 11.859 14.015Z" fill={Colors.hyperBlue}/>
      </Svg>
      <Text style={styles.heading}>Connect Your Contacts</Text>
      <Text style={styles.description}>
        Find friends by syncing your phone's contact list with QWRM. No one will be notified until you choose people to connect with on the next screen.
      </Text>
      {isLoading || isConnecting ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Connecting to your contacts...</Text>
        </View>
      ) : (
        <Button
          style={styles.connectButton}
          textStyle={styles.connectButtonText}
          onPress={handleConnectContacts}
        >
          CONNECT CONTACTS
        </Button>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: 80,
    paddingHorizontal: 20,
    textAlign: 'center',
  },
  icon: {
    width: 85,
    height: 85,
    marginBottom: 16,
    color: Colors.primary,
  },
  heading: {
    fontSize: 21,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 24,
  },
  description: {
    fontSize: 15,
    color: Colors.white,
    marginBottom: 48,
    maxWidth: 300,
    lineHeight: 1.5 * 15,
    textAlign: 'center',
  },
  connectButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
    paddingVertical: 13,
    fontSize: 16,
    fontWeight: '500',
    borderRadius: 4,
    width: 260,
    height: 'auto',
  },
  connectButtonText: {
    color: Colors.primary,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
});