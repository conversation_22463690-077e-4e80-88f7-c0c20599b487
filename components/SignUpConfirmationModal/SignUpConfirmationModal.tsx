import React from 'react';
import { Modal, View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from "@/components/Themed";
import Colors from '@/constants/Colors';

interface DateOption {
  date: string;
  time: string;
}

interface SignUpConfirmationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  eventTitle: string;
  selectedDates: DateOption[];
  onClose: () => void;
  onCancel: () => void;
}

export const SignUpConfirmationModal: React.FC<SignUpConfirmationModalProps> = ({
  open,
  onOpenChange,
  eventTitle,
  selectedDates,
  onClose,
  onCancel,
}) => {
  return (
    <Modal
      visible={open}
      transparent={true}
      animationType="fade"
      onRequestClose={() => onOpenChange(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modal}>
          <View style={styles.content}>
            <Text style={styles.signedUpText}>You've signed up for</Text>
            <Text style={styles.eventTitle}>{eventTitle}</Text>

            <View style={styles.datesList}>
              {selectedDates.map((date, index) => (
                <React.Fragment key={index}>
                  <View style={styles.dateItem}>
                    <Text style={styles.date}>{date.date}</Text>
                    <Text style={styles.time}>{date.time}</Text>
                  </View>
                  {index < selectedDates.length - 1 && (
                    <Text style={styles.orDivider}>or</Text>
                  )}
                </React.Fragment>
              ))}
            </View>

            <Text style={styles.message}>
              We'll let you know when{'\n'}
              the event becomes official.
            </Text>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onCancel}
            >
              <Text style={styles.cancelButtonText}>I didn't mean to sign up</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modal: {
    width: '90%',
    maxWidth: 290,
    backgroundColor: Colors.background,
    borderRadius: 8,
    overflow: 'hidden',
  },
  content: {
    padding: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  signedUpText: {
    fontSize: 16,
    color: Colors.white,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  eventTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 24,
    textAlign: 'center',
  },
  datesList: {
    alignItems: 'center',
    marginBottom: 24,
  },
  dateItem: {
    alignItems: 'center',
  },
  date: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '600',
  },
  time: {
    fontSize: 16,
    color: Colors.white,
  },
  orDivider: {
    fontSize: 16,
    color: Colors.primary,
    marginVertical: 8,
    textTransform: 'uppercase',
  },
  message: {
    fontSize: 16,
    fontStyle: 'italic',
    color: Colors.white,
    marginBottom: 24,
    lineHeight: 22,
    textAlign: 'center',
  },
  closeButton: {
    height: 45,
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
    paddingHorizontal: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  closeButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '700',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    padding: 0,
  },
  cancelButtonText: {
    color: Colors.white,
    fontSize: 13,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
});