import React, { useState } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { Check } from 'lucide-react-native';
import Svg, { Path } from 'react-native-svg';
import Colors from '@/constants/Colors';

export interface Contact {
  id: string;
  name: string;
  selected?: boolean;
}

interface AddMembersProps {
  contacts: Contact[];
  onContactToggle: (contactId: string) => void;
  searchPlaceholder?: string;
  withPadding?: boolean;
}

const AddMembers: React.FC<AddMembersProps> = ({
  contacts,
  onContactToggle,
  searchPlaceholder = "Start typing a friend's name",
  withPadding = false
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderContactItem = ({ item }: { item: Contact }) => (
    <TouchableOpacity
      style={styles.contactItem}
      onPress={() => onContactToggle(item.id)}
    >
      <View style={[styles.checkbox, item.selected && styles.checkboxChecked]}>
        {item.selected && <Check width={12} height={12} color="black" />}
      </View>
      <Svg style={styles.avatar} width={17} height={18} viewBox="0 0 17 18" fill="none">
        <Path d="M8.5 10.818C8.5 10.818 2.318 10.818 0 16.227C2.72509 17.2209 5.59947 17.7437 8.5 17.773C11.4005 17.7437 14.2749 17.2209 17 16.227C14.682 10.818 8.5 10.818 8.5 10.818ZM8.5 10.045C10.818 10.045 12.364 7.727 12.364 3.863C12.364 -0.000999928 8.5 2.67573e-10 8.5 2.67573e-10C8.5 2.67573e-10 4.636 0 4.636 3.864C4.636 7.728 6.182 10.045 8.5 10.045Z" fill={Colors.secondary}/>
      </Svg>
      <Text style={styles.name}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TextInput
        style={[styles.searchInput, withPadding && styles.searchInputWithPadding]}
        placeholder={searchPlaceholder}
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholderTextColor={`${Colors.primaryDark}`}
      />

      <FlatList
        data={filteredContacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.id}
        style={[styles.contactsList, withPadding && styles.contactsListWithPadding]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 0,
  },
  searchInput: {
    width: '100%',
    paddingVertical: 8,
    paddingHorizontal: 0,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    fontSize: 16,
    color: Colors.primary,
    backgroundColor: 'transparent',
    marginBottom: 16,
  },
  searchInputWithPadding: {
    width: '100%',  // In React Native we'll handle the padding differently
    marginLeft: 20,
    marginRight: 20,
  },
  contactsList: {
    flexDirection: 'column',
    maxHeight: 300, // Fixed height to ensure scrollability
  },
  contactsListWithPadding: {
    paddingHorizontal: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 0,
    gap: 12,
  },
  checkbox: {
    width: 15,
    height: 15,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
  },
  avatar: {
    width: 17,
    height: 17,
    color: Colors.secondary,
  },
  name: {
    fontSize: 16,
    color: Colors.white,
  },
});

export default AddMembers;