import React from 'react';
import { View, TouchableOpacity, Modal, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { Text } from "@/components/Themed";
import Colors from '@/constants/Colors';

interface BlockContactModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contactName: string;
  onBlock: () => void;
  isBlocked?: boolean;
}

const BlockContactModal: React.FC<BlockContactModalProps> = ({
  open,
  onOpenChange,
  contactName,
  onBlock,
  isBlocked = false,
}) => {
  return (
    <Modal
      visible={open}
      transparent={true}
      animationType="fade"
      onRequestClose={() => onOpenChange(false)}
    >
      <TouchableWithoutFeedback onPress={() => onOpenChange(false)}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
            <View style={styles.modal}>
              <Text style={styles.title}>{isBlocked ? `Unblock ${contactName}?` : `Block ${contactName}?`}</Text>
              <Text style={styles.description}>
                {isBlocked
                  ? `You will be able to invite each other to events again.`
                  : `You will no longer be able to invite each other to events.`
                }
              </Text>
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.blockButton, isBlocked && styles.unblockButton]}
                  onPress={onBlock}
                >
                  <Text style={[styles.blockButtonText, isBlocked && styles.unblockButtonText]}>{isBlocked ? 'UNBLOCK' : 'BLOCK'}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 50,
  },
  modal: {
    width: '90%',
    maxWidth: 290,
    backgroundColor: Colors.background,
    borderRadius: 8,
    padding: 32,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '500',
    color: Colors.white,
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 15,
    color: Colors.white,
    marginBottom: 32,
    lineHeight: 21, // approximate equivalent of 1.4 line-height
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  blockButton: {
    backgroundColor: 'transparent',
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 0,
    borderWidth: 1,
    borderColor: Colors.error,
  },
  unblockButton: {
    borderColor: Colors.success, // Green color for unblock
  },
  blockButtonText: {
    color: Colors.error,
    fontSize: 16,
    fontWeight: '500',
  },
  unblockButtonText: {
    color: Colors.success,
  },
});

export default BlockContactModal;