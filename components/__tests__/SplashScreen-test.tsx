import React from 'react';
import { render } from '@testing-library/react-native';
import SplashScreen from '../SplashScreen';

// Mock the image require
jest.mock('@/assets/images/splash-icon.png', () => 'splash-icon.png');

describe('SplashScreen', () => {
  it('renders correctly', () => {
    const { getByTestId } = render(<SplashScreen />);
    // The component should render without crashing
    expect(true).toBe(true);
  });

  it('calls onAnimationComplete callback when provided', (done) => {
    const mockCallback = jest.fn(() => {
      expect(mockCallback).toHaveBeenCalled();
      done();
    });

    render(
      <SplashScreen 
        onAnimationComplete={mockCallback} 
        minimumDisplayTime={100} // Short time for testing
      />
    );
  });

  it('respects minimum display time', (done) => {
    const startTime = Date.now();
    const minimumTime = 500;
    
    const mockCallback = jest.fn(() => {
      const elapsed = Date.now() - startTime;
      expect(elapsed).toBeGreaterThanOrEqual(minimumTime - 50); // Allow small margin
      done();
    });

    render(
      <SplashScreen 
        onAnimationComplete={mockCallback} 
        minimumDisplayTime={minimumTime}
      />
    );
  });

  it('renders with default props', () => {
    const tree = render(<SplashScreen />);
    expect(tree).toBeTruthy();
  });
});
