.modal {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  padding: 0 !important;
  background-color: #0093AD !important;
  border-radius: 0 !important;
  color: white;
}

.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120px 20px 40px;
  text-align: center;
}

.logo {
  width: 335px;
  height: auto;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: white;
  margin: 0 0 80px 0;
}

.message {
  font-size: 24px;
  color: white;
  margin: 0 0 40px 0;
  line-height: 1.4;
  max-width: 300px;
}

.demoImage {
  width: 100%;
  max-width: 335px;
  height: auto;
  border-radius: 8px;
}

.pagination {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 40px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
}

.dot.active {
  background-color: white;
}

.skipButton {
  position: absolute;
  bottom: 40px;
  right: 40px;
  background: none !important;
  border: none !important;
  color: white !important;
  font-size: 16px !important;
  cursor: pointer !important;
  padding: 0 !important;
  height: auto !important;
}