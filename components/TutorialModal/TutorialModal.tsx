import React from 'react';
import { Modal, View, TouchableOpacity, StyleSheet, Image, Dimensions } from 'react-native';
import { Text } from "@/components/Themed";

interface TutorialModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const TutorialModal: React.FC<TutorialModalProps> = ({
  open,
  onOpenChange,
}) => {
  return (
    <Modal
      visible={open}
      transparent={true}
      animationType="fade"
      onRequestClose={() => onOpenChange(false)}
    >
      <View style={styles.modal}>
        <View style={styles.content}>
          <Image 
            source={require('../../assets/signup-sheet-logo.png')} 
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.subtitle}>Tutorial</Text>

          <Text style={styles.message}>
            In your event feed, a solid border around an event means quorum has been met and the event is on!
          </Text>

          <Image 
            source={{ uri: "https://i.imgur.com/1apUkfr.png" }} 
            style={styles.demoImage}
            resizeMode="contain"
          />

          <View style={styles.pagination}>
            <View style={[styles.dot, styles.activeDot]} />
            <View style={styles.dot} />
            <View style={styles.dot} />
            <View style={styles.dot} />
            <View style={styles.dot} />
          </View>

          <TouchableOpacity 
            style={styles.skipButton} 
            onPress={() => onOpenChange(false)}
          >
            <Text style={styles.skipButtonText}>Skip</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  modal: {
    width: '100%',
    height: '100%',
    backgroundColor: '#0093AD',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 120,
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  logo: {
    width: 335,
    height: 50, // Adjust based on your logo's aspect ratio
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    marginBottom: 80,
  },
  message: {
    fontSize: 24,
    color: 'white',
    marginBottom: 40,
    lineHeight: 34,
    textAlign: 'center',
    maxWidth: 300,
  },
  demoImage: {
    width: Math.min(width - 40, 335),
    height: 200, // Adjust based on your image's aspect ratio
    borderRadius: 8,
  },
  pagination: {
    flexDirection: 'row',
    marginTop: 40,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: 'white',
  },
  skipButton: {
    position: 'absolute',
    bottom: 40,
    right: 40,
    padding: 10, // Add some padding for easier touch
  },
  skipButtonText: {
    color: 'white',
    fontSize: 16,
  },
});