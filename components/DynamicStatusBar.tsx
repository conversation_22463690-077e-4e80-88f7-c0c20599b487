import React from 'react';
import { StatusBar } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Colors from '@/constants/Colors';

interface DynamicStatusBarProps {
  backgroundColor?: string;
  barStyle?: 'default' | 'light-content' | 'dark-content';
  translucent?: boolean;
}

export default function DynamicStatusBar({
  backgroundColor = Colors.background, // Default to #202326
  barStyle = 'light-content',
  translucent = false
}: DynamicStatusBarProps) {

  // Use useFocusEffect to ensure status bar updates when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      StatusBar.setBackgroundColor(backgroundColor, true);
      StatusBar.setBarStyle(barStyle, true);
      StatusBar.setTranslucent(translucent);
    }, [backgroundColor, barStyle, translucent])
  );

  return null; // This component doesn't render anything visible
}
