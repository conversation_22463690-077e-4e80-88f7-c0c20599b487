import * as React from "react";
import { View, StyleSheet } from "react-native";

function Card({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return <View style={[styles.card, style]}>{children}</View>;
}

function CardHeader({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return <View style={[styles.cardHeader, style]}>{children}</View>;
}

function CardTitle({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return <View style={[styles.cardTitle, style]}>{children}</View>;
}

function CardDescription({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return <View style={[styles.cardDescription, style]}>{children}</View>;
}

function CardContent({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return <View style={[styles.cardContent, style]}>{children}</View>;
}

function CardFooter({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return <View style={[styles.cardFooter, style]}>{children}</View>;
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "white",
    borderRadius: 8,
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 1 },
    // shadowOpacity: 0.1,
    // shadowRadius: 2,
    // elevation: 2,
    overflow: "hidden",
  },
  cardHeader: {
    padding: 16,
    paddingBottom: 0,
  },
  cardTitle: {
    marginBottom: 4,
  },
  cardDescription: {
    marginBottom: 8,
  },
  cardContent: {
    padding: 16,
  },
  cardFooter: {
    padding: 16,
    paddingTop: 0,
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
  },
});

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
};