import * as React from "react";
import { View, Modal, Pressable, StyleSheet, Animated } from "react-native";
import { Text } from "@/components/Themed";

interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

const Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => {
  return (
    <DialogContext.Provider value={{ open, onOpenChange }}>
      {children}
    </DialogContext.Provider>
  );
};

const DialogContext = React.createContext<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
}>({
  open: false,
  onOpenChange: () => {},
});

const DialogTrigger: React.FC<{
  children: React.ReactNode;
  asChild?: boolean;
}> = ({ children, asChild }) => {
  const { onOpenChange } = React.useContext(DialogContext);
  
  return (
    <Pressable onPress={() => onOpenChange(true)}>
      {children}
    </Pressable>
  );
};

interface DialogContentProps {
  className?: string;
  children: React.ReactNode;
}

const DialogContent = React.forwardRef<View, DialogContentProps>(
  ({ className, children, ...props }, ref) => {
    const { open, onOpenChange } = React.useContext(DialogContext);
    const fadeAnim = React.useRef(new Animated.Value(0)).current;
    const scaleAnim = React.useRef(new Animated.Value(0.95)).current;

    React.useEffect(() => {
      if (open) {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          })
        ]).start();
      } else {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.95,
            duration: 200,
            useNativeDriver: true,
          })
        ]).start();
      }
    }, [open, fadeAnim, scaleAnim]);

    return (
      <Modal
        transparent
        visible={open}
        onRequestClose={() => onOpenChange(false)}
        animationType="none"
      >
        <Pressable 
          style={styles.overlay} 
          onPress={() => onOpenChange(false)}
        >
          <Animated.View 
            style={[
              styles.overlayBackground,
              { opacity: fadeAnim }
            ]}
          />
          <Animated.View
            ref={ref}
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              },
              className
            ]}
            {...props}
          >
            <Pressable onPress={(e) => e.stopPropagation()}>
              {children}
            </Pressable>
          </Animated.View>
        </Pressable>
      </Modal>
    );
  }
);
DialogContent.displayName = "DialogContent";

const DialogClose: React.FC<{
  children: React.ReactNode;
  asChild?: boolean;
}> = ({ children, asChild }) => {
  const { onOpenChange } = React.useContext(DialogContext);
  
  return (
    <Pressable onPress={() => onOpenChange(false)}>
      {children}
    </Pressable>
  );
};

const DialogHeader: React.FC<{
  className?: string;
  children: React.ReactNode;
}> = ({ className, children, ...props }) => (
  <View style={[styles.header, className]} {...props}>
    {children}
  </View>
);
DialogHeader.displayName = "DialogHeader";

const DialogFooter: React.FC<{
  className?: string;
  children: React.ReactNode;
}> = ({ className, children, ...props }) => (
  <View style={[styles.footer, className]} {...props}>
    {children}
  </View>
);
DialogFooter.displayName = "DialogFooter";

const DialogTitle: React.FC<{
  className?: string;
  children: React.ReactNode;
}> = ({ className, children, ...props }) => (
  <Text style={[styles.title, className]} {...props}>
    {children}
  </Text>
);
DialogTitle.displayName = "DialogTitle";

const DialogDescription: React.FC<{
  className?: string;
  children: React.ReactNode;
}> = ({ className, children, ...props }) => (
  <Text style={[styles.description, className]} {...props}>
    {children}
  </Text>
);
DialogDescription.displayName = "DialogDescription";

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayBackground: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    backdropFilter: 'blur(4px)', // Note: backdropFilter may not work on all platforms
  },
  content: {
    backgroundColor: 'transparent',
    borderRadius: 8,
    padding: 16,
    width: '90%',
    maxWidth: 500,
    boxShadow: `0px 2px 3.84px rgba(0, 0, 0, 0.25)`,
    elevation: 5,
  },
  header: {
    marginBottom: 12,
  },
  footer: {
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
});

export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
};