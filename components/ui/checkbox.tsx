import React from 'react';
import { TouchableOpacity, View, StyleSheet, ViewStyle } from 'react-native';
import { Check } from 'lucide-react-native';
import Colors from '@/constants/Colors';

interface CheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  style?: ViewStyle;
}

const Checkbox: React.FC<CheckboxProps> = ({ 
  checked, 
  onCheckedChange,
  style 
}) => {
  return (
    <TouchableOpacity
      onPress={() => onCheckedChange(!checked)}
      activeOpacity={0.7}
    >
      <View style={[
        styles.checkbox, 
        checked && styles.checked,
        style
      ]}>
        {checked && <Check size={16} color={Colors.black} />}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checked: {
    backgroundColor: Colors.primary,
  },
});

export default Checkbox;
