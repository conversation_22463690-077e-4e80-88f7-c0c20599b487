import React from 'react';
import { View, TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Text } from "@/components/Themed";
import Colors from '@/constants/Colors';

interface FlatTabsProps {
  tabs: string[];
  activeTab: string;
  onTabChange: (tab: string) => void;
  style?: ViewStyle;
}

export const FlatTabs: React.FC<FlatTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  style,
}) => {
  // Calculate the width percentage for each tab
  const tabWidthPercentage = 100 / tabs.length;

  return (
    <View style={[styles.tabsContainer, style]}>
      <View style={styles.tabsList}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              { width: `${tabWidthPercentage}%` },
              activeTab === tab ? styles.activeTab : null
            ]}
            onPress={() => onTabChange(tab)}
          >
            <Text style={[
              styles.tabText,
              activeTab === tab ? styles.activeTabText : null
            ]}>
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabsContainer: {
    width: '100%',
    backgroundColor: 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: Colors.primaryDark,
  },
  tabsList: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tab: {
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#666666',
    textAlign: 'center',
  },
  activeTabText: {
    color: Colors.primary,
  },
});