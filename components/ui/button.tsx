import * as React from "react";
import {
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle
} from "react-native";
import { Text } from "@/components/Themed";
import Colors from '@/constants/Colors';

type ButtonVariant = "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
type ButtonSize = "default" | "sm" | "lg" | "icon";

interface ButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  disabled?: boolean;
  children?: React.ReactNode;
  asChild?: boolean;
  onPress?: () => void;
}

const Button = React.forwardRef<TouchableOpacity, ButtonProps>(
  ({
    variant = "default",
    size = "default",
    style,
    textStyle,
    disabled = false,
    asChild = false,
    children,
    onPress,
    ...props
  }, ref) => {

    // Handle the asChild pattern for React Native
    if (asChild && React.isValidElement(children)) {
      return React.cloneElement(children as React.ReactElement, {
        ...props,
        onPress,
        disabled,
        style: [getVariantStyle(variant), getSizeStyle(size), style]
      });
    }

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          styles.base,
          getVariantStyle(variant),
          getSizeStyle(size),
          disabled && styles.disabled,
          style,
        ]}
        disabled={disabled}
        activeOpacity={0.7}
        onPress={onPress}
        {...props}
      >
        {typeof children === 'string' ? (
          <Text
            style={[
              styles.text,
              getTextStyle(variant),
              textStyle
            ]}
          >
            {children}
          </Text>
        ) : (
          children
        )}
      </TouchableOpacity>
    );
  }
);

Button.displayName = "Button";

// Helper functions for styles
const getVariantStyle = (variant: ButtonVariant): StyleProp<ViewStyle> => {
  switch (variant) {
    case "default":
      return styles.default;
    case "destructive":
      return styles.destructive;
    case "outline":
      return styles.outline;
    case "secondary":
      return styles.secondary;
    case "ghost":
      return styles.ghost;
    case "link":
      return styles.link;
    default:
      return {};
  }
};

const getTextStyle = (variant: ButtonVariant): StyleProp<TextStyle> => {
  switch (variant) {
    case "default":
      return styles.textDefault;
    case "destructive":
      return styles.textDestructive;
    case "outline":
      return styles.textOutline;
    case "secondary":
      return styles.textSecondary;
    case "ghost":
      return styles.textGhost;
    case "link":
      return styles.textLink;
    default:
      return {};
  }
};

const getSizeStyle = (size: ButtonSize): StyleProp<ViewStyle> => {
  switch (size) {
    case "default":
      return styles.sizeDefault;
    case "sm":
      return styles.sizeSm;
    case "lg":
      return styles.sizeLg;
    case "icon":
      return styles.sizeIcon;
    default:
      return {};
  }
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 0,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Variants
  default: {
    backgroundColor: Colors.secondary,
  },
  textDefault: {
    color: Colors.black,
    fontWeight: '700',
  },
  destructive: {
    backgroundColor: Colors.error,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
  },
  textDestructive: {
    color: Colors.white,
    fontWeight: '700',
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  textOutline: {
    color: Colors.primary,
  },
  secondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  textSecondary: {
    color: Colors.primary,
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  textGhost: {
    color: Colors.white,
  },
  link: {
    backgroundColor: 'transparent',
  },
  textLink: {
    color: Colors.primary,
    textDecorationLine: 'underline',
  },
  // Sizes
  sizeDefault: {
    height: 36,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  sizeSm: {
    height: 32,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  sizeLg: {
    height: 40,
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  sizeIcon: {
    height: 36,
    width: 36,
    padding: 0,
  },
  // States
  disabled: {
    opacity: 0.5,
  },
});

const buttonVariants = (options: {
  variant?: ButtonVariant;
  size?: ButtonSize;
}): StyleProp<ViewStyle> => {
  const { variant = 'default', size = 'default' } = options;

  return [
    styles.base,
    getVariantStyle(variant),
    getSizeStyle(size),
  ];
};

export { Button, buttonVariants };
export type { ButtonProps, ButtonVariant, ButtonSize };