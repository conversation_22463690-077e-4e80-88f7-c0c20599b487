import { CheckIcon, ChevronDownIcon } from "lucide-react-native";
import * as React from "react";
import {
  View,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
  Pressable
} from "react-native";
import { Text } from "@/components/Themed";
import Colors from "@/constants/Colors";

const Select = ({
  children,
  value,
  onValueChange,
  defaultValue
}: {
  children: React.ReactNode;
  value?: string;
  onValueChange?: (value: string) => void;
  defaultValue?: string;
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [selectedValue, setSelectedValue] = React.useState(value || defaultValue || "");

  React.useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  const handleSelect = (newValue: string) => {
    setSelectedValue(newValue);
    if (onValueChange) {
      onValueChange(newValue);
    }
    setIsOpen(false);
  };

  return (
    <SelectProvider
      value={{
        isOpen,
        setIsOpen,
        selectedValue,
        handleSelect
      }}
    >
      {children}
    </SelectProvider>
  );
};

type SelectContextType = {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedValue: string;
  handleSelect: (value: string) => void;
};

const SelectContext = React.createContext<SelectContextType | undefined>(undefined);

const SelectProvider = ({
  children,
  value
}: {
  children: React.ReactNode;
  value: SelectContextType;
}) => {
  return (
    <SelectContext.Provider value={value}>
      {children}
    </SelectContext.Provider>
  );
};

const useSelectContext = () => {
  const context = React.useContext(SelectContext);
  if (!context) {
    throw new Error("Select components must be used within a Select");
  }
  return context;
};

const SelectGroup = ({ children }: { children: React.ReactNode }) => {
  return <View style={styles.group}>{children}</View>;
};

const SelectValue = ({ placeholder }: { placeholder?: string }) => {
  const { selectedValue } = useSelectContext();
  const labelRef = React.useRef<string>("");

  // Find the corresponding label for the selected value
  React.Children.forEach(
    React.useContext(SelectContext)?.children as React.ReactNode,
    (child) => {
      if (React.isValidElement(child) && child.type === SelectContent) {
        React.Children.forEach(child.props.children, (contentChild) => {
          if (
            React.isValidElement(contentChild) &&
            contentChild.type === SelectItem &&
            contentChild.props.value === selectedValue
          ) {
            labelRef.current = contentChild.props.children;
          }
        });
      }
    }
  );

  // Determine if we're showing a placeholder or a selected value
  const isPlaceholder = !(labelRef.current || selectedValue);

  return (
    <Text style={[styles.selectValue, isPlaceholder && styles.placeholderText]}>
      {labelRef.current || selectedValue || placeholder || "Select an option"}
    </Text>
  );
};

const SelectTrigger = ({
  children,
  style
}: {
  children: React.ReactNode;
  style?: any;
}) => {
  const { isOpen, setIsOpen } = useSelectContext();

  return (
    <TouchableOpacity
      style={[styles.trigger, style, isOpen && styles.triggerOpen]}
      onPress={() => setIsOpen(!isOpen)}
      activeOpacity={0.7}
    >
      <View style={styles.triggerContent}>
        {children}
        <ChevronDownIcon size={16} color="#fff" style={styles.icon} />
      </View>
    </TouchableOpacity>
  );
};

const SelectContent = ({ children }: { children: React.ReactNode }) => {
  const { isOpen, setIsOpen } = useSelectContext();

  if (!isOpen) return null;

  return (
    <Modal
      visible={isOpen}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setIsOpen(false)}
    >
      <Pressable
        style={styles.overlay}
        onPress={() => setIsOpen(false)}
      >
        <View style={styles.contentWrapper}>
          <View style={styles.content} onStartShouldSetResponder={() => true}>
            <ScrollView style={styles.scrollView}>
              {children}
            </ScrollView>
          </View>
        </View>
      </Pressable>
    </Modal>
  );
};

const SelectItem = ({
  children,
  value,
  style
}: {
  children: React.ReactNode;
  value: string;
  style?: any;
}) => {
  const { selectedValue, handleSelect } = useSelectContext();
  const isSelected = selectedValue === value;

  return (
    <TouchableOpacity
      style={[styles.item, style, isSelected && styles.itemSelected]}
      onPress={() => handleSelect(value)}
      activeOpacity={0.7}
    >
      <Text style={[styles.itemText, isSelected && styles.itemTextSelected]}>
        {children}
      </Text>
      {isSelected && (
        <View style={styles.checkIconWrapper}>
          <CheckIcon size={14} color={Colors.background} />
        </View>
      )}
    </TouchableOpacity>
  );
};

const SelectLabel = ({
  children,
  style
}: {
  children: React.ReactNode;
  style?: any;
}) => {
  return (
    <Text style={[styles.label, style]}>
      {children}
    </Text>
  );
};

const SelectSeparator = ({ style }: { style?: any }) => {
  return <View style={[styles.separator, style]} />;
};

const styles = StyleSheet.create({
  trigger: {
    height: 32,
    width: '100%',
    borderWidth: 1,
    borderColor: '#FFFF00',
    borderRadius: 4,
    backgroundColor: 'white',
    paddingHorizontal: 8,
    paddingVertical: 5,
    boxShadow: `0px 1px 2px rgba(0, 0, 0, 0.1)`,
    elevation: 1,
  },
  triggerOpen: {
    borderColor: '#FFFF00',
  },
  triggerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  icon: {
    marginTop: 2,
    opacity: 1,
  },
  selectValue: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
  },
  placeholderText: {
    color: '#999999',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentWrapper: {
    position: 'absolute',
    width: '90%',
    maxWidth: 200,
    backgroundColor: 'transparent',
  },
  content: {
    backgroundColor: Colors.primary,
    borderWidth: 1,
    borderColor: 'transparent',
    boxShadow: `4px 4px 0px #000`,
    elevation: 3,
    maxHeight: 300,
    width: '100%',
  },
  scrollView: {
    padding: 4,
  },
  group: {
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    borderRadius: 4,
  },
  itemSelected: {
    backgroundColor: 'rgba(23, 150, 172, 0.1)',
  },
  itemText: {
    fontSize: 13,
    color: '#0b4551',
  },
  itemTextSelected: {
    fontWeight: '500',
    color: Colors.background,
  },
  checkIconWrapper: {
    height: 14,
    width: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    fontSize: 13,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 6,
  },
  separator: {
    height: 1,
    backgroundColor: '#e5e5e5',
    marginVertical: 4,
    marginHorizontal: 8,
  },
});

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
};
