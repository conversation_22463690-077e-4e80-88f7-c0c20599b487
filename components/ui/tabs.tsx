import * as React from "react";
import { View, TouchableOpacity, StyleSheet, ScrollView } from "react-native";
import { Text } from "@/components/Themed";
import Colors from "@/constants/Colors";

const TabsContext = React.createContext<{
  value: string;
  onChange: (value: string) => void;
} | undefined>(undefined);

function Tabs({
  defaultValue,
  value,
  onValueChange,
  children,
  style,
}: {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
  style?: any;
}) {
  const [tabValue, setTabValue] = React.useState(value || defaultValue || "");

  React.useEffect(() => {
    if (value !== undefined) {
      setTabValue(value);
    }
  }, [value]);

  const onChange = React.useCallback(
    (newValue: string) => {
      setTabValue(newValue);
      onValueChange?.(newValue);
    },
    [onValueChange]
  );

  return (
    <TabsContext.Provider value={{ value: tabValue, onChange }}>
      <View style={[styles.tabs, style]}>{children}</View>
    </TabsContext.Provider>
  );
}

function useTabsContext() {
  const context = React.useContext(TabsContext);
  if (!context) {
    throw new Error("Tabs components must be used within a Tabs provider");
  }
  return context;
}

function TabsList({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: any;
}) {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[styles.tabsList, style]}
    >
      {children}
    </ScrollView>
  );
}

function TabsTrigger({
  value,
  children,
  style,
}: {
  value: string;
  children: React.ReactNode;
  style?: any;
}) {
  const { value: selectedValue, onChange } = useTabsContext();
  const isActive = selectedValue === value;

  return (
    <TouchableOpacity
      style={[
        styles.tabsTrigger,
        style,
        isActive && styles.tabsTriggerActive
      ]}
      onPress={() => onChange(value)}
      activeOpacity={0.7}
    >
      <Text
        style={[
          styles.tabsTriggerText,
          isActive && styles.tabsTriggerTextActive
        ]}
      >
        {children}
      </Text>
    </TouchableOpacity>
  );
}

function TabsContent({
  value,
  children,
  style,
}: {
  value: string;
  children: React.ReactNode;
  style?: any;
}) {
  const { value: selectedValue } = useTabsContext();
  const isActive = selectedValue === value;

  if (!isActive) return null;

  return <View style={[styles.tabsContent, style]}>{children}</View>;
}

const styles = StyleSheet.create({
  tabs: {
    width: "100%"
  },
  tabsList: {
    flexDirection: "row",
    gap: 8,
    paddingBottom: 2
  },
  tabsTrigger: {
    paddingHorizontal: 12,
    height: 27,
    boxSizing: "border-box",
    borderRadius: 4,
    backgroundColor: "#f8fffe",
    borderWidth: 1,
    borderColor: "#FFFF00",
    alignItems: "center",
    justifyContent: "center",
  },
  tabsTriggerActive: {
    backgroundColor: Colors.primary,
    boxShadow: `2px 2px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
  },
  tabsTriggerText: {
    fontSize: 11,
    fontWeight: "500",
    color: Colors.primary,
  },
  tabsTriggerTextActive: {
    color: "black",
  },
  tabsContent: {
    paddingVertical: 10,
  },
});

export { Tabs, TabsList, TabsTrigger, TabsContent };