.container {
  background-color: transparent;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}

.wrapper {
  width: 100%;
  min-height: 100vh;
}

.content {
  position: relative;
  min-height: 100vh;
  background-color: white;
  padding-bottom: 47px;
}

/* Common section styling */
.section {
  margin-top: 24px;
}

.padding {
  padding: 16px 20px;
}

.flexColumn {
  display: flex;
  flex-direction: column;
}

.flexRow {
  display: flex;
  flex-direction: row;
}

.gap8 {
  gap: 8px;
}

.gap12 {
  gap: 12px;
}

.gap16 {
  gap: 16px;
}

.alignCenter {
  align-items: center;
}

.justifyBetween {
  justify-content: space-between;
}

.relative {
  position: relative;
}
