.header {
  background-color: #FFFF00;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}

.headerText {
  color: white;
  font-size: 16px;
  margin: 0;
}

.headerLink {
  color: white;
  text-decoration: underline;
  font-size: 14px;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
}

.quorumHeader {
  background-color: #E35D5D;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quorumHeader.met {
  background-color: #3257DB;
}

.quorumHeaderText {
  color: white;
  font-size: 13px;
  margin: 0;
  font-weight: 500;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: #222222;
  margin: 0;
}

.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}
