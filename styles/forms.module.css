.form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.input {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid #DDDDDD;
  border-radius: 4px;
  font-size: 16px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 16px;
  font-weight: 600;
  color: #222222;
}

.checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #FFFF00;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: white;
}

.checkbox.checked {
  background-color: #FFFF00;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.toggle {
  width: 48px;
  height: 28px;
  background-color: #D9D9D9;
  border-radius: 14px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle.active {
  background-color: #FFFF00;
}

.toggleHandle {
  width: 24px;
  height: 24px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  left: 2px;
  top: 2px;
  transition: transform 0.3s;
}

.toggle.active .toggleHandle {
  transform: translateX(20px);
}

.searchContainer {
  position: relative;
  margin-bottom: 16px;
}

.searchInput {
  width: 100%;
  height: 40px;
  padding: 0 16px 0 40px;
  border: 1px solid #DDDDDD;
  border-radius: 4px;
  font-size: 16px;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #FFFF00;
}
