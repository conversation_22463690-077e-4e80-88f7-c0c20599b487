import React, { useState, useRef } from 'react';
import { View, Image, StyleSheet, TouchableOpacity, ScrollView, Dimensions, NativeSyntheticEvent, NativeScrollEvent, Linking } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { Button } from '@/components/ui/button';
import { useTutorialStore } from '@/stores/tutorial';
import Colors from '@/constants/Colors';
import { LogoYellow } from '@/assets/icons/qwrm-logo-yellow';
import { CreateEventIcon } from '@/components/TabIcons';
import { LogoGreen } from '@/assets/icons/qwrm-logo-green';
import { LogoTeal } from '@/assets/icons/qwrm-logo-teal';
import { LogoBlue } from '@/assets/icons/qwrm-logo-blue';
import { LogoRed } from '@/assets/icons/qwrm-logo-red';

// Individual slide components
interface SlideProps {
  width: number;
}

const Slide1: React.FC<SlideProps> = ({ width }) => (
  <View style={[styles.slide, { width }]}>
    <View style={styles.logo}>
      <LogoYellow />
    </View>
    <Text style={styles.subtitle}>HOW TO <Text style={{ color: Colors.primaryDark, fontWeight: 600 }}>CREATE EVENTS</Text></Text>

    <View style={styles.textContainer}>
      <Text style={{...styles.title, color: Colors.primary}}>
        Create an event.
      </Text>
      <Text style={styles.message}>
        Create an event by clicking the calendar-plus icon at the bottom of the screen.
      </Text>
    </View>

    <View style={styles.image}>
      <CreateEventIcon color={Colors.secondary} size={40} />
    </View>

    <View style={styles.textContainer}>
      <Text style={styles.message}>
        We’ll walk you through adding all of your event details.
      </Text>
      <View style={{ height: 30 }} />
      <Text style={styles.message}>
        If the event has a link, paste it in and we’ll grab the details for you!
      </Text>
    </View>
  </View>
);

const Slide2: React.FC<SlideProps> = ({ width }) => (
  <View style={[styles.slide, { width }]}>
    <View style={styles.logo}>
      <LogoGreen />
    </View>
    <Text style={styles.subtitle}>HOW TO <Text style={{ color: Colors.green, fontWeight: 600 }}>CREATE EVENTS</Text></Text>

    <View style={styles.textContainer}>
      <Text style={{...styles.title, color: Colors.green}}>
        Set the quorum.
      </Text>

      <Text style={styles.message}>
        The quorum is the minimum number of people needed to make an event "official".
      </Text>
      <View style={{ height: 30 }} />
      <Text style={styles.message}>
        You can also cap the number of people who can attend by setting a maximum.
      </Text>
    </View>

    <Image source={require('@/assets/images/tutorial-quorum.png')} style={styles.demoImage} resizeMode="contain" />
  </View>
);

const Slide3: React.FC<SlideProps> = ({ width }) => (
  <View style={[styles.slide, { width }]}>
    <View style={styles.logo}>
      <LogoTeal />
    </View>
    <Text style={styles.subtitle}>HOW TO <Text style={{ color: Colors.secondary, fontWeight: 600 }}>CREATE EVENTS</Text></Text>

    <View style={styles.textContainer}>
      <Text style={{...styles.title, color: Colors.secondary}}>
        Invite people.
      </Text>
      <Text style={styles.message}>
        Select individual contacts or groups you've created and post the event. It'll land in your invitees' event feeds.
      </Text>
      <View style={{ height: 30 }} />
      <Text style={styles.message}>
        If someone doesn't have <Text style={{ fontWeight: 700 }}>QWRM</Text> yet, we'll email them a link to download it.
      </Text>
    </View>
  </View>
);

const Slide4: React.FC<SlideProps> = ({ width }) => (
  <View style={[styles.slide, { width }]}>
    <View style={styles.logo}>
      <LogoBlue />
    </View>
    <Text style={styles.subtitle}>HOW TO <Text style={{ color: Colors.hyperBlue, fontWeight: 600 }}>BROWSE EVENTS</Text></Text>

    <View style={styles.textContainer}>
      <Text style={{...styles.title, color: Colors.hyperBlue}}>
        Check your feed.
      </Text>
      <Text style={styles.message}>
        Your home screen shows the events you’ve been invited to as well as events you’ve created.
      </Text>
      <View style={{ height: 30 }} />
      <Text style={styles.message}>
        A dashed yellow line around an event means it isn’t yet official.
      </Text>
    </View>

    <Image source={require('@/assets/images/tutorial-event-dashed.png')} style={styles.eventImage} resizeMode="contain" />

    <View style={{...styles.textContainer, marginTop: 20}}>
      <Text style={styles.message}>
        A solid teal line means quorum has been met. It's on!
      </Text>
    </View>

    <Image source={require('@/assets/images/tutorial-event-teal.png')} style={styles.eventImage} resizeMode="contain" />
  </View>
);

const Slide5: React.FC<SlideProps> = ({ width }) => (
  <View style={[styles.slide, { width }]}>
    <View style={styles.logo}>
      <LogoRed />
    </View>
    <Text style={styles.subtitle}>HOW TO <Text style={{ color: Colors.error, fontWeight: 600 }}>SIGN UP</Text></Text>

    <View style={styles.textContainer}>
      <Text style={{...styles.title, color: Colors.error}}>
        Sign up for what you can attend. Ignore the rest.
      </Text>
      <Text style={styles.message}>
        If you're interested in an event, just hit SIGN UP! on the event screen.
      </Text>
      <View style={{ height: 30 }} />
      <Text style={styles.message}>
        Not interested or can't make it? There's no action to take.
      </Text>
      <View style={{ height: 50 }} />
      <Text style={{...styles.title, color: Colors.error}}>
        Have more questions?
      </Text>
    </View>

    <View style={styles.textContainer}>
      <Text style={styles.message}>
        <Text
          style={styles.linkText}
          onPress={() => Linking.openURL('https://qwrm.app/glossary')}
        >
          Visit our glossary
        </Text>
        {" "}or view our FAQ on{" "}
        <Text
          style={styles.linkText}
          onPress={() => Linking.openURL('https://qwrm.app/glossary')}
        > 
          qwrm.app
        </Text>
      </Text>
    </View>
  </View>
);

const TutorialPage = () => {
  const { replace } = useRouter();
  const markTutorialAsSeen = useTutorialStore(state => state.markTutorialAsSeen);
  const [activeSlide, setActiveSlide] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const { width } = Dimensions.get('window');

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffsetX / width);
    setActiveSlide(currentIndex);
  };

  const goToSlide = (index: number) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: index * width, animated: true });
    }
    setActiveSlide(index);
  };

  const handleSkip = () => {
    markTutorialAsSeen();
    replace('/(tabs)/home');
  };

  const tutorialSlides = [
    <Slide1 key={0} width={width} />,
    <Slide2 key={1} width={width} />,
    <Slide3 key={2} width={width} />,
    <Slide4 key={3} width={width} />,
    <Slide5 key={4} width={width} />
  ];

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.slideContainer}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            contentContainerStyle={styles.scrollContent}
            decelerationRate="fast"
            snapToInterval={width}
            snapToAlignment="center"
            contentOffset={{ x: 0, y: 0 }} // Ensure it starts at the first slide
          >
            {tutorialSlides}
          </ScrollView>
        </View>

        <View style={styles.pagination}>
          {tutorialSlides.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.dot, activeSlide === index && styles.activeDot]}
              onPress={() => goToSlide(index)}
              testID="pagination-dot"
            />
          ))}
        </View>

        <TouchableOpacity
          style={styles.skipButton}
          onPress={handleSkip}
        >
          <Text style={styles.skipButtonText}>{activeSlide === tutorialSlides.length - 1 ? 'DONE' : 'SKIP'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.backgroundDark,
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    padding: 40,
    paddingHorizontal: 0,
    position: 'relative',
  },
  logo: {
    width: 200,
    height: 80,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    marginBottom: 45,
    fontWeight: 600
  },
  title: {
    fontWeight: 700,
    fontSize: 23,
    marginBottom: 20,
  },
  slideContainer: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    // alignItems: 'center',
  },
  slide: {
    marginTop: 40,
    flex: 1,
    alignItems: 'center',
  },
  textContainer: {
    width: 300,
  },
  message: {
    fontSize: 18,
    color: 'white',
    lineHeight: 24,
    maxWidth: 300,
    fontWeight: 500
  },
  image: {
    marginVertical: 30
  },
  strong: {
    fontWeight: '600',
  },
  demoImage: {
    width: '100%',
    maxWidth: 300,
    height: 250,
    marginTop: 30
  },
  eventImage: {
    width: '100%',
    marginTop: 20
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  dot: {
    width: 11,
    height: 11,
    borderRadius: 10,
    backgroundColor: Colors.primaryDark,
    marginHorizontal: 7,
  },
  activeDot: {
    backgroundColor: Colors.primary,
  },
  skipButton: {
    position: 'absolute',
    bottom: 40,
    right: 40,
  },
  skipButtonText: {
    color: 'white',
    fontSize: 16,
  },
  finishButton: {
    backgroundColor: 'white',
    paddingHorizontal: 40,
    marginTop: 20,
  },
  finishButtonText: {
    color: '#00788D',
    fontSize: 16,
  },
  linkText: {
    color: Colors.primary,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
    fontSize: 18,
    fontWeight: 500,
  },
});

export default TutorialPage;
