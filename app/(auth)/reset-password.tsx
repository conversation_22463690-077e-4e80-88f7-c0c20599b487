import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Button } from '@/components/ui/button';
import { authService } from '@/services/auth';
import Colors from '@/constants/Colors';
import { LogoYellow } from '@/assets/icons/qwrm-logo-yellow';

const ResetPasswordPage = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('');

  // Extract email and token from deep link parameters
  useEffect(() => {
    if (params.email && params.token) {
      setEmail(params.email as string);
      setToken(params.token as string);
    } else {
      // If no parameters, redirect back to forgot password
      setErrorMessage('Invalid reset link. Please request a new password reset.');
      setTimeout(() => {
        router.replace('/(auth)/forgot-password');
      }, 3000);
    }
  }, [params]);

  const handleSubmit = async () => {
    // Validate form
    if (!password || !confirmPassword) {
      setErrorMessage('Both password fields are required');
      return;
    }

    if (password !== confirmPassword) {
      setErrorMessage('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setErrorMessage('Password must be at least 6 characters long');
      return;
    }

    if (!email || !token) {
      setErrorMessage('Invalid reset link. Please request a new password reset.');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      // Call the reset password API
      const response = await authService.resetPassword({ email, token, password });

      if (response.error) {
        setErrorMessage(response.error);
        return;
      }

      setSuccessMessage('Password has been reset successfully!');

      // Navigate back to sign-in after a delay
      setTimeout(() => {
        router.replace('/(auth)/sign-in');
      }, 2000);

    } catch (error) {
      console.error('Reset password error:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logo}>
          <LogoYellow />
        </View>

        <View style={styles.form}>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>
            {email ? `Enter your new password for ${email}` : 'Enter your new password below.'}
          </Text>
          
          <TextInput
            style={styles.input}
            placeholder="New Password"
            placeholderTextColor={Colors.background}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            textContentType="newPassword"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Confirm New Password"
            placeholderTextColor={Colors.background}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            textContentType="newPassword"
          />
          
          {errorMessage ? (
            <Text style={styles.errorText}>{errorMessage}</Text>
          ) : null}
          
          {successMessage ? (
            <Text style={styles.successText}>{successMessage}</Text>
          ) : null}

          <Button
            onPress={handleSubmit}
            style={styles.resetButton}
            textStyle={styles.resetButtonText}
            disabled={isLoading}
          >
            {isLoading ? <ActivityIndicator color={Colors.black} /> : 'UPDATE PASSWORD'}
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logo: {
    marginBottom: 35,
  },
  form: {
    width: '100%',
    maxWidth: 335,
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
  },
  title: {
    color: Colors.white,
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    color: Colors.white,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  input: {
    width: '100%',
    height: 45,
    paddingHorizontal: 20,
    borderRadius: 0,
    backgroundColor: Colors.gray,
    color: Colors.black,
    fontSize: 16,
    marginBottom: 16,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  successText: {
    color: '#4CAF50',
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  resetButton: {
    width: '100%',
    height: 45,
    color: Colors.black,
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
    fontSize: 16,
    marginTop: 8,
  },
  resetButtonText: {
    color: Colors.black,
    fontWeight: '700',
    fontSize: 16,
  },
});

export default ResetPasswordPage;
