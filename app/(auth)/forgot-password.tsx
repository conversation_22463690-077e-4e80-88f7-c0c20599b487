import React, { useState } from 'react';
import { View, StyleSheet, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform, TouchableOpacity } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { Button } from '@/components/ui/button';
import { authService } from '@/services/auth';
import Colors from '@/constants/Colors';
import { LogoYellow } from '@/assets/icons/qwrm-logo-yellow';
import { ChevronLeft } from 'lucide-react-native';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const ForgotPasswordPage = () => {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleSubmit = async () => {
    // Validate form
    if (!email) {
      setErrorMessage('Email is required');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setErrorMessage('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      // Call the forgot password API
      const response = await authService.forgotPassword({ email });

      if (response.error) {
        setErrorMessage(response.error);
        return;
      }

      // Show success message
      setSuccessMessage('Password reset instructions have been sent to your email.');

      // Navigate back to sign-in after a delay
      setTimeout(() => {
        router.back();
      }, 3000);

    } catch (error) {
      console.error('Forgot password error:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeft size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.logo}>
            <LogoYellow />
          </View>

        <View style={styles.form}>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you instructions to reset your password.
          </Text>
          
          <TextInput
            style={styles.input}
            placeholder="Email"
            placeholderTextColor={Colors.background}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          {errorMessage ? (
            <Text style={styles.errorText}>{errorMessage}</Text>
          ) : null}
          
          {successMessage ? (
            <Text style={styles.successText}>{successMessage}</Text>
          ) : null}

          <Button
            onPress={handleSubmit}
            style={styles.resetButton}
            textStyle={styles.resetButtonText}
            disabled={isLoading}
          >
            {isLoading ? <ActivityIndicator color={Colors.black} /> : 'SEND RESET EMAIL'}
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 40, // Add top padding to account for fixed header
    paddingBottom: 40,
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20, // Add more top padding for status bar
    paddingBottom: 20,
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
  },
  backButton: {
    padding: 8,
  },
  logo: {
    marginBottom: 35,
  },
  form: {
    width: '100%',
    maxWidth: 335,
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
  },
  title: {
    color: Colors.white,
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    color: Colors.white,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  input: {
    width: '100%',
    height: 45,
    paddingHorizontal: 20,
    borderRadius: 0,
    backgroundColor: Colors.gray,
    color: Colors.black,
    fontSize: 16,
    marginBottom: 16,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  successText: {
    color: '#4CAF50',
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  resetButton: {
    width: '100%',
    height: 45,
    color: Colors.black,
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
    fontSize: 16,
    marginTop: 8,
  },
  resetButtonText: {
    color: Colors.black,
    fontWeight: '700',
    fontSize: 16,
  },
});

export default ForgotPasswordPage;
