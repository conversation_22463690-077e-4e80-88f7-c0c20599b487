import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import * as Google from 'expo-auth-session/providers/google';
import { makeRedirectUri } from 'expo-auth-session';
import Constants from 'expo-constants';
import { Text, TextInput } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/stores/auth';
import { useTutorialStore } from '@/stores/tutorial';
import { authService } from '@/services/auth';
// import { signInWithGoogle } from '@/services/googleAuth';
import Colors from '@/constants/Colors';
import { LogoYellow } from '@/assets/icons/qwrm-logo-yellow';
import { mixpanelService } from '@/services/mixpanel';

const SignInPage = () => {
  const router = useRouter();
  const login = useAuthStore(state => state.login);
  const hasSeenTutorial = useTutorialStore(state => state.hasSeenTutorial);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  // Remove manual navigation - let root layout handle routing based on auth state

    const googleAndroidClientId = Constants.expoConfig?.extra?.googleAndroidClientId;
    const googleIosClientId = Constants.expoConfig?.extra?.googleIosClientId;
  
    const redirectUri = makeRedirectUri({
      scheme: 'com.signupsheet',
    });

    console.log('Generated redirect URI:', redirectUri);

    const [request, response, promptAsync] = Google.useAuthRequest({
      iosClientId: googleIosClientId,
      androidClientId: googleAndroidClientId,
      redirectUri: redirectUri,
    });
  
    const signInWithGoogle = async () => {
    try {
      console.log("SIGNING IN WITH GOOGLE")
      console.log(request)
      const result = await promptAsync();
      console.log('Auth result:', result);
  
      if (result.type === 'success') {
        // Get the authorization code from the result
        const authCode = result.params?.code;

        if (!authCode) {
          throw new Error('Failed to get authorization code from Google');
        }

        console.log('Got authorization code:', authCode);
        console.log('Code verifier:', request?.codeVerifier);

        // Send the authorization code and code verifier to our backend to exchange for access token and authenticate
        const response = await authService.googleAuth({
          access_token: authCode,
          code_verifier: request?.codeVerifier,
          redirect_uri: redirectUri,
          platform: Platform.OS,
        });

        console.log("response", response)
  
        if (response.data) {
          // Capture the response data before the timeout
          const userData = response.data;

            // Store auth data in zustand store
            useAuthStore.getState().login({
              id: userData.user.id,
              displayName: userData.user.display_name,
              email: userData.user.email,
              accessLevel: userData.user.access_level,
              token: userData.token,
              refreshToken: userData.refreshToken,
              profileImageUrl: userData.user.profile_image_url,
              emailVerified: userData.user.email_verified
            });
  
            try {
              if (userData.isNewUser) {
                await mixpanelService.trackSignUp(
                  userData.user.email,
                  userData.user.display_name,
                  'google'
                );
              } else {
                await mixpanelService.trackSignIn(
                  userData.user.email,
                  userData.user.display_name,
                  'google'
                );
              }
              // Always identify the user (new or existing)
              await mixpanelService.identify(userData.user.id.toString(), {
                email: userData.user.email,
                name: userData.user.display_name,
                display_name: userData.user.display_name,
              });
            } catch (analyticsError) {
              console.error('Analytics tracking error:', analyticsError);
              // Don't fail the authentication if analytics fails
            }
  
          return { success: true, data: response.data };
        }
      } else if (result.type === 'error') {
        console.error('Auth error:', result.error);
        throw new Error(result.error?.message || 'Authentication failed');
      } else {
        throw new Error('Authentication was cancelled or failed');
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      };
    }
  };
  

  const handleSubmit = async () => {
    // Validate form
    if (!email || !password) {
      setErrorMessage('Email and password are required');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      const response = await authService.login({ email, password });

      if (response.error) {
        setErrorMessage(response.error);
        return;
      }

      if (response.data) {
        // Store auth data in zustand store
        login({
          id: response.data.user.id,
          displayName: response.data.user.display_name,
          email: response.data.user.email,
          accessLevel: response.data.user.access_level,
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          profileImageUrl: response.data.user.profile_image_url,
          emailVerified: response.data.user.email_verified
        });

        // Track sign in and identify user in Mixpanel
        try {
          await mixpanelService.trackSignIn(response.data.user.email, response.data.user.display_name, 'email');
          await mixpanelService.identify(response.data.user.id.toString(), {
            email: response.data.user.email,
            name: response.data.user.display_name,
            display_name: response.data.user.display_name,
          });
        } catch (analyticsError) {
          console.error('Analytics tracking error:', analyticsError);
          // Don't fail the login if analytics fails
        }

        // Check if email verification is needed
        if (!response.data.user.email_verified) {
          router.replace('/(auth)/email-verification');
        } else {
          // Redirect to tutorial if first time, otherwise to home
          if (hasSeenTutorial) {
            router.replace('/(tabs)/home');
          } else {
            router.replace('/tutorial');
          }
        }
      }
    } catch (error) {
      console.error('Login screen error:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    setErrorMessage('');

    try {
      console.log('Starting Google sign-in process...');

      // Use the standard Google OAuth flow for development builds
      const result = await signInWithGoogle();

      console.log('Google sign-in result:', result);

      if (!result || !result.success) {
        const errorMsg = result?.error || 'Google sign-in failed';
        console.error('Google sign-in error:', errorMsg);
        setErrorMessage(errorMsg);
        return;
      }

      // Don't manually navigate - let the root layout handle navigation based on auth state
      // The root layout will automatically redirect based on isLoggedIn and emailVerified status

      // Add a small delay to ensure auth state is properly updated before navigation
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error('Google sign-in screen error:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred. Please try again.'
      );
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logo}>
          <LogoYellow />
        </View>

        <View style={styles.form}>
          <TextInput
            style={styles.input}
            placeholder="Email"
            placeholderTextColor={Colors.background}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          <TextInput
            style={styles.input}
            placeholder="Password"
            placeholderTextColor={Colors.background}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          {errorMessage ? (
            <Text style={styles.errorText}>{errorMessage}</Text>
          ) : null}

          <Button
            onPress={handleSubmit}
            style={styles.signInButton}
            textStyle={styles.signInButtonText}
            disabled={isLoading}
          >
            {isLoading ? <ActivityIndicator color={Colors.black} /> : 'SIGN IN'}
          </Button>
          <TouchableOpacity onPress={() => router.push('/(auth)/forgot-password')}>
            <Text style={styles.forgotPassword}>Forgot password?</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.socialButtons}>
          <Button
            style={styles.socialButton}
            textStyle={styles.socialButtonText}
            onPress={() => router.push('/(auth)/sign-up')}
          >
            Join with phone or email
          </Button>
          <Button
            style={styles.socialButton}
            textStyle={styles.socialButtonText}
            onPress={handleGoogleSignIn}
            disabled={isGoogleLoading}
          >
            {isGoogleLoading ? <ActivityIndicator color={Colors.primary} /> : 'Sign in with Google'}
          </Button>
          {/* <Button style={styles.socialButton} disabled={true}>
            Sign in with Facebook
          </Button> */}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logo: {
    marginBottom: 35,
  },
  form: {
    width: '100%',
    maxWidth: 335,
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
  },
  input: {
    width: '100%',
    height: 45,
    paddingHorizontal: 20,
    borderRadius: 0,
    backgroundColor: Colors.gray,
    color: Colors.black,
    fontSize: 16,
    marginBottom: 16,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  signInButton: {
    width: '100%',
    height: 45,
    color: Colors.black,
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
    fontSize: 16,
  },
  signInButtonText: {
    color: Colors.black,
    fontWeight: '700',
    fontSize: 16,
  },
  forgotPassword: {
    color: Colors.white,
    textAlign: 'center',
    fontSize: 14,
    marginTop: 20,
    marginBottom: 24,
  },
  socialButtons: {
    width: '100%',
    maxWidth: 335,
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
  socialButton: {
    width: '100%',
    height: 45,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 0,
  },
  socialButtonText: {
    color: Colors.primary,
    fontSize: 15,
  },
});

export default SignInPage;
