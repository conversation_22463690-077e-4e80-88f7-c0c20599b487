import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform, Linking } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { Button } from '@/components/ui/button';
import Checkbox from '@/components/ui/checkbox';
import { useAuthStore } from '@/stores/auth';
import { useTutorialStore } from '@/stores/tutorial';
import { authService } from '@/services/auth';
import Colors from '@/constants/Colors';
import { LogoYellow } from '@/assets/icons/qwrm-logo-yellow';
import { mixpanelService } from '@/services/mixpanel';

const SignUpPage = () => {
  const router = useRouter();
  const login = useAuthStore(state => state.login);
  const hasSeenTutorial = useTutorialStore(state => state.hasSeenTutorial);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Email validation function
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async () => {
    // Validate form
    if (!fullName || !email || !password) {
      setErrorMessage('All fields are required');
      return;
    }

    if (!isValidEmail(email)) {
      setErrorMessage('Please enter a valid email address');
      return;
    }

    if (password !== confirmPassword) {
      setErrorMessage('Passwords do not match');
      return;
    }

    if (!termsAccepted) {
      setErrorMessage('You must agree to the Terms of Service and Privacy Policy');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      // Prepare registration data
      const userData = {
        display_name: fullName,
        email,
        password,
        // Split full name into first and last name if possible
        first_name: fullName.split(' ')[0],
        last_name: fullName.split(' ').slice(1).join(' ') || ''
      };

      const response = await authService.register(userData);

      if (response.error) {
        setErrorMessage(response.error);
        return;
      }

      if (response.data) {
        // Store auth data in zustand store
        login({
          id: response.data.user.id,
          displayName: response.data.user.display_name,
          email: response.data.user.email,
          accessLevel: response.data.user.access_level || 'user',
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          profileImageUrl: response.data.user.profile_image_url,
          emailVerified: response.data.user.email_verified
        });

        // Track user sign up in Mixpanel
        try {
          await mixpanelService.trackSignUp(email, fullName, 'email');
          await mixpanelService.identify(response.data.user.id.toString(), {
            email: email,
            name: fullName,
            display_name: response.data.user.display_name,
          });
        } catch (analyticsError) {
          console.error('Analytics tracking error:', analyticsError);
          // Don't fail the registration if analytics fails
        }

        // Check if email verification is needed
        if (!response.data.user.email_verified) {
          router.replace('/(auth)/email-verification');
        } else {
          // Redirect to tutorial if first time, otherwise to home
          if (hasSeenTutorial) {
            router.replace('/(tabs)/home');
          } else {
            router.replace('/tutorial');
          }
        }
      }
    } catch (error) {
      console.error('Registration screen error:', error);
      setErrorMessage(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logo}>
          <LogoYellow />
        </View>

        <Text style={styles.title}>Create an Account</Text>

        <View style={styles.form}>
          <TextInput
            style={styles.input}
            placeholder="Full Name"
            placeholderTextColor={Colors.background}
            value={fullName}
            onChangeText={setFullName}
          />
          <TextInput
            style={styles.input}
            placeholder="Email"
            placeholderTextColor={Colors.background}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          <TextInput
            style={styles.input}
            placeholder="Password"
            placeholderTextColor={Colors.background}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            textContentType="newPassword"
          />
          <TextInput
            style={styles.input}
            placeholder="Confirm Password"
            placeholderTextColor={Colors.background}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            textContentType="password"
          />

          <View style={styles.termsContainer}>
            <Checkbox
              checked={termsAccepted}
              onCheckedChange={setTermsAccepted}
              style={styles.termsCheckbox}
            />
            <View style={styles.termsTextContainer}>
              <Text style={styles.terms}>
                I have read and agree to the{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => Linking.openURL('https://www.qwrm.app/terms-and-conditions/')}
                >
                  Terms of Service
                </Text>
                {' '}and{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => Linking.openURL('https://www.qwrm.app/privacy/')}
                >
                  Privacy Policy
                </Text>
              </Text>
            </View>
          </View>

          {errorMessage ? (
            <Text style={styles.errorText}>{errorMessage}</Text>
          ) : null}

          <Button
            onPress={handleSubmit}
            style={styles.joinButton}
            textStyle={styles.joinButtonText}
            disabled={isLoading}
          >
            {isLoading ? <ActivityIndicator color={Colors.black} /> : 'JOIN'}
          </Button>

          <TouchableOpacity onPress={() => router.push('/(auth)/sign-in')}>
            <Text style={styles.signInLink}>Already have an account? Sign In</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logo: {
    marginBottom: 35,
  },
  title: {
    color: Colors.white,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    fontSize: 24,
    fontWeight: '500',
    marginBottom: 24,
  },
  form: {
    width: '100%',
    maxWidth: 335,
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
  },
  input: {
    width: '100%',
    height: 45,
    paddingHorizontal: 20,
    borderRadius: 0,
    backgroundColor: Colors.gray,
    color: Colors.black,
    fontSize: 16,
    marginBottom: 15,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: 8,
    paddingHorizontal: 4,
  },
  termsCheckbox: {
    marginTop: 2,
    marginRight: 12,
  },
  termsTextContainer: {
    flex: 1,
  },
  terms: {
    color: Colors.white,
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'left',
  },
  termsLink: {
    color: Colors.white,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dashed',
  },
  joinButton: {
    width: '100%',
    height: 45,
    marginTop: 8,
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
  },
  joinButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '700',
  },
  signInLink: {
    color: Colors.white,
    textAlign: 'center',
    fontSize: 14,
    marginTop: 16,
  }
});

export default SignUpPage;
