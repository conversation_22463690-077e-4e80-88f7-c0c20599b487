import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/stores/auth';
import { useTutorialStore } from '@/stores/tutorial';
import { authService } from '@/services/auth';
import Colors from '@/constants/Colors';
import { LogoYellow } from '@/assets/icons/qwrm-logo-yellow';
import DynamicStatusBar from '@/components/DynamicStatusBar';

export default function EmailVerificationScreen() {
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const router = useRouter();
  const { userData, logout, updateUserData } = useAuthStore(state => ({
    userData: state.userData,
    logout: state.logout,
    updateUserData: state.updateUserData
  }));

  const { hasSeenTutorial } = useTutorialStore(state => ({
    hasSeenTutorial: state.hasSeenTutorial
  }));

  // Check verification status on mount and redirect if already verified
  useEffect(() => {
    // If user is already verified, redirect to appropriate screen
    if (userData?.emailVerified === true) {
      if (hasSeenTutorial) {
        router.replace('/(tabs)/home');
      } else {
        router.replace('/tutorial');
      }
      return;
    }

    checkVerificationStatus();
  }, [userData?.emailVerified, hasSeenTutorial]);

  const checkVerificationStatus = async () => {
    if (!userData?.token) return;
    
    setIsCheckingStatus(true);
    setErrorMessage('');
    
    try {
      const response = await authService.checkEmailVerificationStatus();
      
      if (response.data?.email_verified) {
        // Update the local auth store with verification status
        updateUserData({ emailVerified: true });

        // User is now verified, redirect to appropriate screen
        if (hasSeenTutorial) {
          router.replace('/(tabs)/home');
        } else {
          router.replace('/tutorial');
        }
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
      // Don't show error for status check, just continue showing the screen
    } finally {
      setIsCheckingStatus(false);
    }
  };

  const handleResendVerification = async () => {
    if (!userData?.token) {
      setErrorMessage('Authentication required. Please sign in again.');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      await authService.resendVerificationEmail();
      setSuccessMessage('Verification email sent! Please check your inbox and spam folder.');
    } catch (error: any) {
      console.error('Error resending verification email:', error);
      setErrorMessage(
        error.response?.data?.message || 
        'Failed to send verification email. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out? You will need to verify your email when you sign back in.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            logout();
            router.replace('/(auth)/sign-in');
          },
        },
      ]
    );
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logo}>
          <LogoYellow />
        </View>

        <Text style={styles.title}>Verify Your Email</Text>
        
        <View style={styles.content}>

          <Text style={styles.description}>
            We've sent a verification email to:
          </Text>
          
          <Text style={styles.email}>
            {userData?.email}
          </Text>

          <Text style={styles.instructions}>
            Please check your inbox and click the verification link to complete your account setup.
          </Text>

          {errorMessage ? (
            <Text style={styles.errorText}>{errorMessage}</Text>
          ) : null}

          {successMessage ? (
            <Text style={styles.successText}>{successMessage}</Text>
          ) : null}

          <View style={styles.buttonContainer}>
            <Button
              onPress={handleResendVerification}
              style={styles.resendButton}
              textStyle={styles.resendButtonText}
              disabled={isLoading}
            >
              {isLoading ? <ActivityIndicator color={Colors.black} /> : 'Resend Verification Email'}
            </Button>

            <Button
              onPress={checkVerificationStatus}
              style={styles.checkButton}
              textStyle={styles.checkButtonText}
              disabled={isCheckingStatus}
            >
              {isCheckingStatus ? <ActivityIndicator color={Colors.primary} /> : 'I\'ve Verified My Email'}
            </Button>
          </View>

          <TouchableOpacity onPress={handleSignOut} style={styles.signOutContainer}>
            <Text style={styles.signOutText}>Sign out and use a different account</Text>
          </TouchableOpacity>

          <View style={styles.helpContainer}>
            <Text style={styles.helpText}>
              Didn't receive the email? Check your spam folder or try resending.
            </Text>
            <Text style={styles.helpText}>
              The verification link will expire in 24 hours.
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logo: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.white,
    textAlign: 'center',
    marginBottom: 30,
  },
  content: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  emailIcon: {
    fontSize: 40,
  },
  description: {
    fontSize: 16,
    color: Colors.white,
    textAlign: 'center',
    marginBottom: 10,
  },
  email: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  instructions: {
    fontSize: 14,
    color: Colors.icon.muted,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 30,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  successText: {
    color: Colors.success,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    width: '100%',
    gap: 15,
  },
  resendButton: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primaryDark,
    borderWidth: 2,
    boxShadow: `0px 4px 0px ${Colors.primaryDark}`,
    elevation: 4,
  },
  resendButtonText: {
    color: Colors.black,
    fontWeight: 'bold',
  },
  checkButton: {
    backgroundColor: 'transparent',
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  checkButtonText: {
    color: Colors.primary,
    fontWeight: 'bold',
  },
  signOutContainer: {
    marginTop: 30,
    paddingVertical: 10,
  },
  signOutText: {
    color: Colors.icon.muted,
    fontSize: 14,
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
  helpContainer: {
    marginTop: 30,
    paddingHorizontal: 20,
  },
  helpText: {
    fontSize: 12,
    color: Colors.icon.muted,
    textAlign: 'center',
    lineHeight: 16,
    marginBottom: 5,
  },
});
