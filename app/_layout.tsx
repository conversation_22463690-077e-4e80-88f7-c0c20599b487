import '../polyfills'; // Required for React Navigation 7.x compatibility
import 'react-native-get-random-values'; // Required for Mixpanel
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';

import { useEffect, useState } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-reanimated';
import 'react-native-gesture-handler';

import { useColorScheme } from '@/components/useColorScheme';
import { useAuthStore } from '@/stores/auth';
import usePushNotifications from '@/hooks/usePushNotifications';
import { mixpanelService } from '@/services/mixpanel';
import CustomSplashScreen from '@/components/SplashScreen';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export const unstable_settings = {
  // Don't set an initial route - let the root layout logic decide
  // Define the routes for the tabs and auth stacks
  '(tabs)': {
    initialRouteName: 'home',
  },
  '(auth)': {
    initialRouteName: 'sign-in',
  },
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    'WorkSans': require('../assets/fonts/WorkSans-Regular.ttf'), // Keep for backward compatibility
    'WorkSans_300Light': require('../assets/fonts/WorkSans-Light.ttf'),
    'WorkSans_400Regular': require('../assets/fonts/WorkSans-Regular.ttf'),
    'WorkSans_500Medium': require('../assets/fonts/WorkSans-Medium.ttf'),
    'WorkSans_600SemiBold': require('../assets/fonts/WorkSans-SemiBold.ttf'),
    'WorkSans_700Bold': require('../assets/fonts/WorkSans-Bold.ttf'),
    'WorkSans_800ExtraBold': require('../assets/fonts/WorkSans-ExtraBold.ttf'),
    ...FontAwesome.font,
  });

  const [appReady, setAppReady] = useState(false);

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  const handleSplashComplete = () => {
    setAppReady(true);
  };

  if (!loaded || !appReady) {
    return <CustomSplashScreen onAnimationComplete={handleSplashComplete} />;
  }

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  const colorScheme = useColorScheme();
  const { isLoggedIn, userData } = useAuthStore(state => ({
    isLoggedIn: state.isLoggedIn,
    userData: state.userData
  }));

  // Initialize push notifications (must be called before any conditional returns)
  usePushNotifications();

  // Initialize Mixpanel analytics
  useEffect(() => {
    mixpanelService.initialize();
  }, []);

  // Add a small delay to ensure persisted state is loaded
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Give zustand persist time to load from AsyncStorage
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Navigation logic - show tabs only if logged in AND verified
  const shouldShowAuth = !isLoggedIn || (isLoggedIn && !userData?.emailVerified);
  console.log("userData", userData);

  // Don't render navigation until persisted state is loaded
  if (!isReady) {
    return null; // or a loading spinner
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen
            name="(auth)"
            redirect={!shouldShowAuth}
          />
          <Stack.Screen
            name="(tabs)"
            redirect={shouldShowAuth}
          />
          <Stack.Screen
            name="tutorial"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="index"
            redirect
          />
          <Stack.Screen
            name="signup"
            redirect
          />
        </Stack>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
