import React, { useEffect, useRef } from 'react';
import { Tabs } from 'expo-router';
import { SafeAreaView, AppState, AppStateStatus } from 'react-native';
import { useColorScheme } from 'react-native';
import Colors from '@/constants/Colors';
import { useAuthStore } from '@/stores/auth';
import { useNotificationStore } from '@/stores/notification';
import { HomeIcon, PeopleIcon, CreateEventIcon, NotificationsIcon, ProfileIcon } from '@/components/TabIcons';
import { Platform } from 'react-native';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  // Get userData for profile images (auth state is handled at root level)
  const userData = useAuthStore(state => state.userData);
  const { unreadCount, fetchNotifications, fetchNotificationsSilently } = useNotificationStore(state => ({
    unreadCount: state.unreadCount,
    fetchNotifications: state.fetchNotifications,
    fetchNotificationsSilently: state.fetchNotificationsSilently
  }));
  // We don't need hasSeenTutorial or router here anymore

  // References for polling mechanism
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isFetchingRef = useRef(false);
  const appStateRef = useRef(AppState.currentState);

  // Function to fetch notifications if not already fetching
  const fetchNotificationsIfNeeded = async (silent: boolean = true) => {
    if (isFetchingRef.current) {
      return;
    }

    isFetchingRef.current = true;
    try {
      if (silent) {
        // Use silent fetch for background polling (no loading indicator)
        await fetchNotificationsSilently();
      } else {
        // Use regular fetch for user-initiated actions (with loading indicator)
        await fetchNotifications();
        console.log('Global notifications poll with loading indicator at:', new Date().toISOString());
      }
    } catch (error) {
      console.error('Error fetching notifications in global poll:', error);
    } finally {
      isFetchingRef.current = false;
    }
  };

  useEffect(() => {
    // Initial fetch when logged in - use silent fetch
    fetchNotificationsIfNeeded(true);

    // Set up polling interval (every 5 seconds) - use silent fetch
    pollingIntervalRef.current = setInterval(() => fetchNotificationsIfNeeded(true), 5000);

    // Set up app state listener to pause/resume polling
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground, fetch immediately - use silent fetch
        fetchNotificationsIfNeeded(true);
      }
      appStateRef.current = nextAppState;
    });

    // Clean up on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      subscription.remove();
    };
  }, [fetchNotifications, fetchNotificationsSilently]);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.background }}>
      <Tabs
        screenOptions={{
          tabBarShowLabel: false,
          tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
          headerShown: false,
          tabBarStyle: {
            backgroundColor: Colors.background,
            height: Platform.OS === 'ios' ? 40 : 40,
            paddingBottom: Platform.OS === 'ios' ? 0 : 0,
            paddingTop: Platform.OS === 'ios' ? 10 : 0,
            borderTopWidth: 0,
          }
        }}>
        <Tabs.Screen
          name="home"
          options={{
            tabBarShowLabel: false,
            title: 'Home',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
          }}
        />
        <Tabs.Screen
          name="people"
          options={{
            tabBarShowLabel: false,
            title: 'People',
            tabBarIcon: ({ focused }) => (
              <PeopleIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
          }}
        />
        <Tabs.Screen
          name="create-event"
          options={{
            tabBarShowLabel: false,
            title: 'Create Event',
            tabBarIcon: ({ focused }) => (
              <CreateEventIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
          }}
        />
        <Tabs.Screen
          name="notifications"
          options={{
            tabBarShowLabel: false,
            title: 'Notifications',
            tabBarIcon: ({ focused }) => (
              <NotificationsIcon
                color={focused ? Colors.icon.selected : Colors.icon.default}
                unreadCount={unreadCount}
                size={24}
              />
            ),
          }}
          listeners={{
            tabPress: () => {
              // Fetch latest notifications silently before marking them as read
              fetchNotificationsIfNeeded(true).then(() => {
                // Mark all notifications as read when the tab is pressed
                useNotificationStore.getState().markAllAsRead();
              });
            },
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            tabBarShowLabel: false,
            title: 'Settings',
            tabBarIcon: ({ focused }) => (
              <ProfileIcon
                color={focused ? Colors.icon.selected : Colors.icon.default}
                profileImageUrl={userData?.profileImageUrl}
                size={24}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="contact"
          options={{
            tabBarShowLabel: false,
            title: 'Contact',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="contacts"
          options={{
            tabBarShowLabel: false,
            title: 'Contacts',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="create-contact"
          options={{
            tabBarShowLabel: false,
            title: 'Create contact',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="create-group"
          options={{
            tabBarShowLabel: false,
            title: 'Create Group',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="edit-group"
          options={{
            tabBarShowLabel: false,
            title: 'Edit Group',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="event"
          options={{
            tabBarShowLabel: false,
            title: 'Event',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="group"
          options={{
            tabBarShowLabel: false,
            title: 'Group',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="groups"
          options={{
            tabBarShowLabel: false,
            title: 'Groups',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="settings-notifications"
          options={{
            tabBarShowLabel: false,
            title: 'Notification Settings',
            tabBarIcon: ({ focused }) => (
              <ProfileIcon
                color={focused ? Colors.icon.selected : Colors.icon.default}
                profileImageUrl={userData?.profileImageUrl}
                size={24}
              />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="settings-payments"
          options={{
            tabBarShowLabel: false,
            title: 'Payment Settings',
            tabBarIcon: ({ focused }) => (
              <ProfileIcon
                color={focused ? Colors.icon.selected : Colors.icon.default}
                profileImageUrl={userData?.profileImageUrl}
                size={24}
              />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="settings-personal-info"
          options={{
            tabBarShowLabel: false,
            title: 'Personal Info',
            tabBarIcon: ({ focused }) => (
              <ProfileIcon
                color={focused ? Colors.icon.selected : Colors.icon.default}
                profileImageUrl={userData?.profileImageUrl}
                size={24}
              />
            ),
            href: null,
          }}
        />
        <Tabs.Screen
          name="edit-event"
          options={{
            tabBarShowLabel: false,
            title: 'Edit Event',
            tabBarIcon: ({ focused }) => (
              <HomeIcon color={focused ? Colors.icon.selected : Colors.icon.default} />
            ),
            href: null,
          }}
        />

      </Tabs>
    </SafeAreaView>
  );
}
