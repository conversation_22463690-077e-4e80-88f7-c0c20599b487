import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, SafeAreaView, ActivityIndicator, Alert } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { ChevronLeft } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import AddMembers, { Contact } from '@/components/AddMembers/AddMembers';
import { useRouter } from 'expo-router';
import { useApiContactsStore } from '@/stores/apiContacts';
import { useGroupsStore } from '@/stores/groups';
import { useAuthStore } from '@/stores/auth';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

export default function CreateGroupPage() {
  const router = useRouter();
  const [groupName, setGroupName] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);

  const { contacts, isLoading: isLoadingContacts, error: contactsError, fetchContacts } = useApiContactsStore();
  const { createGroup, error: groupError, isLoading: isLoadingGroup } = useGroupsStore();
  const { userData } = useAuthStore();

  useEffect(() => {
    fetchContacts();
  }, []);

  useEffect(() => {
    if (contactsError) {
      Alert.alert('Error', contactsError);
    }
    if (groupError) {
      Alert.alert('Error', groupError);
    }
  }, [contactsError, groupError]);

  const toggleContact = (contactId: string) => {
    setSelectedContacts(prev =>
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const resetForm = () => {
    setGroupName('');
    setSelectedContacts([]);
  };

  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      Alert.alert('Error', 'Please enter a group name');
      return;
    }

    if (selectedContacts.length === 0) {
      Alert.alert('Error', 'Please select at least one contact');
      return;
    }

    setIsCreating(true);
    try {
      // Convert string IDs to numbers for the API
      const contactIds = selectedContacts.map(id => parseInt(id));
      const success = await createGroup(groupName, contactIds);

      if (success) {
        resetForm(); // Reset the form inputs and checkboxes
        Alert.alert('Success', 'Group created successfully', [
          { text: 'OK', onPress: () => router.navigate('/groups') }
        ]);
      }
    } catch (error) {
      console.error('Error creating group:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // Map API contacts to the format expected by AddMembers component, filtering out contacts without user_id and the current user's contact
  const apiContactsFormatted: Contact[] = contacts
    .filter(contact => contact.user_id && contact.user_id !== userData?.id)
    .map(contact => ({
      id: contact.id.toString(),
      name: `${contact.first_name} ${contact.last_name || ''}`.trim(),
      selected: selectedContacts.includes(contact.id.toString())
    }));

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <SafeAreaView style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.navigate('/groups')}
            >
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <Text style={styles.title}>Create a Group</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.groupNameSection}>
              <TextInput
                style={styles.groupNameInput}
                placeholder="Name your group"
                placeholderTextColor={Colors.primaryDark}
                value={groupName}
                onChangeText={setGroupName}
                maxLength={75}
              />
              <Text style={styles.counter}>{groupName.length}/75</Text>
            </View>

            <Text style={styles.addMembersTitle}>Add members</Text>

            {isLoadingContacts ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={Colors.primary} />
                <Text style={styles.loadingText}>Loading contacts...</Text>
              </View>
            ) : apiContactsFormatted.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No contacts found</Text>
                <TouchableOpacity
                  style={styles.addContactButton}
                  onPress={() => router.navigate('/create-contact')}
                >
                  <Text style={styles.addContactButtonText}>Add Contacts</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <AddMembers
                contacts={apiContactsFormatted}
                onContactToggle={toggleContact}
                withPadding={false}
              />
            )}
          </View>

          <Button
            style={styles.createButton}
            textStyle={styles.createButtonText}
            onPress={handleCreateGroup}
            disabled={isCreating || isLoadingGroup}
          >
            {isCreating ? 'CREATING...' : 'CREATE GROUP'}
          </Button>
        </View>
      </View>
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.primaryDark,
    marginBottom: 20,
  },
  addContactButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  addContactButtonText: {
    color: Colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  container: {
    backgroundColor: 'transparent',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    flex: 1,
  },
  content: {
    position: 'relative',
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 100, // Increased to avoid tab navigator overlap
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 0,
    color: Colors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '700',
    color: Colors.white,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    margin: 0,
  },
  form: {
    padding: 20,
  },
  groupNameSection: {
    marginBottom: 16,
  },
  groupNameInput: {
    width: '100%',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    fontSize: 16,
    color: Colors.white,
    backgroundColor: 'transparent',
  },
  counter: {
    textAlign: 'right',
    fontSize: 12,
    color: Colors.white,
    marginTop: 4,
  },
  addMembersTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 16,
  },
  createButton: {
    width: '90%',
    alignSelf: 'center',
    marginVertical: 20,
    backgroundColor: Colors.primary,
    borderWidth: 1,
    borderColor: Colors.primary,
    height: 48,
    borderRadius: 4,
  },
  createButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '500',
  },
});