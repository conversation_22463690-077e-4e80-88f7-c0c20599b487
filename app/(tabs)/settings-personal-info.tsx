import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, SafeAreaView, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import { userService, UserUpdateRequest } from '@/services/user';
import { useAuthStore } from '@/stores/auth';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const PersonalInfoSettingsPage = () => {
  const router = useRouter();
  const { userData, updateUserData } = useAuthStore(state => ({
    userData: state.userData,
    updateUserData: state.updateUserData
  }));

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  // Autolocate feature removed
  // const [autolocateEnabled, setAutolocateEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load user data when component mounts
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    setIsLoading(true);
    try {
      // First try to use data from auth store
      if (userData) {
        setDisplayName(userData.displayName || '');
        setEmail(userData.email || '');
      }

      // Then fetch complete user data from API
      if (userData?.id) {
        const response = await userService.getUserById(userData.id);
        if (response.data) {
          setFirstName(response.data.first_name || '');
          setLastName(response.data.last_name || '');
          setDisplayName(response.data.display_name || '');
          setEmail(response.data.email || '');
          setPhone(response.data.phone_number || '');
          // Autolocate feature removed
          // setAutolocateEnabled(response.data.auto_locate || false);
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      Alert.alert('Error', 'Failed to load user data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!userData?.id) {
      Alert.alert('Error', 'User ID not found. Please log in again.');
      return;
    }

    setIsSaving(true);
    try {
      const userUpdateData: UserUpdateRequest = {
        first_name: firstName,
        last_name: lastName,
        display_name: displayName,
        email: email,
        phone_number: phone,
        // Autolocate feature removed
        // auto_locate: autolocateEnabled
      };

      const response = await userService.updateUser(userData.id, userUpdateData);

      if (response.data) {
        // Update the auth store with new user data
        updateUserData({
          displayName: response.data.display_name,
          email: response.data.email
        });

        Alert.alert('Success', 'Personal information updated successfully');
        router.push('/settings');
      } else if (response.error) {
        Alert.alert('Error', response.error);
      }
    } catch (error) {
      console.error('Error saving user data:', error);
      Alert.alert('Error', 'Failed to save user data. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <>
        <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
        <SafeAreaView style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading user data...</Text>
          </View>
        </SafeAreaView>
      </>
    );
  }

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <SafeAreaView style={styles.container}>
      <ScrollView style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.topBar}>
            <TouchableOpacity style={styles.backButton} onPress={() => router.push('/settings')}>
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleSave} disabled={isSaving}>
              {isSaving ? (
                <ActivityIndicator color={Colors.primary} size="small" />
              ) : (
                <Text style={styles.saveButton}>SAVE</Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.header}>
            <Text style={styles.title}>EDIT PERSONAL INFO</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>First name</Text>
              <TextInput
                style={styles.input}
                value={firstName}
                onChangeText={setFirstName}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Last name</Text>
              <TextInput
                style={styles.input}
                value={lastName}
                onChangeText={setLastName}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Display name</Text>
              <TextInput
                style={styles.input}
                value={displayName}
                onChangeText={setDisplayName}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Phone number</Text>
              <TextInput
                style={styles.input}
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>

            {/* Location section removed */}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flex: 1,
  },
  wrapper: {
    width: '100%',
    flex: 1,
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    paddingHorizontal: 25,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    marginLeft: -5,
    padding: 0,
    color: Colors.primary,
    alignItems: 'center',
  },
  saveButton: {
    padding: 0,
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '700',
  },
  header: {
    paddingHorizontal: 25,
    paddingBottom: 16,
    marginBottom: 32,
    backgroundColor: Colors.backgroundDark,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: Colors.white,
    margin: 0,
  },
  form: {
    paddingHorizontal: 25,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: 2,
    textTransform: 'uppercase',
  },
  input: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.primary}33`,
    paddingVertical: 4,
    fontSize: 16,
    color: Colors.white,
    backgroundColor: 'transparent',
    height: 28,
  },
  locationSection: {
    marginTop: 32,
  },
  locationTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: 16,
    textTransform: 'uppercase',
  },
  location: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '600',
  },
  country: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
  autolocateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  autolocateText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  toggle: {
    width: 45,
    height: 15,
    backgroundColor: `${Colors.primary}33`,
    borderRadius: 7.5,
    position: 'relative',
  },
  toggleActive: {
    backgroundColor: `${Colors.primary}33`,
  },
  toggleHandle: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: `${Colors.primary}99`,
    backgroundColor: 'white',
    borderRadius: 11,
    position: 'absolute',
    top: -3.5,
    left: 0,
  },
  toggleHandleActive: {
    transform: [{ translateX: 23 }],
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.primary,
  },
});

export default PersonalInfoSettingsPage;