import React, { useState, useEffect, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import { WhatStep } from '@/components/CreateEvent/WhatStep';
import { QuorumStep } from '@/components/CreateEvent/QuorumStep';
import { WhenStep } from '@/components/CreateEvent/WhenStep';
import { WhereStep } from '@/components/CreateEvent/WhereStep';
import { HowMuchStep } from '@/components/CreateEvent/HowMuchStep';
import { WhyStep } from '@/components/CreateEvent/WhyStep';
import { PreviewStep } from '@/components/CreateEvent/PreviewStep';
import { InviteStep } from '@/components/CreateEvent/InviteStep';
import { eventService } from '@/services/event';
import Colors from '@/constants/Colors';
import { convertDatesToLocal } from '@/utils/dateHelpers';
import DynamicStatusBar from '@/components/DynamicStatusBar';

// Define the EventResponse interface to match the API response
interface EventDateResponse {
  id: number;
  event_id: number;
  date: string;
  participants: number[];
  created_at: string;
  updated_at: string;
}

interface EventResponse {
  id: number;
  name: string;
  date: string[];
  location?: string;
  location_name?: string;
  address: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  meeting_point?: string;
  description?: string;
  event_link?: string;
  ticketUrl?: string;
  owner: string;
  owner_id?: number;
  image_url: string;
  has_options: boolean;
  quorum_met: boolean;
  cost?: number;
  cost_purpose?: string;
  payment_type?: string;
  participants?: number[];
  participants_count?: number;
  invitees?: number[]; // Array of user IDs who were invited to this event
  paid_participants?: number[];
  quorum?: number;
  max_participants?: number | null;
  confirmed_date?: string;
  eventDates?: EventDateResponse[];
  multiple_dates?: boolean;
  response_cutoff?: number;
  created_at?: string;
  updated_at?: string;
}

type Step = 'what' | 'quorum' | 'when' | 'where' | 'howMuch' | 'why' | 'preview' | 'invite';

const CreateEventPage = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const eventId = params.eventId ? parseInt(params.eventId as string) : null;
  const stepParam = params.step as Step | undefined;
  console.log('Create-event received params:', params);
  console.log('Parsed eventId:', eventId, 'stepParam:', stepParam);
  const [isEditing, setIsEditing] = useState(!!eventId);
  const [isLoading, setIsLoading] = useState(!!eventId);

  const [currentStep, setCurrentStep] = useState<Step>(stepParam || 'what');
  const [eventName, setEventName] = useState('');
  const [minPeople, setMinPeople] = useState('');
  const [maxPeople, setMaxPeople] = useState('');
  const [dates, setDates] = useState<Array<{date: string; time: string}>>([{date: '', time: ''}]);
  // Keep these for backward compatibility
  const date = dates[0]?.date || '';
  const time = dates[0]?.time || '';
  const setDate = (value: string) => {
    const newDates = [...dates];
    newDates[0] = { ...newDates[0], date: value };
    setDates(newDates);
  };
  const setTime = (value: string) => {
    const newDates = [...dates];
    newDates[0] = { ...newDates[0], time: value };
    setDates(newDates);
  };
  const [isFlexible, setIsFlexible] = useState(false);
  const [isMultiple, setIsMultiple] = useState(false);
  const [locationName, setLocationName] = useState('');
  const [addressLine1, setAddressLine1] = useState('');
  const [addressLine2, setAddressLine2] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zip, setZip] = useState('');
  const [meetingPoint, setMeetingPoint] = useState('');
  const [amount, setAmount] = useState('');
  const [costDescription, setCostDescription] = useState('');
  const [paymentType, setPaymentType] = useState('');
  const [description, setDescription] = useState('');
  const [cutoffTime, setCutoffTime] = useState('12');
  const [imageUri, setImageUri] = useState('');
  const [createdEventId, setCreatedEventId] = useState<number | null>(null);
  const [eventLink, setEventLink] = useState<string>('');
  const [invitees, setInvitees] = useState<number[]>([]);
  const [quorumMet, setQuorumMet] = useState<boolean>(false);

  const whenStepRef = useRef<{ validate: () => boolean }>(null);

  // Calculate progress percentage for the progress bar
  const steps = ['what', 'quorum', 'when', 'where', 'howMuch', 'why', 'preview', 'invite'];
  const currentIndex = steps.indexOf(currentStep);
  const progressPercentage = (currentIndex + 1) / steps.length * 100;

  // Update current step when stepParam changes
  useEffect(() => {
    if (stepParam && stepParam !== currentStep) {
      // Prevent navigation to WhenStep if quorum is met and we're editing
      if (stepParam === 'when' && isEditing && quorumMet) {
        console.log('Preventing navigation to WhenStep because quorum is met');
        return;
      }
      setCurrentStep(stepParam);
    }
  }, [stepParam, isEditing, quorumMet]);

  // Fetch event data when editing an existing event
  useEffect(() => {
    const fetchEventData = async () => {
      if (!eventId) return;

      try {
        setIsLoading(true);
        const response = await eventService.getEventById(eventId);

        if (response.error) {
          Alert.alert('Error', response.error || 'Failed to load event data');
          setIsLoading(false);
          return;
        }

        if (response.data) {
          const eventData = response.data as EventResponse;

          // Prefill form with event data
          setEventName(eventData.name || '');
          setDescription(eventData.description || '');
          setLocationName(eventData.location || '');

          // Handle address fields
          // First check if we have the individual address fields
          if (eventData.address_line1 !== undefined) {
            setAddressLine1(eventData.address_line1 || '');
            setAddressLine2(eventData.address_line2 || '');
            setCity(eventData.city || '');
            setState(eventData.state || '');
            setZip(eventData.zip || '');
            setMeetingPoint(eventData.meeting_point || '');
          }
          // Fallback to parsing the combined address if individual fields aren't available
          else if (eventData.address) {
            const addressParts = eventData.address.split(',').map(part => part.trim());
            if (addressParts.length >= 4) {
              setAddressLine1(addressParts[0] || '');
              setAddressLine2(addressParts[1] || '');
              setCity(addressParts[2] || '');

              // Handle state and zip which might be combined
              const stateZipPart = addressParts[3] || '';
              const stateZipMatch = stateZipPart.match(/([A-Z]{2})\s+(\d{5})/);
              if (stateZipMatch) {
                setState(stateZipMatch[1] || '');
                setZip(stateZipMatch[2] || '');
              } else {
                setState(stateZipPart);
              }
            } else {
              // Fallback if address format is different
              setAddressLine1(eventData.address);
            }
          }

          // Handle quorum and max participants
          setMinPeople(eventData.quorum ? eventData.quorum.toString() : '1');
          setMaxPeople(eventData.max_participants ? eventData.max_participants.toString() : 'unlimited');

          // Handle cost information
          if (eventData.cost) {
            setAmount(eventData.cost.toString().replace('$', ''));
          }
          setCostDescription(eventData.cost_purpose || '');
          setPaymentType(eventData.payment_type || '');

          // Handle image
          setImageUri(eventData.image_url || '');

          // Handle event link - check for both possible property names
          if ('event_link' in eventData) {
            setEventLink(eventData.event_link as string || '');
          } else if ('ticketUrl' in eventData) {
            setEventLink(eventData.ticketUrl as string || '');
          }

          // Handle dates
          setIsMultiple(eventData.multiple_dates || false);

          if (eventData.eventDates && eventData.eventDates.length > 0) {
            const formattedDates = convertDatesToLocal(eventData.eventDates.map(ed => ed.date));
            setDates(formattedDates);
            setIsFlexible(formattedDates.length > 1);
          }

          // Handle invitees
          if (eventData.invitees && Array.isArray(eventData.invitees)) {
            console.log('Setting invitees for editing:', eventData.invitees);
            setInvitees(eventData.invitees);
          }

          // Set quorum status
          setQuorumMet(eventData.quorum_met || false);

          // Set createdEventId to the event being edited
          setCreatedEventId(eventId);
        }
      } catch (error) {
        console.error('Error fetching event data:', error);
        Alert.alert('Error', 'Failed to load event data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventData();
  }, [eventId]);

  // Function to reset all form fields to their initial state
  const resetForm = () => {
    setEventName('');
    setMinPeople('');
    setMaxPeople('');
    setDates([{date: '', time: ''}]);
    setIsFlexible(false);
    setIsMultiple(false);
    setLocationName('');
    setAddressLine1('');
    setAddressLine2('');
    setCity('');
    setState('');
    setZip('');
    setMeetingPoint('');
    setAmount('');
    setCostDescription('');
    setPaymentType('');
    setDescription('');
    setCutoffTime('12');
    setImageUri('');
    setEventLink('');
    setInvitees([]);
    setCreatedEventId(null);
    setCurrentStep('what');
    setIsEditing(false);
  };

  const handleNext = () => {
    if (currentStep === 'what') {
      setCurrentStep('quorum');
    } else if (currentStep === 'quorum') {
      // Skip WhenStep if quorum is met and we're editing
      if (isEditing && quorumMet) {
        setCurrentStep('where');
      } else {
        setCurrentStep('when');
      }
    } else if (currentStep === 'when') {
      // Validate date selection before proceeding
      if (whenStepRef.current?.validate()) {
        setCurrentStep('where');
      }
    } else if (currentStep === 'where') {
      setCurrentStep('howMuch');
    } else if (currentStep === 'howMuch') {
      setCurrentStep('why');
    } else if (currentStep === 'why') {
      setCurrentStep('preview');
    } else if (currentStep === 'preview') {
      setCurrentStep('invite');
    } else if (currentStep === 'invite') {
      // Navigate to the event page with the created/updated event ID
      if (createdEventId) {
        // Add a timestamp as a refresh parameter to force the event page to reload data
        const refreshParam = Date.now().toString();
        console.log(`Navigating to event page with refresh parameter: ${refreshParam}`);

        // Reset the create event flow to the first step
        setCurrentStep('what');

        // Reset the editing state
        setIsEditing(false);

        // Navigate to the event page
        router.navigate({
          pathname: '/event',
          params: {
            id: createdEventId,
            refresh: refreshParam
          }
        });

        // Note: Form is already reset in the InviteStep component
      } else {
        // Fallback to home if no event ID is available
        router.navigate('/home');
      }
    }
  };

  const handleBack = () => {
    if (currentStep === 'what') {
      router.back();
    } else if (currentStep === 'quorum') {
      setCurrentStep('what');
    } else if (currentStep === 'when') {
      setCurrentStep('quorum');
    } else if (currentStep === 'where') {
      // Go back to WhenStep unless quorum is met and we're editing
      if (isEditing && quorumMet) {
        setCurrentStep('quorum');
      } else {
        setCurrentStep('when');
      }
    } else if (currentStep === 'howMuch') {
      setCurrentStep('where');
    } else if (currentStep === 'why') {
      setCurrentStep('howMuch');
    } else if (currentStep === 'preview') {
      setCurrentStep('why');
    } else if (currentStep === 'invite') {
      setCurrentStep('preview');
    }
  };

  const handleEditBasics = () => setCurrentStep('what');
  const handleEditDateTime = () => {
    // Prevent editing date/time if quorum is met
    if (isEditing && quorumMet) {
      console.log('Cannot edit date/time because quorum is met');
      return;
    }
    setCurrentStep('when');
  };
  const handleEditLocation = () => setCurrentStep('where');
  const handleEditQuorum = () => setCurrentStep('quorum');
  const handleEditDetails = () => setCurrentStep('why');
  const handleEditCosts = () => setCurrentStep('howMuch');
  const handleEditImage = () => setCurrentStep('what'); // assuming image edit is in 'what' step

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.header}>
          <View style={styles.progressBar}>
            <View
              style={[styles.progressFill, { width: `${progressPercentage}%` }]}
            />
          </View>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <ChevronLeft size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading event data...</Text>
          </View>
        ) : (
          <ScrollView style={styles.content}>
          {currentStep === 'what' && (
            <WhatStep
              eventName={eventName}
              onEventNameChange={setEventName}
              imageUri={imageUri}
              onImageUriChange={setImageUri}
              onDescriptionChange={setDescription}
              onLocationChange={setLocationName}
              onCostChange={(value) => setAmount(typeof value === 'string' ? value : value.toString())}
              onDateChange={setDate}
              eventLink={eventLink}
              onEventLinkChange={setEventLink}
            />
          )}
          {currentStep === 'quorum' && (
            <QuorumStep
              minPeople={minPeople}
              maxPeople={maxPeople}
              onMinPeopleChange={setMinPeople}
              onMaxPeopleChange={setMaxPeople}
            />
          )}
          {currentStep === 'when' && (
            <WhenStep
              ref={whenStepRef}
              date={date}
              time={time}
              isFlexible={isFlexible}
              isMultiple={isMultiple}
              onDateChange={setDate}
              onTimeChange={setTime}
              onFlexibleChange={setIsFlexible}
              onMultipleChange={setIsMultiple}
              dates={dates}
              onDatesChange={setDates}
              quorumMet={isEditing && quorumMet}
            />
          )}
          {currentStep === 'where' && (
            <WhereStep
              locationName={locationName}
              addressLine1={addressLine1}
              addressLine2={addressLine2}
              city={city}
              state={state}
              zip={zip}
              meetingPoint={meetingPoint}
              onLocationNameChange={setLocationName}
              onAddressLine1Change={setAddressLine1}
              onAddressLine2Change={setAddressLine2}
              onCityChange={setCity}
              onStateChange={setState}
              onZipChange={setZip}
              onMeetingPointChange={setMeetingPoint}
            />
          )}
          {currentStep === 'howMuch' && (
            <HowMuchStep
              amount={amount}
              description={costDescription}
              paymentType={paymentType}
              onAmountChange={setAmount}
              onDescriptionChange={setCostDescription}
              onPaymentTypeChange={setPaymentType}
            />
          )}
          {currentStep === 'why' && (
            <WhyStep
              description={description}
              cutoffTime={cutoffTime}
              onDescriptionChange={setDescription}
              onCutoffTimeChange={setCutoffTime}
            />
          )}
          {currentStep === 'preview' && (
            <PreviewStep
              eventName={eventName}
              minPeople={minPeople}
              maxPeople={maxPeople}
              date={date}
              time={time}
              locationName={locationName}
              addressLine1={addressLine1}
              addressLine2={addressLine2}
              city={city}
              state={state}
              zip={zip}
              meetingPoint={meetingPoint}
              amount={amount}
              costDescription={costDescription}
              paymentType={paymentType}
              description={description}
              imageUri={imageUri}
              onNext={() => setCurrentStep('invite')}
              onEditImage={handleEditImage}
              onEditBasics={handleEditBasics}
              onEditDateTime={handleEditDateTime}
              onEditLocation={handleEditLocation}
              onEditQuorum={handleEditQuorum}
              onEditDetails={handleEditDetails}
              onEditCosts={handleEditCosts}
              dates={dates}
              eventLink={eventLink}
              quorumMet={quorumMet}
              isEditing={isEditing}
            />
          )}
          {currentStep === 'invite' && (
            <InviteStep
              eventTitle={eventName}
              eventData={{
                name: eventName,
                date: date,
                time: time,
                locationName: locationName,
                addressLine1: addressLine1,
                addressLine2: addressLine2,
                city: city,
                state: state,
                zip: zip,
                meetingPoint: meetingPoint,
                minPeople: minPeople,
                maxPeople: maxPeople,
                description: description,
                imageUri: imageUri,
                dates: dates,
                amount: amount,
                costDescription: costDescription,
                payment_type: paymentType,
                event_link: eventLink,
                isMultiple: isMultiple,
                cutoffTime: cutoffTime,
                invitees: invitees,
              }}
              onNext={handleNext}
              onEventCreated={(eventId) => {
                console.log('Event created with ID:', eventId);
                setCreatedEventId(eventId);
              }}
              onResetForm={resetForm}
              isEditing={isEditing}
              eventId={eventId}
            />
          )}

          {currentStep !== 'preview' && currentStep !== 'invite' && (
            <View style={styles.nextButtonContainer}>
              <Button
                variant="default"
                style={styles.nextButton}
                textStyle={styles.nextButtonText}
                onPress={handleNext}
              >
                {currentStep === 'why' ? 'PREVIEW' : 'NEXT'}
              </Button>
            </View>
          )}
        </ScrollView>
        )}
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.primary,
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.background,
    zIndex: 10,
    flexDirection: 'column',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: `${Colors.primary}33`,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 20,
    width: '100%',
  },
  content: {
    paddingTop: 64,
    paddingBottom: 47,
    paddingLeft: 0,
    paddingRight: 0,
  },
  finished: {
    paddingTop: 0,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.white,
    marginTop: 20,
    marginBottom: 24,
    marginLeft: 20,
  },
  nextButtonContainer: {
    padding: 16,
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    marginTop: 40,
    marginBottom: 63,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButton: {
    backgroundColor: 'transparent',
    color: Colors.primary,
    borderColor: Colors.primary,
    borderWidth: 1,
    padding: 16,
    borderRadius: 0,
    fontWeight: '500',
    width: 200,
    height: 48,
    fontSize: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 16,
  },
});

export default CreateEventPage;
