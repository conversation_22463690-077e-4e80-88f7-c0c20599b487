import React, { useState, useEffect, useRef } from 'react';
import { View, Image, ScrollView, TouchableOpacity, Linking, StyleSheet, ActivityIndicator, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Send, Plus } from 'lucide-react-native';
import { CalendarIcon, LocationPinIcon, EditPenIcon, FriendsIcon, AvatarIcon, ClockIcon } from '@/assets/icons';

import { SignUpModal } from '@/components/SignUpModal/SignUpModal';
import { SignUpConfirmationModal } from '@/components/SignUpConfirmationModal/SignUpConfirmationModal';
import { eventService, eventDateService } from '@/services/event';
import { EventParticipant } from '@/services/eventParticipant';
import { messageService } from '@/services/message';
import { paymentService } from '@/services/payment';
import { userPreferenceService } from '@/services/userPreference';
import { Event as BaseEvent } from '@/data/events';
import { useAuthStore } from '@/stores/auth';
import { cloudfrontService } from '@/services/cloudfront';
import { contactService } from '@/services/contact';
import { connectRequestService, ConnectRequestResponse } from '@/services/connectRequest';
import Colors from '@/constants/Colors';
import moment from 'moment';
import DynamicStatusBar from '@/components/DynamicStatusBar';

// Extended ConnectRequestResponse to include user_id which exists in the database but not in the TypeScript interface
interface ExtendedConnectRequestResponse extends ConnectRequestResponse {
  user_id?: number;
}

// Interface for invitee details
interface InviteeDetail {
  user_id: number;
  user?: {
    id: number;
    display_name: string;
    first_name?: string;
    last_name?: string;
    email: string;
    profile_image_url?: string;
  };
}

// Extended EventResponse interface to include participants
interface EventResponse {
  id: number;
  name: string;
  date: string[]; // Array of timestamp strings
  location?: string;
  location_name?: string;
  address: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  meeting_point?: string;
  description?: string;
  event_link?: string;
  owner: string;
  owner_id?: number;
  image_url: string;
  has_options: boolean;
  quorum_met: boolean;
  cost?: number;
  cost_purpose?: string;
  payment_type?: string;
  participants?: number[]; // Array of user IDs
  participantDetails?: EventParticipant[]; // Participant details with user information
  participants_count?: number;
  paid_participants?: number[]; // Array of user IDs who have paid
  invitees?: number[]; // Array of invited user IDs
  inviteeDetails?: InviteeDetail[]; // Invitee details with user information
  quorum?: number; // Minimum number of people needed for the event to become official
  max_participants?: number | null; // Maximum number of participants allowed (null means unlimited)
  confirmed_date?: string; // The date that met quorum and was confirmed
  eventDates?: EventDateResponse[]; // Array of event dates
  multiple_dates?: boolean; // Whether users can select multiple dates
}

// Extended Event interface with additional properties
interface Event extends BaseEvent {
  description?: string;
  ticketUrl?: string;
  cost?: string;
  cost_purpose?: string; // Purpose of the cost
  payment_type?: string; // Type of payment (pay_me_back, chip_in, etc.)
  location?: string; // Location name
  location_name?: string; // Location name (new field)
  address_line1?: string; // Address line 1 (new field)
  address_line2?: string; // Address line 2 (new field)
  city?: string; // City (new field)
  state?: string; // State (new field)
  zip?: string; // ZIP code (new field)
  meeting_point?: string; // Meeting point (new field)
  owner_id?: number; // ID of the event creator
  participants?: number[]; // Array of user IDs
  participantDetails?: EventParticipant[]; // Participant details with user information
  participants_count?: number;
  paid_participants?: number[]; // Array of user IDs who have paid
  invitees?: number[]; // Array of invited user IDs
  inviteeDetails?: InviteeDetail[]; // Invitee details with user information
  quorum?: number; // Minimum number of people needed for the event to become official
  max_participants?: number | null; // Maximum number of participants allowed (null means unlimited)
  eventDates?: EventDateResponse[];
  confirmed_date?: string;
  multiple_dates?: boolean; // Whether users can select multiple dates
  ownerPreferences?: {
    venmo_enabled?: boolean;
    paypal_enabled?: boolean;
    venmo_username?: string;
    paypal_username?: string;
  }; // Payment preferences of the event owner
}

interface Message {
  id: number;
  text: string;
  user_id: number;
  event_id: number;
  created_at: string;
  user?: {
    id: number;
    display_name: string;
    first_name?: string;
    last_name?: string;
    profile_image_url?: string;
  };
  isCurrentUser?: boolean;
}

interface EventDateResponse {
  id: number;
  event_id: number;
  date: string; // Timestamp string
  participants: number[]; // Array of user IDs who have signed up for this specific date
  created_at: string;
  updated_at: string;
}

interface DateOption {
  id?: number; // Event date ID
  date: string;
  time: string;
  selected?: boolean;
}

// Helper function to check if an event is in the past
const checkIfEventIsPast = (event: Event): boolean => {
  // Check if the confirmed date is in the past
  if (event.confirmed_date) {
    const confirmedDate = new Date(event.confirmed_date);
    return confirmedDate < new Date();
  }
  // If no confirmed date, check if the last date in the event dates is in the past
  else if (event.eventDates && event.eventDates.length > 0) {
    // Sort dates and check if the last one is in the past
    const sortedDates = [...event.eventDates].sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
    const lastDate = new Date(sortedDates[sortedDates.length - 1].date);
    return lastDate < new Date();
  } else if (event.date && event.date.length > 0) {
    // Sort dates and check if the last one is in the past
    const sortedDates = [...event.date].sort((a, b) => {
      return new Date(a).getTime() - new Date(b).getTime();
    });
    const lastDate = new Date(sortedDates[sortedDates.length - 1]);
    return lastDate < new Date();
  }
  return false;
};

// Helper function to check if an event is within the response cutoff period
const checkIfEventIsWithinCutoff = (event: Event): boolean => {
  if (event.date && event.date.length > 0 && event.response_cutoff) {
    try {
      const earliestDate = event.date.reduce((earliest, dateStr) => {
        const date = new Date(dateStr);
        return date < earliest ? date : earliest;
      }, new Date(event.date[0]));

      const cutoffTimeMs = event.response_cutoff * 60 * 60 * 1000; // Convert hours to milliseconds
      const now = new Date();

      // Check if the event is within the response cutoff period
      return earliestDate.getTime() - now.getTime() < cutoffTimeMs;
    } catch (error) {
      console.error('Error checking response cutoff:', error);
    }
  }
  return false;
};

// Edit Button Component
interface EditButtonProps {
  onPress: () => void;
  style?: 'image' | 'section';
  isVisible: boolean;
}

const EditButton: React.FC<EditButtonProps> = ({ onPress, style = 'section', isVisible }) => {
  if (!isVisible) return null;

  return (
    <TouchableOpacity
      style={style === 'image' ? styles.editButton : styles.sectionEditButton}
      onPress={onPress}
    >
      <EditPenIcon size={16} color={Colors.primary} />
    </TouchableOpacity>
  );
};

// Helper function to convert API event to frontend event model
const mapApiEventToFrontend = (apiEvent: EventResponse): Event => {
  // Extract location name from address if not provided
  const locationName = apiEvent.location || apiEvent.address.split(',')[0];

  // If confirmed_date exists, use only that date
  let eventDates = apiEvent.date || [];
  if (apiEvent.confirmed_date && apiEvent.quorum_met) {
    console.log(`Event ${apiEvent.id} has confirmed date:`, apiEvent.confirmed_date);
    // Use only the confirmed date
    eventDates = [apiEvent.confirmed_date];
  }

  return {
    id: apiEvent.id,
    name: apiEvent.name,
    date: eventDates, // Use confirmed_date if available, otherwise use all dates
    address: apiEvent.address,
    owner: apiEvent.owner,
    owner_id: apiEvent.owner_id,
    imageURL: apiEvent.image_url || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png', // We'll get signed URL later
    hasOptions: apiEvent.has_options,
    quorumMet: apiEvent.quorum_met,
    isAtCapacity: false, // Default value, will need to be calculated based on participants
    userStatus: null, // Default value, will need to be determined based on user's relationship to event

    // Include participants from the API response
    participants: apiEvent.participants || [],
    participantDetails: apiEvent.participantDetails || [],
    participants_count: apiEvent.participants_count || 0,
    paid_participants: apiEvent.paid_participants || [],

    // Include invitees from the API response
    invitees: apiEvent.invitees || [],
    inviteeDetails: apiEvent.inviteeDetails || [],

    // Include quorum and max_participants from the API response
    quorum: apiEvent.quorum,
    max_participants: apiEvent.max_participants,
    eventDates: apiEvent.eventDates || [],
    confirmed_date: apiEvent.confirmed_date,
    multiple_dates: apiEvent.multiple_dates || false,

    // Extended properties with mock data (in a real app, these would come from the API)
    description: apiEvent.description || 'No description provided',
    ticketUrl: apiEvent.event_link || undefined,
    cost: apiEvent.cost === 0 ? 'Free' : apiEvent.cost ? `$${apiEvent.cost}` : undefined,
    cost_purpose: apiEvent.cost === 0 ? undefined : apiEvent.cost_purpose,
    payment_type: apiEvent.payment_type,
    location: locationName,

    // New address fields
    location_name: apiEvent.location_name,
    address_line1: apiEvent.address_line1,
    address_line2: apiEvent.address_line2,
    city: apiEvent.city,
    state: apiEvent.state,
    zip: apiEvent.zip,
    meeting_point: apiEvent.meeting_point,
  };
};

const EventPage = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const eventId = params.id ? parseInt(params.id as string) : 0;
  const refreshParam = params.refresh; // Get the refresh parameter

  // Get the current user from auth store
  const { isLoggedIn, userData } = useAuthStore();

  // Get isCreator from params or from event data
  const [isCreator, setIsCreator] = useState(params.isCreator === 'true');

  const [isSignUpModalOpen, setIsSignUpModalOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [selectedDates, setSelectedDates] = useState<DateOption[]>([]);
  const [dateOptions, setDateOptions] = useState<DateOption[]>([]);

  // State for event data
  const [event, setEvent] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSigningUp, setIsSigningUp] = useState(false);
  const [isUserParticipant, setIsUserParticipant] = useState(false);
  const [isUserPaid, setIsUserPaid] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [isPastOrWithinCutoff, setIsPastOrWithinCutoff] = useState(false);
  const [isPastEvent, setIsPastEvent] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageText, setMessageText] = useState('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [signedImageUrl, setSignedImageUrl] = useState<string | undefined>(undefined);
  const messagesIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messagesScrollViewRef = useRef<ScrollView>(null);

  // Debug useEffect for dateOptions
  useEffect(() => {
    if (isSignUpModalOpen) {
      console.log('SignUpModal opened with dateOptions:', dateOptions);
    }
  }, [isSignUpModalOpen, dateOptions]);

  // Update signed image URL when event changes
  useEffect(() => {
    const updateSignedImageUrl = async () => {
      if (event?.imageURL) {
        try {
          // Check if this is an S3 URL that needs signing
          const isS3Url = cloudfrontService.isS3Url(event.imageURL);

          if (isS3Url) {
            // console.log('Updating signed S3 URL:', event.imageURL);
            const signedUrl = await cloudfrontService.getSignedUrl(event.imageURL);
            setSignedImageUrl(signedUrl);
          } else {
            // console.log('Not an S3 URL, using as is:', event.imageURL);
            setSignedImageUrl(event.imageURL);
          }
        } catch (error) {
          console.error('Error getting signed URL in useEffect:', error);
          // Keep the original URL if signing fails
          setSignedImageUrl(event.imageURL);
        }
      }
    };

    updateSignedImageUrl();
  }, [event?.imageURL]);

  // Fetch event data when component mounts or refreshParam changes
  useEffect(() => {
    console.log(`Fetching event data. EventID: ${eventId}, RefreshParam: ${refreshParam}`);
    const fetchEventData = async () => {
      if (!eventId) {
        setError('No event ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await eventService.getEventById(eventId);

        if (response.error) {
          setError(response.error);
          setIsLoading(false);
          return;
        }

        if (response.data) {
          const eventData = mapApiEventToFrontend(response.data);

          // Get signed URL for the image
          if (eventData.imageURL) {
            try {
              // Check if this is an S3 URL that needs signing
              const isS3Url = cloudfrontService.isS3Url(eventData.imageURL);

              if (isS3Url) {
                // console.log('Signing S3 URL:', eventData.imageURL);
                const signedUrl = await cloudfrontService.getSignedUrl(eventData.imageURL);
                setSignedImageUrl(signedUrl);
              } else {
                // console.log('Not an S3 URL, using as is:', eventData.imageURL);
                setSignedImageUrl(eventData.imageURL);
              }
            } catch (error) {
              console.error('Error getting signed URL:', error);
              // Keep the original URL if signing fails
              setSignedImageUrl(eventData.imageURL);
            }
          }

          // Fetch the owner's user preferences if payment_type is pay_me_back
          if (eventData.payment_type === 'pay_me_back' && eventData.owner_id) {
            try {
              const preferencesResponse = await userPreferenceService.getUserPreferenceByUserId(eventData.owner_id);
              if (preferencesResponse.data) {
                // Add the owner's payment preferences to the event data
                eventData.ownerPreferences = {
                  venmo_enabled: preferencesResponse.data.venmo_enabled,
                  paypal_enabled: preferencesResponse.data.paypal_enabled,
                  venmo_username: preferencesResponse.data.venmo_username,
                  paypal_username: preferencesResponse.data.paypal_username
                };
              }
            } catch (error) {
              console.error('Error fetching owner preferences:', error);
            }
          }

          setEvent(eventData);

          // Check if the current user is the creator
          const isUserCreator = userData?.id ? eventData.owner_id === userData.id : false;
          setIsCreator(isUserCreator);

          // Check if the current user is already a participant
          if (userData?.id && eventData.participants) {
            const isParticipant = eventData.participants.includes(userData.id);
            setIsUserParticipant(isParticipant);

            // Check if the current user has already paid
            if (eventData.paid_participants) {
              const isPaid = eventData.paid_participants.includes(userData.id);
              setIsUserPaid(isPaid);
            }
          }

          // Check if the event is past or within response cutoff period
          const isPastEventCheck = checkIfEventIsPast(eventData);
          const isWithinCutoff = checkIfEventIsWithinCutoff(eventData);
          setIsPastEvent(isPastEventCheck);
          setIsPastOrWithinCutoff(isPastEventCheck || isWithinCutoff);

          // Convert event dates to date options for the SignUpModal
          if (eventData.eventDates && eventData.eventDates.length > 0) {
            console.log(`Event has ${eventData.eventDates.length} dates:`, eventData.eventDates);

            // If the event has a confirmed date and quorum is met, only show that date
            let datesToShow = eventData.eventDates;
            if (eventData.confirmed_date && eventData.quorumMet) {
              console.log('Event has confirmed date, only showing that date');
              // Find the event date that matches the confirmed date
              const confirmedDateObj = new Date(eventData.confirmed_date);
              datesToShow = eventData.eventDates.filter(eventDate => {
                const eventDateObj = new Date(eventDate.date);
                return eventDateObj.getTime() === confirmedDateObj.getTime();
              });

              if (datesToShow.length === 0) {
                console.log('Could not find matching event date for confirmed date, using all dates');
                datesToShow = eventData.eventDates;
              } else {
                console.log('Found matching event date for confirmed date:', datesToShow);
              }
            }

            const options = datesToShow.map(eventDate => {
              const date = moment(eventDate.date);
              return {
                id: eventDate.id,
                date: date.format('dddd, MMMM D, YYYY'),
                time: date.format('h:mm A'),
                selected: false
              };
            });
            console.log('Setting date options:', options);
            setDateOptions(options);
          } else {
            console.log('No event dates found in eventData:', eventData);
          }

          // quorumMet is now determined directly from the event data
        }
      } catch (err) {
        console.error('Error fetching event:', err);
        setError('Failed to load event. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventData();
  }, [eventId, userData, refreshParam]); // Add refreshParam to dependencies to reload when it changes

  // Reusable function to fetch messages
  const fetchMessages = async (showLoading = true) => {
    if (!eventId) return;

    try {
      if (showLoading) setIsLoadingMessages(true);
      const response = await messageService.getEventMessages(eventId);

      if (response.error) {
        console.error('Error fetching messages:', response.error);
        return;
      }

      if (response.data) {
        // Map messages and mark current user's messages
        const mappedMessages = response.data.map(msg => ({
          ...msg,
          isCurrentUser: userData?.id === msg.user_id
        }));

        setMessages(mappedMessages);

        // Sign profile image URLs for messages
        const signedUrls: Record<string, string> = {};
        for (const message of mappedMessages) {
          if (message.user?.profile_image_url) {
            const userId = message.user_id.toString();
            try {
              // Only sign S3 URLs
              if (cloudfrontService.isS3Url(message.user.profile_image_url)) {
                const signedUrl = await cloudfrontService.getSignedUrl(message.user.profile_image_url);
                signedUrls[userId] = signedUrl;
              } else {
                signedUrls[userId] = message.user.profile_image_url;
              }
            } catch (error) {
              console.error(`Error signing profile image URL for message user ${userId}:`, error);
              // Use original URL as fallback
              signedUrls[userId] = message.user.profile_image_url;
            }
          }
        }
        setSignedMessageProfileUrls(signedUrls);

        // Scroll to bottom of messages
        setTimeout(() => {
          messagesScrollViewRef.current?.scrollToEnd({ animated: false });
        }, 100);
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
    } finally {
      if (showLoading) setIsLoadingMessages(false);
    }
  };

  // Fetch messages and set up polling - only if user is a participant or creator
  useEffect(() => {
    // Only fetch messages if the user is a participant or the creator
    if (!isUserParticipant && !isCreator) return;

    // Fetch messages immediately
    fetchMessages();

    // Set up polling every 5 seconds (don't show loading for background polling)
    messagesIntervalRef.current = setInterval(() => fetchMessages(false), 5000);

    // Clean up interval on unmount
    return () => {
      if (messagesIntervalRef.current) {
        clearInterval(messagesIntervalRef.current);
      }
    };
  }, [eventId, userData, isUserParticipant, isCreator]);

  const handleSendMessage = async () => {
    if (!messageText.trim() || !eventId || !isLoggedIn) return;

    try {
      setIsSendingMessage(true);

      const response = await messageService.createMessage(eventId, { text: messageText });

      if (response.error) {
        Alert.alert('Error', 'Failed to send message. Please try again.');
        return;
      }

      if (response.data) {
        // Clear input immediately for better UX
        setMessageText('');

        // Trigger an immediate fetch to show the new message without waiting for polling
        // This prevents duplicates while providing immediate feedback
        setTimeout(() => {
          fetchMessages(false); // Don't show loading spinner for this fetch
          // Scroll to bottom after messages are updated
          setTimeout(() => {
            messagesScrollViewRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }, 200); // Small delay to ensure the message is saved on the server
      }
    } catch (err) {
      console.error('Error sending message:', err);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsSendingMessage(false);
    }
  };

  const handleEdit = (step?: string, inline: boolean = false) => {
    // Navigate to edit-event with the current event ID for editing
    console.log('Navigating to edit-event with eventId:', eventId, 'step:', step, 'inline:', inline);
    const params: any = { eventId: eventId.toString() };
    if (step) {
      params.step = step;
    }
    if (inline) {
      params.inline = 'true';
    }
    router.push({
      pathname: '/(tabs)/edit-event',
      params
    });
  };

  const handleEditDateTime = () => {
    // Prevent editing date/time if quorum is met
    if (event?.quorumMet) {
      console.log('Cannot edit date/time because quorum is met');
      return;
    }
    handleEdit('when', true); // Inline editing
  };
  const handleEditLocation = () => handleEdit('where', true); // Inline editing
  const handleEditQuorum = () => handleEdit('quorum', true); // Inline editing
  const handleEditDetails = () => handleEdit('why', true); // Inline editing
  const handleEditCosts = () => handleEdit('howMuch', true); // Inline editing
  const handleEditInvitees = () => handleEdit('invite', true); // Inline editing

  const handleCancel = () => {
    Alert.alert(
      'Cancel Event',
      'Are you sure you want to cancel this event? This will delete the event permanently.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel Event',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              const response = await eventService.deleteEvent(eventId);

              if (response.error) {
                Alert.alert('Error', response.error || 'Failed to cancel the event');
                setIsLoading(false);
                return;
              }

              Alert.alert(
                'Success',
                'Event has been cancelled successfully',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // Navigate to home with a refresh parameter to trigger events reload
                      router.replace({
                        pathname: '/(tabs)/home',
                        params: { refresh: Date.now().toString() }
                      });
                    }
                  }
                ]
              );
            } catch (error) {
              console.error('Error cancelling event:', error);
              Alert.alert('Error', 'Failed to cancel the event. Please try again.');
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleSignUp = () => {
    // Check if user is authenticated
    if (!userData?.id || !isLoggedIn) {
      Alert.alert('Authentication Required', 'Please sign in to join this event.');
      return;
    }

    // Check if user is already a participant
    if (isUserParticipant) {
      Alert.alert('Already Joined', 'You are already signed up for this event.');
      return;
    }

    // Log the current dateOptions before opening the modal
    console.log('Opening SignUpModal with dateOptions:', dateOptions);

    // Check if we have any date options
    if (dateOptions.length === 0 && event?.eventDates && event.eventDates.length > 0) {
      // If dateOptions is empty but we have eventDates, convert them now
      console.log('Converting event dates to options on the fly');

      // If the event has a confirmed date and quorum is met, only show that date
      let datesToShow = event.eventDates;
      if (event.confirmed_date && event.quorumMet) {
        console.log('Event has confirmed date, only showing that date');
        // Find the event date that matches the confirmed date
        const confirmedDateObj = new Date(event.confirmed_date);
        datesToShow = event.eventDates.filter(eventDate => {
          const eventDateObj = new Date(eventDate.date);
          return eventDateObj.getTime() === confirmedDateObj.getTime();
        });

        if (datesToShow.length === 0) {
          console.log('Could not find matching event date for confirmed date, using all dates');
          datesToShow = event.eventDates;
        } else {
          console.log('Found matching event date for confirmed date:', datesToShow);
        }
      }

      const options = datesToShow.map(eventDate => {
        const date = moment(eventDate.date);
        return {
          id: eventDate.id,
          date: date.format('dddd, MMMM D, YYYY'),
          time: date.format('h:mm A'),
          selected: false
        };
      });
      setDateOptions(options);
    }

    // Open the SignUpModal to select dates
    setIsSignUpModalOpen(true);
  };

  const handleSignUpForDates = async (selectedDateIds: number[]) => {
    if (!eventId || selectedDateIds.length === 0) return;

    try {
      setIsSigningUp(true);

      // Call the API to sign up for the selected dates
      const response = await eventDateService.signUpForMultipleDates(eventId, selectedDateIds);

      if (response.error) {
        Alert.alert('Error', response.error);
        return false;
      }

      // Update the event with the new data from the response
      if (response.data && response.data.updatedEvent) {
        const updatedEvent = mapApiEventToFrontend(response.data.updatedEvent);
        setEvent(updatedEvent);
        setIsUserParticipant(true);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error signing up for dates:', error);
      Alert.alert('Error', 'Failed to sign up for the selected dates. Please try again.');
      return false;
    } finally {
      setIsSigningUp(false);
    }
  };

  const handleDone = (dates: DateOption[]) => {
    const selected = dates.filter(date => date.selected);
    setSelectedDates(selected);
    setIsSignUpModalOpen(false);
    setIsConfirmationModalOpen(true);
  };

  const handleClose = async () => {
    // Get the IDs of the selected dates
    const selectedDateIds = selectedDates
      .filter(date => date.id !== undefined)
      .map(date => date.id as number);

    // Sign up for the selected dates
    const success = await handleSignUpForDates(selectedDateIds);

    if (success) {
      setIsConfirmationModalOpen(false);
      // Refresh the event data
      const response = await eventService.getEventById(eventId);
      if (response.data) {
        const updatedEvent = mapApiEventToFrontend(response.data);
        setEvent(updatedEvent);
      }
    } else {
      setIsConfirmationModalOpen(false);
    }
  };

  const handleCancelSignUp = () => {
    setIsConfirmationModalOpen(false);
  };

  // Function to handle opening payment links
  const handleOpenPaymentLink = async () => {
    if (!event) return;

    // Extract payment information
    const { ownerPreferences, name, cost_purpose } = event;
    const costAmount = event.cost ? event.cost.replace('$', '') : '';

    // Check if owner has payment preferences
    if (!ownerPreferences) {
      Alert.alert('Payment Error', 'The event owner has not set up payment preferences.');
      return;
    }

    // Mark the user as paid in the database
    try {
      const costAmount = event.cost ? parseFloat(event.cost.replace('$', '')) : undefined;
      const response = await paymentService.markAsPaid(eventId, event.name, costAmount);

      if (response.data && response.data.success && userData?.id) {
        // Update the local state to reflect that the user has paid
        setIsUserPaid(true);

        // Update the event object with the new paid_participants array
        if (event) {
          const updatedPaidParticipants = [...(event.paid_participants || []), userData.id];
          setEvent({
            ...event,
            paid_participants: updatedPaidParticipants
          });
        }
      }
    } catch (error) {
      console.error('Error marking user as paid:', error);
      // Continue with payment link even if marking as paid fails
    }

    // Check which payment methods are available
    const hasVenmo = ownerPreferences.venmo_enabled && ownerPreferences.venmo_username;
    const hasPayPal = ownerPreferences.paypal_enabled && ownerPreferences.paypal_username;

    if (!hasVenmo && !hasPayPal) {
      Alert.alert('Payment Error', 'The event owner has not set up any payment methods.');
      return;
    }

    // If both payment methods are available, show a prompt to choose
    if (hasVenmo && hasPayPal) {
      Alert.alert(
        'Choose Payment Method',
        'How would you like to pay?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Venmo',
            onPress: () => openVenmoLink(ownerPreferences.venmo_username!, costAmount, name, cost_purpose)
          },
          {
            text: 'PayPal',
            onPress: () => openPayPalLink(ownerPreferences.paypal_username!, costAmount)
          }
        ]
      );
      return;
    }

    // If only one payment method is available, use that
    if (hasVenmo) {
      openVenmoLink(ownerPreferences.venmo_username!, costAmount, name, cost_purpose);
    } else if (hasPayPal) {
      openPayPalLink(ownerPreferences.paypal_username!, costAmount);
    }
  };

  // Function to open Venmo link
  const openVenmoLink = (username: string, amount: string, eventName: string, purpose?: string) => {
    // Create the note for the payment
    const note = `For ${eventName}${purpose ? ` ${purpose}` : ''}`;

    // Create the Venmo URL
    // Format: https://venmo.com/username?txn=pay&amount=X&note=For+Event+Name
    const venmoUrl = `https://venmo.com/${username}?txn=pay&amount=${amount}&note=${encodeURIComponent(note)}`;

    // Open the Venmo URL
    Linking.openURL(venmoUrl).catch(err => {
      console.error('Error opening Venmo:', err);
      Alert.alert('Error', 'Could not open Venmo. Please try again.');
    });
  };

  // Function to open PayPal link
  const openPayPalLink = (username: string, amount: string) => {
    // Create the PayPal.me URL
    // Format: https://paypal.me/username/amount
    const paypalUrl = `https://paypal.me/${username}/${amount}`;

    // Open the PayPal URL
    Linking.openURL(paypalUrl).catch(err => {
      console.error('Error opening PayPal:', err);
      Alert.alert('Error', 'Could not open PayPal. Please try again.');
    });
  };

  // Handle the Pay Now button press
  const handlePayNow = async () => {
    if (!eventId || !isLoggedIn || !userData?.id) {
      Alert.alert('Authentication Required', 'Please sign in to pay for this event.');
      return;
    }

    // Check if user is a participant
    if (!isUserParticipant) {
      Alert.alert('Not Signed Up', 'You need to sign up for this event before you can pay.');
      return;
    }

    // If payment type is pay_me_back, open the appropriate payment link
    if (event?.payment_type === 'pay_me_back') {
      handleOpenPaymentLink();
      return;
    }

    try {
      setIsProcessingPayment(true);

      // Call the API to mark the user as having paid
      const costAmount = event?.cost ? parseFloat(event.cost.replace('$', '')) : undefined;
      const response = await paymentService.markAsPaid(eventId, event?.name, costAmount);

      if (response.error) {
        Alert.alert('Error', response.error || 'Failed to process payment');
        return;
      }

      if (response.data && response.data.success) {
        // Update the local state to reflect that the user has paid
        setIsUserPaid(true);

        // Update the event object with the new paid_participants array
        if (event && userData.id) {
          const updatedPaidParticipants = [...(event.paid_participants || []), userData.id];
          setEvent({
            ...event,
            paid_participants: updatedPaidParticipants
          });
        }

        Alert.alert('Success', 'Payment marked as successful');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      Alert.alert('Error', 'Failed to process payment. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // Helper function to get human-readable payment type
  const getPaymentTypeText = (type?: string) => {
    if (!type) return '';

    switch (type) {
      case 'pay_me_back': return 'Pay me back';
      case 'chip_in': return 'Chip in';
      case 'buy_ticket': return 'Buy a ticket';
      case 'bring_wallet': return 'Bring your wallet';
      default: return '';
    }
  };

  // Define the attendee interface
  interface Attendee {
    id: string;
    name: string;
    status?: string;
    isCurrentUser?: boolean;
    isCreator?: boolean;
    profileImageUrl?: string;
  }

  // We use the EventParticipant type from the service for participant status

  // Handle marking a user as paid
  const handleMarkAsPaid = async (userId: number) => {
    if (!eventId || !isLoggedIn) {
      Alert.alert('Authentication Required', 'Please sign in to mark payments.');
      return;
    }

    try {
      setIsProcessingPayment(true);

      // Call the API to mark the user as having paid
      const costAmount = event?.cost ? parseFloat(event.cost.replace('$', '')) : undefined;
      const response = await paymentService.markAsPaid(eventId, event?.name, costAmount);

      if (response.error) {
        Alert.alert('Error', response.error || 'Failed to process payment');
        return;
      }

      if (response.data && response.data.success) {
        // Update the event object with the new paid_participants array
        if (event) {
          // Add the userId to the paid_participants array
          const updatedPaidParticipants = [...(event.paid_participants || [])];
          if (!updatedPaidParticipants.includes(userId)) {
            updatedPaidParticipants.push(userId);
          }

          setEvent({
            ...event,
            paid_participants: updatedPaidParticipants
          });
        }

        Alert.alert('Success', 'Payment marked as successful');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      Alert.alert('Error', 'Failed to process payment. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // State for signed profile image URLs
  const [signedProfileUrls, setSignedProfileUrls] = useState<Record<string, string>>({});
  const [signedMessageProfileUrls, setSignedMessageProfileUrls] = useState<Record<string, string>>({});
  const [userContacts, setUserContacts] = useState<{id: number, user_id?: number}[]>([]);
  const [sendingConnectRequest, setSendingConnectRequest] = useState<number | null>(null);
  const [pendingConnectRequests, setPendingConnectRequests] = useState<ExtendedConnectRequestResponse[]>([]);

  // Fetch user's contacts and pending connect requests when component mounts
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userData?.id) return;

      try {
        // Fetch contacts
        const contactsResponse = await contactService.getAllContacts();
        if (contactsResponse.data) {
          setUserContacts(contactsResponse.data);
        }

        // Fetch pending connect requests
        const connectRequestsResponse = await connectRequestService.getAllConnectRequests();
        if (connectRequestsResponse.data) {
          // Filter to only include pending requests (not accepted or declined)
          const pendingRequests = connectRequestsResponse.data.filter(
            req => !req.is_accepted && !req.is_declined
          );
          setPendingConnectRequests(pendingRequests);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, [userData?.id]);

  // Check if a user is already a contact of the current user
  const isUserContact = (userId: number): boolean => {
    return userContacts.some(contact => contact.user_id === userId);
  };

  // Check if a connect request has already been sent to a user
  const hasPendingConnectRequest = (userId: number): boolean => {
    return pendingConnectRequests.some(request => request.user_id === userId || request.email === `user-${userId}@placeholder.com`);
  };

  // Handle sending a connect request to a user
  const handleSendConnectRequest = async (attendee: Attendee) => {
    if (!userData?.id || !attendee) return;

    const userId = parseInt(attendee.id);
    setSendingConnectRequest(userId);

    try {
      // Find the participant details to get the complete user information
      const participantDetail = event?.participantDetails?.find(p => p.user_id === userId);

      if (!participantDetail || !participantDetail.user) {
        // If we don't have the participant details, fetch the user data
        Alert.alert('Error', 'Could not find user details. Please try again.');
        return;
      }

      // Extract user information from participant details
      const { user } = participantDetail;

      // Create connect request with the complete user information
      const response = await connectRequestService.createConnectRequest({
        first_name: user.first_name || user.display_name.split(' ')[0] || 'Unknown',
        last_name: user.last_name || user.display_name.split(' ').slice(1).join(' ') || undefined,
        email: user.email // Use the actual user email
      });

      if (response.error) {
        Alert.alert('Error', response.error);
        return;
      }

      // Show success message
      Alert.alert(
        'Connect Request Sent',
        'A connect request has been sent to the user.'
      );

      // Add the new connect request to the pending requests
      if (response.data) {
        setPendingConnectRequests(prev => [...prev, response.data as ExtendedConnectRequestResponse]);
      }

      // Refresh contacts list
      const contactsResponse = await contactService.getAllContacts();
      if (contactsResponse.data) {
        setUserContacts(contactsResponse.data);
      }

    } catch (error) {
      console.error('Error sending connect request:', error);
      Alert.alert('Error', 'Failed to send connect request');
    } finally {
      setSendingConnectRequest(null);
    }
  };

  // Convert participants to attendees for display
  const getAttendees = (event: Event | null): Attendee[] => {
    if (!event || !event.participants) return [];

    // Map participants to attendees format
    const attendeesList: Attendee[] = [];

    // Process each participant ID
    event.participants.forEach((userId) => {
      // Find participant details if available
      const participantDetail = event.participantDetails?.find(p => p.user_id === userId);

      attendeesList.push({
        id: userId.toString(),
        name: participantDetail?.user?.display_name || `User ${userId}`,
        status: participantDetail?.status || 'going',
        isCurrentUser: userData?.id === userId,
        isCreator: event.owner_id === userId,
        profileImageUrl: participantDetail?.user?.profile_image_url
      });
    });

    // Add the event owner if they're not already in the list
    if (event.owner && event.owner_id !== undefined && event.owner_id !== null) {
      // Check if the owner is already in the list
      const ownerIdStr = event.owner_id.toString();
      if (!attendeesList.some(a => a.id === ownerIdStr)) {
      attendeesList.unshift({
        id: event.owner_id.toString(),
        name: event.owner,
        status: 'going',
        isCreator: true
      } as Attendee);
      }
    }

    return attendeesList;
  };

  const attendees = getAttendees(event);

  // Sign profile image URLs when event changes
  useEffect(() => {
    const signProfileImages = async () => {
      if (!event || !event.participantDetails) return;

      const signedUrls: Record<string, string> = {};

      // Process participant details directly from the event
      for (const participant of event.participantDetails) {
        if (participant.user?.profile_image_url) {
          const userId = participant.user_id.toString();
          try {
            // Only sign S3 URLs
            if (cloudfrontService.isS3Url(participant.user.profile_image_url)) {
              const signedUrl = await cloudfrontService.getSignedUrl(participant.user.profile_image_url);
              signedUrls[userId] = signedUrl;
            } else {
              signedUrls[userId] = participant.user.profile_image_url;
            }
          } catch (error) {
            console.error(`Error signing profile image URL for participant ${userId}:`, error);
            // Use original URL as fallback
            signedUrls[userId] = participant.user.profile_image_url;
          }
        }
      }

      setSignedProfileUrls(signedUrls);
    };

    if (event) {
      signProfileImages();
    }
  }, [event]);

  return (
    <>
      <DynamicStatusBar backgroundColor={event?.quorumMet ? Colors.background : Colors.primaryDark} barStyle="light-content" />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 20}
      >
      <View style={styles.wrapper}>
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
        >
          {isLoading ? (
            <View style={[styles.centeredContainer, { padding: 20 }]}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={{ marginTop: 10, fontSize: 16, color: Colors.white }}>Loading event...</Text>
            </View>
          ) : error ? (
            <View style={[styles.centeredContainer, { padding: 20 }]}>
              <Text style={{ marginBottom: 10, fontSize: 16, color: '#E35D5D' }}>{error}</Text>
              <TouchableOpacity
                style={[styles.primaryButton, { width: 200 }]}
                onPress={() => router.push('/home')}
              >
                <Text style={styles.buttonText}>Go Back</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              {/* Show creator controls if user is the creator and event is not past */}
              {isCreator && !isPastEvent && (
                <View style={styles.header}>
                  <Text style={styles.headerText}>Your event is live.</Text>
                  <View style={styles.headerLinks}>
                    <TouchableOpacity onPress={() => handleEdit()}>
                      <Text style={styles.headerLink}>Edit</Text>
                    </TouchableOpacity>
                    <Text style={styles.headerLinkText}> or </Text>
                    <TouchableOpacity onPress={handleCancel}>
                      <Text style={styles.headerLink}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {/* Show quorum banner for all events */}
              <View style={[styles.quorumHeader, event?.quorumMet ? styles.quorumHeaderMet : null]}>
                <Text style={styles.quorumHeaderText}>
                  {event?.quorumMet ? 'QUORUM MET!' : 'QUORUM NOT YET MET'}
                </Text>
              </View>

              <View style={styles.previewContent}>
                <View style={styles.previewImage}>
                  <Image
                    source={{ uri: signedImageUrl || event?.imageURL }}
                    style={styles.eventImage}
                    onError={(e) => console.error('Image loading error:', e.nativeEvent.error)}
                  />
                  <EditButton
                    onPress={() => handleEdit('what', true)}
                    style="image"
                    isVisible={isCreator && !isPastEvent}
                  />
                </View>

                <View style={styles.previewSection}>
                  <Text style={styles.eventTitle}>{event?.name || "Event"}</Text>
                </View>

                <View style={styles.previewSection}>
                  <View style={styles.sectionWithEdit}>
                    <View style={styles.dateTimeList}>
                      {event?.date && event.date.length > 0 ? (
                        // Display all dates with OR between them
                        event.date.map((dateStr, index) => {
                          // Format the date string using moment for consistent cross-platform display
                          const date = moment(dateStr);
                          const formattedDate = {
                            date: date.format('dddd, MMMM D, YYYY'),
                            time: date.format('h:mm A')
                          };

                          return (
                            <View key={index} style={styles.dateTimeItem}>
                              {index === 0 ? (
                                <CalendarIcon size={20} color={Colors.secondary} />
                              ) : (
                                <View style={styles.orContainer}>
                                  <Text style={styles.orText}>OR</Text>
                                </View>
                              )}
                              <View style={styles.dateTimeContent}>
                                <Text style={[styles.bodyText, styles.dateText]}>{formattedDate.date}</Text>
                                <Text style={styles.bodyText}>{formattedDate.time}</Text>
                              </View>
                            </View>
                          );
                        })
                      ) : (
                        <View style={styles.dateTimeItem}>
                          <CalendarIcon size={20} color={Colors.secondary} />
                          <View style={styles.dateTimeContent}>
                            <Text style={[styles.bodyText, styles.dateText]}>Date TBD</Text>
                          </View>
                        </View>
                      )}
                    </View>
                    <EditButton
                      onPress={handleEditDateTime}
                      isVisible={isCreator && !event?.quorumMet && !isPastEvent}
                    />
                  </View>
                </View>

                {/* Only show location section if we have location data */}
                {((event?.location_name && event.location_name.trim() !== '') ||
                  (event?.location && event.location.trim() !== '') ||
                  (event?.address && event.address.trim() !== '')) && (
                  <View style={styles.previewSection}>
                    <View style={styles.sectionWithEdit}>
                      <View style={styles.locationInfo}>
                        <TouchableOpacity
                          style={styles.locationInfo}
                          onPress={() => {
                            const location = event?.location || '';
                            if (location && location.trim() !== '') {
                              // Use Google Maps search URL for cross-platform compatibility
                              const encodedLocation = encodeURIComponent(location);
                              const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
                              Linking.openURL(googleMapsUrl);
                            }
                          }}
                        >
                          <View style={styles.locationHeader}>
                            <LocationPinIcon size={18} color={Colors.secondary} />
                            <Text style={styles.locationName}>
                              {(event?.location_name && event.location_name.trim() !== '') ?
                                event.location_name :
                                (event?.location && event.location.trim() !== '') ?
                                  event.location : "Location"}
                            </Text>
                          </View>
                          {event?.address && event.address.trim() !== '' && (
                            <Text style={styles.address}>{event.address}</Text>
                          )}
                          {event?.meeting_point && event.meeting_point.trim() !== '' && (
                            <Text style={styles.meetingPoint}>Meeting point: {event.meeting_point}</Text>
                          )}
                        </TouchableOpacity>
                      </View>
                      <EditButton
                        onPress={handleEditLocation}
                        isVisible={isCreator && !isPastEvent}
                      />
                    </View>
                  </View>
                )}

            <View style={styles.previewSection}>
              <View style={styles.sectionWithEdit}>
                <View style={styles.quorumInfo}>
                  <View style={styles.quorumIcon}>
                    <FriendsIcon size={20} color={Colors.secondary} />
                  </View>
                  <View style={styles.quorumContent}>
                    <View style={styles.quorumItem}>
                      <Text style={styles.quorumLabel}>Quorum</Text>
                      <Text style={[
                        styles.quorumNumber,
                        event?.quorumMet ? styles.quorumNumberHighlighted : undefined
                      ]}>{event?.quorum || 1}</Text>
                    </View>
                    {event?.max_participants && (
                      <View style={styles.quorumItem}>
                      <Text style={styles.quorumLabel}>Maximum</Text>
                      <Text style={[
                        styles.quorumNumber,
                        event?.max_participants && event?.participants_count &&
                        event.participants_count >= event.max_participants ?
                        styles.quorumNumberHighlighted : undefined
                      ]}>{event?.max_participants}</Text>
                    </View>)}
                  </View>
                </View>
                <EditButton
                  onPress={handleEditQuorum}
                  isVisible={isCreator && !isPastEvent}
                />
              </View>
            </View>

            <View style={styles.previewSection}>
              <View style={styles.sectionWithEdit}>
                <View style={styles.detailsSection}>
                  <Text style={styles.detailsTitle}>DETAILS</Text>
                  <Text style={styles.detailsText}>
                    {event?.description || "No details available for this event."}
                  </Text>
                </View>
                <EditButton
                  onPress={handleEditDetails}
                  isVisible={isCreator && !isPastEvent}
                />
              </View>
            </View>

            <View style={styles.previewSection}>
              <View style={styles.whoIsGoingSection}>
                <Text style={styles.whoIsGoingTitle}>WHO'S GOING</Text>
                {attendees.length > 0 ? (
                  <View style={styles.attendeesList}>
                    {attendees.map((attendee, index) => {
                      // Truncate name to 14 characters if needed
                      const displayName = attendee.name.length > 14
                        ? `${attendee.name.substring(0, 14)}...`
                        : attendee.name;

                      // Check if user has paid
                      const hasPaid = event?.paid_participants?.includes(parseInt(attendee.id));

                      return (
                        <View key={attendee.id} style={styles.attendeeItem}>
                          <Text style={styles.attendeeNumber}>{index + 1}.</Text>
                          <View style={styles.attendeeContent}>
                            {/* Profile image or default user icon */}
                            {attendee.profileImageUrl ? (
                              <Image
                                source={{ uri: signedProfileUrls[attendee.id] || attendee.profileImageUrl }}
                                style={styles.attendeeAvatar}
                              />
                            ) : (
                              <View style={styles.attendeeAvatarPlaceholder}>
                                <AvatarIcon size={24} color={Colors.hyperBlue} />
                              </View>
                            )}
                            <Text style={styles.attendeeName}>{displayName}</Text>

                            {/* Payment status - only visible to event owner and not shown for the first user (owner) */}
                            {isCreator && !attendee.isCreator && (
                              hasPaid ? (
                                <View style={styles.paidBox}>
                                  <Text style={styles.paidText}>PAID</Text>
                                </View>
                              ) : (
                                <TouchableOpacity
                                  style={styles.markAsPaidBox}
                                  onPress={() => handleMarkAsPaid(parseInt(attendee.id))}
                                >
                                  <Text style={styles.markAsPaidText}>MARK AS PAID</Text>
                                </TouchableOpacity>
                              )
                            )}

                            {/* Connect button - only visible to non-creators for users who aren't already contacts */}
                            {!isCreator && !attendee.isCurrentUser && !attendee.isCreator &&
                             userData?.id && !isUserContact(parseInt(attendee.id)) && (
                              <TouchableOpacity
                                style={[styles.connectButton, hasPendingConnectRequest(parseInt(attendee.id)) && styles.connectButtonSent]}
                                onPress={() => handleSendConnectRequest(attendee)}
                                disabled={sendingConnectRequest === parseInt(attendee.id) || hasPendingConnectRequest(parseInt(attendee.id))}
                              >
                                {sendingConnectRequest === parseInt(attendee.id) ? (
                                  <ActivityIndicator size="small" color="white" />
                                ) : hasPendingConnectRequest(parseInt(attendee.id)) ? (
                                  <ClockIcon size={13} color={Colors.primary} />
                                ) : (
                                  <Plus size={16} color="white" />
                                )}
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      );
                    })}

                    {/* Add empty placeholder rows when user is invited but not signed up */}
                    {!isCreator && !isUserParticipant && (
                      <>
                        <View style={styles.attendeeItem}>
                          <Text style={styles.attendeeNumberPlaceholder}>{attendees.length + 1}.</Text>
                          <View style={styles.attendeeContent}>
                            {/* Empty space instead of avatar */}
                            <View style={{width: 36}} />
                            <Text style={styles.attendeeName}></Text>
                          </View>
                        </View>
                        <View style={styles.attendeeItem}>
                          <Text style={styles.attendeeNumberPlaceholder}>{attendees.length + 2}.</Text>
                          <View style={styles.attendeeContent}>
                            {/* Empty space instead of avatar */}
                            <View style={{width: 36}} />
                            <Text style={styles.attendeeName}></Text>
                          </View>
                        </View>
                      </>
                    )}
                  </View>
                ) : (
                  <View style={styles.emptyAttendees}>
                    <Text style={styles.emptyAttendeesText}>No one has signed up yet. Be the first!</Text>

                    {/* Add empty placeholder rows when no attendees */}
                    {!isCreator && !isUserParticipant && (
                      <>
                        <View style={styles.attendeeItem}>
                          <Text style={styles.attendeeNumberPlaceholder}>1.</Text>
                          <View style={styles.attendeeContent}>
                            {/* Empty space instead of avatar */}
                            <View style={{width: 36}} />
                            <Text style={styles.attendeeName}></Text>
                          </View>
                        </View>
                        <View style={styles.attendeeItem}>
                          <Text style={styles.attendeeNumberPlaceholder}>2.</Text>
                          <View style={styles.attendeeContent}>
                            {/* Empty space instead of avatar */}
                            <View style={{width: 36}} />
                            <Text style={styles.attendeeName}></Text>
                          </View>
                        </View>
                      </>
                    )}
                  </View>
                )}
                {!isCreator && !isUserParticipant && !isPastOrWithinCutoff && (
                  <View
                    style={styles.signUpButtonOverlay}
                  >
                    {isSigningUp ? (
                      <TouchableOpacity style={styles.primaryButton} disabled>
                        <ActivityIndicator size="small" color="#FFFFFF" />
                      </TouchableOpacity>
                    ) : (
                      <TouchableOpacity
                        style={styles.primaryButton}
                        onPress={handleSignUp}
                      >
                        <Text style={styles.buttonText}>SIGN UP!</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </View>
            </View>

            {event?.ticketUrl && (
              <View style={styles.previewSection}>
                <View style={styles.detailsSection}>
                  <Text style={styles.detailsTitle}>TICKET LINK</Text>
                  <TouchableOpacity onPress={() => Linking.openURL(event.ticketUrl || "")}>
                    <Text style={styles.link}>
                      {event.ticketUrl}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}


            {/* Always show costs section */}
            <View style={styles.previewSection}>
              <View style={styles.sectionWithEdit}>
                <View style={styles.costsSection}>
                  <Text style={styles.costsTitle}>COSTS</Text>
                  <Text style={styles.costsAmount}>
                    {event?.cost ? (
                      <>
                        {event.cost} {event.cost !== 'Free' && event.cost_purpose ? `for ${event.cost_purpose}` : ''}
                      </>
                    ) : (
                      'Free'
                    )}
                  </Text>
                  {event?.cost && event.cost !== 'Free' && event.payment_type && (
                    <Text style={styles.paymentTypeText}>{getPaymentTypeText(event.payment_type)}</Text>
                  )}
                  {event?.cost && event.cost !== 'Free' && (
                    <View style={styles.costsButtons}>
                      {/* Show payment status */}
                      {isUserPaid ? (
                        <Text style={styles.paidStatusText}>PAID ✓</Text>
                      ) : !isCreator && isUserParticipant && (
                        isProcessingPayment ? (
                          <TouchableOpacity style={styles.payNowButton} disabled>
                            <ActivityIndicator size="small" color="#FFFFFF" />
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            style={styles.payNowButton}
                            onPress={handlePayNow}
                          >
                            <Text style={styles.buttonText}>PAY NOW</Text>
                          </TouchableOpacity>
                        )
                      )}
                      {/* <TouchableOpacity style={styles.addToDiscussionButton}>
                        <Text style={styles.secondaryButtonText}>ADD TO DISCUSSION</Text>
                      </TouchableOpacity> */}
                    </View>
                  )}
                </View>
                <EditButton
                  onPress={handleEditCosts}
                  isVisible={isCreator && !isPastEvent}
                />
              </View>
            </View>

            {/* WHO'S INVITED section - only visible to event creators */}
            {isCreator && event?.inviteeDetails && event.inviteeDetails.length > 0 && (
              <View style={styles.previewSection}>
                <View style={styles.sectionWithEdit}>
                  <View style={styles.inviteesSection}>
                    <Text style={styles.inviteesTitle}>WHO'S INVITED</Text>
                    <View style={styles.inviteesGrid}>
                      {event.inviteeDetails.map((invitee) => (
                        <View key={invitee.user_id} style={styles.inviteeItem}>
                          <Text style={styles.inviteeName}>
                            {invitee.user?.display_name || `User ${invitee.user_id}`}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                  <EditButton
                    onPress={handleEditInvitees}
                    isVisible={isCreator && !isPastEvent}
                  />
                </View>
              </View>
            )}

            {/* Only show discussion section if user is a participant or the creator */}
            {(isUserParticipant || isCreator) && (
              <View style={styles.discussionSection}>
                <Text style={styles.discussionTitle}>DISCUSSION</Text>
                {isLoadingMessages && messages.length === 0 ? (
                  <View style={styles.loadingMessages}>
                    <ActivityIndicator size="small" color={Colors.primary} />
                    <Text style={styles.loadingText}>Loading messages...</Text>
                  </View>
                ) : (
                  <ScrollView
                    ref={messagesScrollViewRef}
                    style={styles.discussionList}
                    contentContainerStyle={styles.discussionListContent}
                  >
                    {messages.length === 0 ? (
                      <View style={styles.emptyMessages}>
                        <Text style={styles.emptyMessagesText}>No messages yet. Start the conversation!</Text>
                      </View>
                    ) : (
                      messages.map((message) => (
                        <View key={message.id} style={[
                          styles.discussionItem,
                          message.isCurrentUser ? styles.userMessage : null
                        ]}>
                          {message.user?.profile_image_url ? (
                            <Image
                              source={{ uri: signedMessageProfileUrls[message.user_id.toString()] || message.user.profile_image_url }}
                              style={[styles.discussionAvatar, message.isCurrentUser ? styles.userMessageAvatar : null]}
                            />
                          ) : (
                            <View style={[styles.discussionAvatarPlaceholder, message.isCurrentUser ? styles.userMessageAvatar : null]}>
                              <AvatarIcon size={14} color="#ABD4DD" />
                            </View>
                          )}
                          <View style={[
                            styles.discussionContent,
                            message.isCurrentUser ? styles.userMessageContent : null
                          ]}>
                            <Text style={styles.discussionName}>
                              {message.user?.display_name || `User ${message.user_id}`}
                            </Text>
                            <Text style={[
                              styles.discussionText,
                              message.isCurrentUser ? styles.userMessageText : null
                            ]}>{message.text}</Text>
                          </View>
                        </View>
                      ))
                    )}
                  </ScrollView>
                )}

                <View style={styles.messageInputContainer}>
                  <TextInput
                    style={[styles.messageInput, isPastEvent && styles.messageInputDisabled]}
                    placeholderTextColor={isPastEvent ? Colors.gray : Colors.white}
                    placeholder={isPastEvent ? "Discussion is disabled for past events" : "Type a message..."}
                    value={messageText}
                    onChangeText={setMessageText}
                    multiline
                    maxLength={500}
                    editable={!isPastEvent}
                  />
                  <TouchableOpacity
                    style={[styles.sendButton, (!messageText.trim() || isPastEvent) && styles.sendButtonDisabled]}
                    onPress={handleSendMessage}
                    disabled={!messageText.trim() || isSendingMessage || isPastEvent}
                  >
                    {isSendingMessage ? (
                      <ActivityIndicator size="small" color={Colors.black} />
                    ) : (
                      <Send width={20} height={20} color={Colors.black} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
          </>
          )
          }

          <SignUpModal
            open={isSignUpModalOpen}
            onOpenChange={setIsSignUpModalOpen}
            eventTitle={event?.name || "Event"}
            dates={dateOptions}
            onDone={handleDone}
            showQuorum={false}
            multipleDatesAllowed={event?.multiple_dates || false}
          />
          {/* Debug output - using useEffect to avoid rendering issues */}

          <SignUpConfirmationModal
            open={isConfirmationModalOpen}
            onOpenChange={setIsConfirmationModalOpen}
            eventTitle={event?.name || "Event"}
            selectedDates={selectedDates}
            onClose={handleClose}
            onCancel={handleCancelSignUp}
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
    </>
  );
};

const styles = StyleSheet.create({
  // Loading and error styles
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 300,
  },
  // Layout styles
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  // orDivider style is defined below
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: 0, // Extra padding for keyboard
  },

  // Header styles
  header: {
    backgroundColor: Colors.primary,
    padding: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 6,
    margin: 0,
  },
  headerText: {
    color: Colors.black,
    fontSize: 18,
    fontWeight: '600',
    margin: 0,
  },
  headerLinks: {
    flexDirection: 'row',
    marginTop: 1.5,
  },
  headerLink: {
    color: Colors.primaryDark,
    fontSize: 13,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  headerLinkText: {
    color: Colors.primaryDark,
    fontSize: 13,
  },
  quorumHeader: {
    backgroundColor: Colors.primaryDark,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quorumHeaderMet: {
    backgroundColor: Colors.secondary,
  },
  quorumHeaderText: {
    color: 'white',
    fontSize: 13,
    fontWeight: '500',
  },

  // Typography styles
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    lineHeight: 28,
    margin: 0,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    lineHeight: 28,
    paddingTop: 16,
    paddingBottom: 12,
    margin: 0,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 16,
    margin: 0,
  },
  bodyText: {
    fontSize: 16,
    color: Colors.white,
    lineHeight: 24,
    margin: 0,
  },
  link: {
    color: Colors.secondary,
    textDecorationLine: 'underline',
  },

  // Button styles
  primaryButton: {
    width: '100%',
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    borderWidth: 0,
    borderRadius: 0,
  },
  buttonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '700',
  },
  secondaryButton: {
    width: '100%',
    height: 48,
    backgroundColor: 'transparent',
    borderColor: Colors.primary,
    borderWidth: 1,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  editButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    backgroundColor: `${Colors.background}33`,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sectionWithEdit: {
    position: 'relative',
    width: '100%',
  },
  sectionEditButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Preview content styles
  previewContent: {
    padding: 0,
    margin: 0,
  },
  previewImage: {
    position: 'relative',
    width: '100%',
    height: 240,
    margin: 0,
  },
  eventImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  previewSection: {
    position: 'relative',
    padding: 8,
    paddingBottom: 0,
    paddingHorizontal: 20,
  },

  // Date-time styles
  dateTimeList: {
    flexDirection: 'column',
    gap: 8,
  },
  dateTimeItem: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  dateTimeContent: {
    flexDirection: 'column',
    gap: 0,
  },
  dateText: {
    fontWeight: '600',
  },
  orContainer: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  orText: {
    fontSize: 12,
    color: Colors.secondary,
    fontWeight: '500',
  },

  // Location styles
  locationInfo: {
    marginVertical: 4,
    marginTop: 8,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  address: {
    margin: 0,
    marginLeft: 29,
    fontSize: 16,
    color: Colors.secondary,
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
  meetingPoint: {
    margin: 0,
    marginTop: 8,
    marginLeft: 29,
    fontSize: 14,
    color: '#999999',
  },

  // Quorum styles
  quorumInfo: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
    alignItems: 'flex-start',
  },
  quorumIcon: {
    paddingTop: 5
  },
  quorumContent: {
    flexDirection: 'row',
    gap: 32,
    alignItems: 'center',
  },
  quorumItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quorumLabel: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '600',
  },
  quorumNumber: {
    textAlign: 'center',
    textAlignVertical: 'center',
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 12,
    color: Colors.white,
    fontSize: 14,
    lineHeight: 14,
    fontWeight: '700',
    paddingTop: Platform.OS === 'android' ? 2 : 5
  },
  quorumNumberHighlighted: {
    backgroundColor: '#3257DB',
    borderColor: '#3257DB',
    color: 'white',
  },

  // Details section styles
  detailsSection: {
    marginTop: 24,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 16,
  },
  detailsText: {
    fontSize: 16,
    color: Colors.white,
    margin: 0,
    lineHeight: 24,
  },

  // Attendees styles
  whoIsGoingSection: {
    marginTop: 24,
    position: 'relative',
    paddingBottom: 20,
  },
  whoIsGoingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 16,
  },
  attendeesList: {
    flexDirection: 'column',
    position: 'relative',
  },
  attendeeItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    paddingLeft: 0,
  },
  attendeeNumber: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
  },
  attendeeNumberPlaceholder: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
    opacity: 0.3,
  },
  attendeeContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.primary}33`,
    paddingBottom: 8,
    justifyContent: 'flex-start', // Changed from space-between to flex-start for better layout when no payment buttons
  },
  attendeeAvatar: {
    width: 21,
    height: 22,
    borderRadius: 12,
    marginRight: 12,
  },
  attendeeAvatarPlaceholder: {
    width: 21,
    height: 22,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  attendeeName: {
    fontSize: 16,
    color: Colors.white,
    flex: 1,
  },
  paidBox: {
    backgroundColor: Colors.primary,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginLeft: 'auto', // Push to the right side
  },
  paidText: {
    color: Colors.black,
    fontSize: 12,
    fontWeight: '500',
  },
  markAsPaidBox: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginLeft: 'auto', // Push to the right side
  },
  markAsPaidText: {
    color: Colors.primary,
    fontSize: 12,
    fontWeight: '500',
  },
  creatorBadge: {
    fontSize: 12,
    color: Colors.primary,
    marginLeft: 8,
    fontWeight: '500',
  },
  currentUserBadge: {
    fontSize: 12,
    color: '#E35D5D',
    marginLeft: 8,
    fontWeight: '500',
  },
  emptyAttendees: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyAttendeesText: {
    fontSize: 16,
    color: '#999999',
    textAlign: 'center',
  },
  signUpButtonOverlay: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    height: 80, // Reduced height to cover only the empty placeholder rows
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: 20,
  },

  // Costs section styles
  costsSection: {
    marginTop: 24,
  },
  costsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 8,
  },
  costsAmount: {
    fontSize: 16,
    color: Colors.white,
    margin: 0,
    marginBottom: 8,
  },
  paymentTypeText: {
    fontSize: 14,
    color: '#999999',
    margin: 0,
    marginBottom: 16,
  },
  costsButtons: {
    flexDirection: 'column',
    gap: 12,
  },
  payNowButton: {
    width: '100%',
    height: 48,
    backgroundColor: Colors.primary,
    borderRadius: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paidButton: {
    backgroundColor: '#4CAF50', // Green color for paid button
  },
  paidStatusText: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: '500',
  },
  addToDiscussionButton: {
    width: '100%',
    height: 48,
    backgroundColor: 'transparent',
    borderColor: Colors.primary,
    borderWidth: 1,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Discussion styles
  discussionSection: {
    marginTop: 24,
    marginBottom: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.primaryDark,
    flex: 1
  },
  discussionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 16
  },
  discussionList: {
    flexDirection: 'column',
    maxHeight: 300
  },
  discussionListContent: {
    gap: 16,
    paddingBottom: 16
  },
  discussionItem: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'flex-end',
    marginBottom: 16
  },
  discussionAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12
  },
  discussionAvatarPlaceholder: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E5F4F7',
    alignItems: 'center',
    justifyContent: 'center'
  },
  discussionContent: {
    // Allow content to take only as much space as needed
    flex: 0,
    maxWidth: '80%',
  },
  discussionName: {
    fontSize: 16,
    color: Colors.white,
    margin: 0,
    marginBottom: 4,
  },
  discussionText: {
    fontSize: 16,
    color: Colors.white,
    margin: 0,
    padding: 12,
    backgroundColor: `${Colors.primary}1A`,
    borderRadius: 8,
  },
  userMessage: {
    flexDirection: 'row-reverse',
  },
  userMessageAvatar: {
    marginLeft: 0,
    marginRight: 0,
  },
  userMessageContent: {
    alignItems: 'flex-end',
    alignSelf: 'flex-end',
  },
  userMessageText: {
    backgroundColor: Colors.primary,
    color: Colors.black,
    alignSelf: 'flex-end',
  },
  messageInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: `${Colors.primary}33`,
    paddingVertical: 8,
    paddingHorizontal: 0,
    backgroundColor: Colors.background,
    marginTop: 16,
  },
  messageInput: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 16,
    backgroundColor: Colors.background,
    color: Colors.white,
  },
  messageInputDisabled: {
    borderColor: Colors.gray,
    backgroundColor: '#333333',
    color: Colors.gray,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  loadingMessages: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyMessages: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyMessagesText: {
    fontSize: 16,
    color: '#999999',
    textAlign: 'center',
  },
  connectButton: {
    width: 39,
    height: 23,
    minWidth: 39,
    padding: 0,
    borderRadius: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    marginLeft: 'auto',
  },
  connectButtonSent: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  clockIcon: {
    width: 16,
    height: 16,
    tintColor: Colors.primary,
  },

  // Invitees section styles
  inviteesSection: {
    marginTop: 24,
  },
  inviteesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 16,
  },
  inviteesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  inviteeItem: {
    width: '48%',
    marginBottom: 8,
  },
  inviteeName: {
    fontSize: 15,
    color: Colors.white,
    margin: 0,
  },
});

export default EventPage;