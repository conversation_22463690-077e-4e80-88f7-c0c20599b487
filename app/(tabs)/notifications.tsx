import React, { useEffect, useState, useRef } from 'react';
import { View, StyleSheet, ScrollView, Image, ActivityIndicator, RefreshControl, TouchableOpacity, Alert, AppState, AppStateStatus } from 'react-native';
import { Text } from "@/components/Themed";
import { Button } from '@/components/ui/button';
import { useRouter } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
// Import notification types from the store instead
// We don't need useAuthStore anymore
import { useNotificationStore } from '@/stores/notification';
import { useApiContactsStore } from '@/stores/apiContacts';
import { cloudfrontService } from '@/services/cloudfront';
import { connectRequestService } from '@/services/connectRequest';
import { AvatarIcon } from '@/assets/icons';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const NotificationsPage = () => {
  const router = useRouter();
  // We don't need userData directly in this component anymore
  const { fetchContacts } = useApiContactsStore();
  const {
    notifications,
    isLoading: loading,
    error,
    fetchNotifications,
    fetchNotificationsSilently,
    markAsRead,
    markAllAsRead,
    updateNotificationAfterAction
  } = useNotificationStore(state => ({
    notifications: state.notifications,
    isLoading: state.isLoading,
    error: state.error,
    fetchNotifications: state.fetchNotifications,
    fetchNotificationsSilently: state.fetchNotificationsSilently,
    markAsRead: state.markAsRead,
    markAllAsRead: state.markAllAsRead,
    updateNotificationAfterAction: state.updateNotificationAfterAction as (notificationId: number, isAccepted?: boolean) => void
  }));
  const [refreshing, setRefreshing] = useState(false);
  const [processingAction, setProcessingAction] = useState<number | null>(null);
  const [signedAvatarUrls, setSignedAvatarUrls] = useState<Record<number, string>>({});

  // Reference to track if a fetch is in progress
  const isFetchingRef = useRef(false);
  // Reference to store the interval ID
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // Reference to track app state
  const appStateRef = useRef(AppState.currentState);

  // Function to fetch notifications if not already fetching
  const fetchNotificationsIfNeeded = async (silent: boolean = false) => {
    if (isFetchingRef.current) {
      console.log('Skipping notification fetch - already in progress');
      return;
    }

    isFetchingRef.current = true;
    try {
      if (silent) {
        // Use silent fetch for background polling (no loading indicator)
        await fetchNotificationsSilently();
        console.log('Notifications fetched silently at:', new Date().toISOString());
      } else {
        // Use regular fetch for user-initiated actions (with loading indicator)
        await fetchNotifications();
        console.log('Notifications fetched with loading indicator at:', new Date().toISOString());
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      isFetchingRef.current = false;
    }
  };

  // Start polling when component mounts
  useEffect(() => {
    // Initial fetch - use regular fetch with loading indicator for first load
    fetchNotificationsIfNeeded(false);
    markAllAsRead();

    // Set up polling interval (every 5 seconds) - use silent fetch for background polling
    pollingIntervalRef.current = setInterval(() => fetchNotificationsIfNeeded(true), 5000);

    // Set up app state listener to pause/resume polling
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground, fetch immediately - use silent fetch
        fetchNotificationsIfNeeded(true);
      }
      appStateRef.current = nextAppState;
    });

    // Clean up on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      subscription.remove();
    };
  }, [fetchNotifications, fetchNotificationsSilently, markAllAsRead]);

  // Use focus effect to handle tab navigation
  useFocusEffect(
    React.useCallback(() => {
      // When tab is focused, fetch notifications silently and mark as read
      fetchNotificationsIfNeeded(true);
      markAllAsRead();

      return () => {
        // No cleanup needed here as the main polling continues
      };
    }, [markAllAsRead])
  );

  // Sign avatar URLs when notifications change, but only for new or changed notifications
  useEffect(() => {
    const signAvatarUrls = async () => {
      // Create a copy of the current signed URLs
      const newSignedUrls = { ...signedAvatarUrls };
      let hasChanges = false;

      for (const notification of notifications) {
        // Only process notifications with avatars that don't already have signed URLs
        if (notification.avatar && !newSignedUrls[notification.id]) {
          try {
            console.log(`Signing avatar URL for notification ${notification.id}`);
            const signedUrl = await cloudfrontService.getSignedUrl(notification.avatar);
            newSignedUrls[notification.id] = signedUrl;
            hasChanges = true;
          } catch (error) {
            console.error(`Error signing avatar URL for notification ${notification.id}:`, error);
            // Use original URL as fallback
            newSignedUrls[notification.id] = notification.avatar;
            hasChanges = true;
          }
        }
      }

      // Only update state if there are new signed URLs
      if (hasChanges) {
        console.log('Updating signed avatar URLs');
        setSignedAvatarUrls(newSignedUrls);
      }
    };

    if (notifications.length > 0) {
      signAvatarUrls();
    }
  }, [notifications, signedAvatarUrls]);

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchNotifications();
    setRefreshing(false);
  };

  // Function to handle notification action button press
  const handleActionPress = async (notificationId: number, action: string) => {
    try {
      setProcessingAction(notificationId);

      // Find the notification
      const notification = notifications.find(n => n.id === notificationId);
      if (!notification) return;

      console.log(`Handling action ${action} for notification ${notificationId}`, notification);

      // For connection requests, use the notification service to handle the action
      if (notification.type === 'connection' && (action === 'ACCEPT' || action === 'IGNORE')) {
        console.log('Processing connection request action for notification:', notification);
        const connectRequestId = notification.connect_request_id;
        console.log(`Processing connect request action: ${action}, connect_request_id: ${connectRequestId}`);

        if (!connectRequestId) {
          console.error('No connect_request_id found in notification:', notification);
          Alert.alert('Error', 'Could not process connection request - missing ID');
          return;
        }

        if (action === 'ACCEPT') {
          const response = await connectRequestService.acceptConnectRequest(connectRequestId);
          console.log('Accept connect request response:', response);
          if (response.error) {
            Alert.alert('Error', `Failed to accept connection: ${response.error}`);
          } else {
            // Refresh contacts after accepting a connection request
            await fetchContacts();
            Alert.alert('Connection Accepted', 'Connection request accepted successfully');

            console.log('Connect request accepted successfully, updating notification:', notificationId);
            // Update the notification in the local state to remove action buttons and mark as accepted
            updateNotificationAfterAction(notificationId, true);
          }
        } else if (action === 'IGNORE') {
          const response = await connectRequestService.declineConnectRequest(connectRequestId);
          console.log('Decline connect request response:', response);
          if (response.error) {
            Alert.alert('Error', `Failed to ignore connection: ${response.error}`);
          } else {
            Alert.alert('Connection Ignored', 'Connection request ignored');

            console.log('Connect request ignored successfully, updating notification:', notificationId);
            // Update the notification in the local state to remove action buttons
            // Pass false to indicate this connect request was not accepted
            updateNotificationAfterAction(notificationId, false);
          }
        }

        // Mark the notification as read after processing
        await markAsRead(notificationId);
      } else {
        // Mark the notification as read
        await markAsRead(notificationId);

        // Handle other action types
        if (action === 'VIEW') {
          // Navigate to the relevant screen based on notification type
          switch (notification.type) {
            case 'event':
            case 'date':
            case 'cancellation':
            case 'comment':
              // Navigate to event page using the event_id from the notification
              if (notification.event_id) {
                router.push({
                  pathname: '/(tabs)/event',
                  params: { id: notification.event_id.toString() }
                });
              } else {
                // Fallback to general event page if no event_id
                router.push('/(tabs)/event');
              }
              break;
            default:
              // Default action
              break;
          }
        } else if (action === 'PAY NOW') {
          Alert.alert('Payment', 'Redirecting to payment...');
        }
      }
    } catch (err) {
      console.error('Error handling notification action:', err);
      Alert.alert('Error', 'Failed to process action');
    } finally {
      setProcessingAction(null);
    }
  };



  const getBoldText = (text: string, boldParts: string[] = []) => {
    if (boldParts.length === 0) return <Text style={styles.text}>{text}</Text>;

    // Split the text by bold parts and render accordingly
    let parts = [text];

    boldParts.forEach(boldPart => {
      const newParts: string[] = [];

      parts.forEach(part => {
        if (typeof part !== 'string') {
          newParts.push(part);
          return;
        }

        const splitPart = part.split(boldPart);

        for (let i = 0; i < splitPart.length; i++) {
          if (i > 0) {
            newParts.push(boldPart);
          }
          if (splitPart[i]) {
            newParts.push(splitPart[i]);
          }
        }
      });

      parts = newParts;
    });

    return (
      <Text style={styles.text}>
        {parts.map((part, index) => {
          if (boldParts.includes(part)) {
            return <Text key={index} style={styles.bold}>{part}</Text>;
          }
          return <Text key={index}>{part}</Text>;
        })}
      </Text>
    );
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
        >
          <Text style={styles.title}>NOTICES</Text>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading notifications...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <Button onPress={fetchNotifications} style={styles.retryButton}>Retry</Button>
            </View>
          ) : notifications.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No notifications yet</Text>
            </View>
          ) : (
            <View style={styles.notificationList}>
              {notifications.map((notification) => (
                <TouchableOpacity
                  key={notification.id}
                  style={[styles.notification, notification.read && styles.readNotification]}
                  onPress={() => {
                    markAsRead(notification.id);
                    // Navigate to event if this is an event-related notification
                    if (['event', 'date', 'cancellation', 'comment'].includes(notification.type) && notification.event_id) {
                      router.push({
                        pathname: '/(tabs)/event',
                        params: { id: notification.event_id.toString() }
                      });
                    }
                  }}
                >
                  {notification.avatar ? (
                    <Image
                      source={{ uri: signedAvatarUrls[notification.id] || notification.avatar }}
                      style={styles.avatar}
                    />
                  ) : (
                    <View style={styles.icon}>
                      <AvatarIcon size={40} color={Colors.hyperBlue} />
                    </View>
                  )}

                  <View style={styles.notificationContent}>
                    {getBoldText(notification.text, notification.boldText)}

                    {/* Show Accepted/Ignored text for connection requests after action */}
                    {notification.type === 'connection' &&
                     notification.text.includes('wants to connect') &&
                     !notification.actions && (
                      <View style={styles.acceptedContainer}>
                        {notification.isAccepted ? (
                          <Text style={styles.acceptedText}>Accepted ✓</Text>
                        ) : (
                          <Text style={styles.ignoredText}>Ignored</Text>
                        )}
                      </View>
                    )}

                    {notification.actions && (
                      <View style={styles.actionButtons}>
                        {notification.actions.map((action, index) => (
                          <Button
                            key={index}
                            variant={action.primary ? 'default' : 'secondary'}
                            style={styles.actionButton}
                            onPress={() => handleActionPress(notification.id, action.label)}
                            disabled={processingAction === notification.id}
                          >
                            {processingAction === notification.id ? 'Processing...' : action.label}
                          </Button>
                        ))}
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </ScrollView>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  avatarContainer: {
    backgroundColor: "red",
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: Colors.white,
    padding: 20,
    paddingHorizontal: 25,
    backgroundColor: Colors.backgroundDark,
    margin: 0,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.gray}70`,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
  },
  notificationList: {
    flexDirection: 'column',
  },
  notification: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
    padding: 16,
    paddingHorizontal: 25,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.gray}90`,
    backgroundColor: Colors.background,
  },
  readNotification: {
    backgroundColor: Colors.background,
    opacity: 0.6,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  icon: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  notificationContent: {
    flex: 1,
    marginLeft: 12,
  },
  text: {
    marginBottom: 12,
    color: Colors.white,
    fontSize: 14,
    lineHeight: 20,
  },
  bold: {
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    width: '100%',
  },
  actionButton: {
    flex: 1,
    maxWidth: '48%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 10,
    color: Colors.white,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  errorText: {
    marginBottom: 15,
    color: '#d32f2f',
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  emptyText: {
    color: Colors.white,
    fontSize: 16,
    textAlign: 'center',
  },
  acceptedContainer: {
    marginTop: 4,
    marginBottom: 8,
  },
  acceptedText: {
    color: Colors.secondary,
    fontSize: 14,
    fontWeight: '500',
  },
  ignoredText: {
    color: '#888888',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NotificationsPage;