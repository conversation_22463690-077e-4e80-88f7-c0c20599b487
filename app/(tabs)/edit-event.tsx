import React, { useState, useEffect, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import { WhatStep } from '@/components/CreateEvent/WhatStep';
import { QuorumStep } from '@/components/CreateEvent/QuorumStep';
import { WhenStep } from '@/components/CreateEvent/WhenStep';
import { WhereStep } from '@/components/CreateEvent/WhereStep';
import { HowMuchStep } from '@/components/CreateEvent/HowMuchStep';
import { WhyStep } from '@/components/CreateEvent/WhyStep';
import { PreviewStep } from '@/components/CreateEvent/PreviewStep';
import { InviteStep } from '@/components/CreateEvent/InviteStep';
import { eventService } from '@/services/event';
import Colors from '@/constants/Colors';
import { convertDatesToLocal, formatDateToLocal } from '@/utils/dateHelpers';
import DynamicStatusBar from '@/components/DynamicStatusBar';

// Define the DateTimeEntry interface
interface DateTimeEntry {
  date: string;
  time: string;
}

// Define the Event interface based on the API response
interface Event {
  id: number;
  name: string;
  location?: string;
  address: string;
  location_name?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  meeting_point?: string;
  owner: string;
  owner_id?: number;
  image_url: string;
  event_link?: string;
  has_options: boolean;
  quorum_met: boolean;
  enabled: boolean;
  participants: number[];
  participants_count: number;
  invitees: number[];
  paid_participants: number[];
  cost?: number;
  cost_purpose?: string;
  payment_type?: string;
  description?: string;
  quorum?: number;
  max_participants?: number | null;
  confirmed_date?: Date;
  multiple_dates: boolean;
  response_cutoff?: number;
  created_at?: string;
  updated_at?: string;
  date?: string[];
}

type Step = 'what' | 'quorum' | 'when' | 'where' | 'howMuch' | 'why' | 'preview' | 'invite';

// Interface to track changes made during editing
interface ChangeTracker {
  [key: string]: boolean;
}

const EditEventPage = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const eventId = params.eventId ? parseInt(params.eventId as string) : null;
  const stepParam = params.step as Step | undefined;
  const inlineMode = params.inline === 'true';

  console.log('Edit-event received params:', params);
  console.log('Parsed eventId:', eventId, 'stepParam:', stepParam, 'inlineMode:', inlineMode);

  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Navigation state
  const [currentStep, setCurrentStep] = useState<Step>(stepParam || 'what');

  // Event data state - all the form fields
  const [eventName, setEventName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [imageUri, setImageUri] = useState<string>('');
  const [locationName, setLocationName] = useState<string>('');
  const [addressLine1, setAddressLine1] = useState<string>('');
  const [addressLine2, setAddressLine2] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [state, setState] = useState<string>('');
  const [zip, setZip] = useState<string>('');
  const [meetingPoint, setMeetingPoint] = useState<string>('');
  const [minPeople, setMinPeople] = useState<string>('');
  const [maxPeople, setMaxPeople] = useState<string>('');
  const [date, setDate] = useState<string>('');
  const [time, setTime] = useState<string>('');
  const [isFlexible, setIsFlexible] = useState<boolean>(false);
  const [isMultiple, setIsMultiple] = useState<boolean>(false);
  const [dates, setDates] = useState<DateTimeEntry[]>([]);
  const [amount, setAmount] = useState<string>('');
  const [costDescription, setCostDescription] = useState<string>('');
  const [paymentType, setPaymentType] = useState<string>('');
  const [cutoffTime, setCutoffTime] = useState<string>('');
  const [eventLink, setEventLink] = useState<string>('');
  const [quorumMet, setQuorumMet] = useState<boolean>(false);

  // Original data for comparison
  const [originalData, setOriginalData] = useState<Event | null>(null);
  
  // Change tracking
  const [changedFields, setChangedFields] = useState<ChangeTracker>({});

  const whenStepRef = useRef<{ validate: () => boolean }>(null);

  // Calculate progress percentage for the progress bar
  const steps = ['what', 'quorum', 'when', 'where', 'howMuch', 'why', 'preview', 'invite'];
  const currentIndex = steps.indexOf(currentStep);
  const progressPercentage = (currentIndex + 1) / steps.length * 100;

  // Update current step when stepParam changes
  useEffect(() => {
    if (stepParam && stepParam !== currentStep) {
      // Prevent navigation to WhenStep if quorum is met
      if (stepParam === 'when' && quorumMet) {
        console.log('Preventing navigation to WhenStep because quorum is met');
        return;
      }
      setCurrentStep(stepParam);
    }
  }, [stepParam, quorumMet]);

  // Fetch event data when component mounts
  useEffect(() => {
    const fetchEventData = async () => {
      if (!eventId) {
        setError('No event ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await eventService.getEventById(eventId);

        if (response.error) {
          Alert.alert('Error', response.error || 'Failed to load event data');
          setIsLoading(false);
          return;
        }

        const eventData = response.data;
        if (!eventData) {
          Alert.alert('Error', 'Event not found');
          setIsLoading(false);
          return;
        }

        // Store original data for comparison
        setOriginalData(eventData as Event);

        // Populate form fields with existing data
        setEventName(eventData.name || '');
        setDescription(eventData.description || '');
        setImageUri(eventData.image_url || '');
        setLocationName(eventData.location_name || '');
        setAddressLine1(eventData.address_line1 || '');
        setAddressLine2(eventData.address_line2 || '');
        setCity(eventData.city || '');
        setState(eventData.state || '');
        setZip(eventData.zip || '');
        setMeetingPoint(eventData.meeting_point || '');
        setMinPeople(eventData.quorum?.toString() || '');
        setMaxPeople(eventData.max_participants?.toString() || '');
        setAmount(eventData.cost?.toString() || '');
        setCostDescription(eventData.cost_purpose || '');
        setPaymentType(eventData.payment_type || '');
        setCutoffTime(eventData.response_cutoff?.toString() || '');
        setEventLink(eventData.event_link || '');
        setQuorumMet(eventData.quorum_met || false);

        // Handle dates - check if it's flexible/multiple dates
        if (eventData.date && eventData.date.length > 0) {
          if (eventData.date.length === 1) {
            // Single date
            const dateEntries = convertDatesToLocal([eventData.date[0]]);
            const singleEntry = dateEntries[0];

            setDate(singleEntry.date);
            setTime(singleEntry.time);
            setIsFlexible(false);
            setIsMultiple(false);

            // Set dates array with single entry for WhenStep component
            setDates([singleEntry]);
          } else {
            // Multiple dates
            const dateEntries = convertDatesToLocal(eventData.date);
            setDates(dateEntries);
            setIsFlexible(true);
            setIsMultiple(true);
            // Set first date as primary
            if (dateEntries.length > 0) {
              setDate(dateEntries[0].date);
              setTime(dateEntries[0].time);
            }
          }
        } else {
          // No existing dates, initialize with empty date for new events
          setDates([{
            date: '',
            time: ''
          }]);
        }

      } catch (error) {
        console.error('Error fetching event data:', error);
        Alert.alert('Error', 'Failed to load event data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventData();
  }, [eventId]);

  // Function to track field changes
  const trackFieldChange = (fieldName: string, newValue: any, originalValue: any) => {
    const hasChanged = newValue !== originalValue;
    setChangedFields(prev => ({
      ...prev,
      [fieldName]: hasChanged
    }));
  };

  // Enhanced setters that track changes
  const setEventNameWithTracking = (value: string) => {
    setEventName(value);
    trackFieldChange('name', value, originalData?.name || '');
  };

  const setDescriptionWithTracking = (value: string) => {
    setDescription(value);
    trackFieldChange('description', value, originalData?.description || '');
  };

  const setImageUriWithTracking = (value: string) => {
    setImageUri(value);
    trackFieldChange('image_url', value, originalData?.image_url || '');
  };

  const setLocationNameWithTracking = (value: string) => {
    setLocationName(value);
    trackFieldChange('location_name', value, originalData?.location_name || '');
  };

  const setMinPeopleWithTracking = (value: string) => {
    setMinPeople(value);
    trackFieldChange('quorum', parseInt(value) || 0, originalData?.quorum || 0);
  };

  const setMaxPeopleWithTracking = (value: string) => {
    setMaxPeople(value);
    trackFieldChange('max_participants', parseInt(value) || null, originalData?.max_participants || null);
  };

  const setAmountWithTracking = (value: string) => {
    setAmount(value);
    trackFieldChange('cost', parseFloat(value) || 0, originalData?.cost || 0);
  };

  const setAddressLine1WithTracking = (value: string) => {
    setAddressLine1(value);
    trackFieldChange('address_line1', value, originalData?.address_line1 || '');
  };

  const setAddressLine2WithTracking = (value: string) => {
    setAddressLine2(value);
    trackFieldChange('address_line2', value, originalData?.address_line2 || '');
  };

  const setCityWithTracking = (value: string) => {
    setCity(value);
    trackFieldChange('city', value, originalData?.city || '');
  };

  const setStateWithTracking = (value: string) => {
    setState(value);
    trackFieldChange('state', value, originalData?.state || '');
  };

  const setZipWithTracking = (value: string) => {
    setZip(value);
    trackFieldChange('zip', value, originalData?.zip || '');
  };

  const setMeetingPointWithTracking = (value: string) => {
    setMeetingPoint(value);
    trackFieldChange('meeting_point', value, originalData?.meeting_point || '');
  };

  const setCostDescriptionWithTracking = (value: string) => {
    setCostDescription(value);
    trackFieldChange('cost_purpose', value, originalData?.cost_purpose || '');
  };

  const setPaymentTypeWithTracking = (value: string) => {
    setPaymentType(value);
    trackFieldChange('payment_type', value, originalData?.payment_type || '');
  };

  const setEventLinkWithTracking = (value: string) => {
    setEventLink(value);
    trackFieldChange('event_link', value, originalData?.event_link || '');
  };

  const setCutoffTimeWithTracking = (value: string) => {
    setCutoffTime(value);
    trackFieldChange('response_cutoff', parseInt(value) || undefined, originalData?.response_cutoff || undefined);
  };

  // Date tracking functions
  const setDateWithTracking = (value: string) => {
    setDate(value);
    if (!quorumMet) {
      const originalDateStr = originalData?.date?.[0] ? formatDateToLocal(new Date(originalData.date[0])) : '';
      trackFieldChange('date', value, originalDateStr);
    }
  };

  const setTimeWithTracking = (value: string) => {
    setTime(value);
    if (!quorumMet) {
      trackFieldChange('time', value, originalData?.date?.[0] ? new Date(originalData.date[0]).toTimeString().slice(0, 5) : '');
    }
  };

  const setDatesWithTracking = (value: DateTimeEntry[]) => {
    setDates(value);
    if (!quorumMet) {
      const originalDatesFormatted = originalData?.date ? convertDatesToLocal(originalData.date) : [];
      trackFieldChange('dates', JSON.stringify(value), JSON.stringify(originalDatesFormatted));
    }
  };

  // Navigation handlers
  const handleNext = () => {
    // In inline mode, save changes and go back to event screen
    if (inlineMode) {
      handleSaveChanges();
      return;
    }

    // Normal flow for full editing mode
    if (currentStep === 'what') {
      setCurrentStep('quorum');
    } else if (currentStep === 'quorum') {
      // Skip WhenStep if quorum is met
      if (quorumMet) {
        setCurrentStep('where');
      } else {
        setCurrentStep('when');
      }
    } else if (currentStep === 'when') {
      // Validate date selection before proceeding
      if (whenStepRef.current?.validate()) {
        setCurrentStep('where');
      }
    } else if (currentStep === 'where') {
      setCurrentStep('howMuch');
    } else if (currentStep === 'howMuch') {
      setCurrentStep('why');
    } else if (currentStep === 'why') {
      setCurrentStep('preview');
    } else if (currentStep === 'preview') {
      setCurrentStep('invite');
    } else if (currentStep === 'invite') {
      handleSaveChanges();
    }
  };



  // Edit handlers for preview step
  const handleEditBasics = () => setCurrentStep('what');
  const handleEditDateTime = () => {
    // Prevent editing date/time if quorum is met
    if (quorumMet) {
      console.log('Cannot edit date/time because quorum is met');
      return;
    }
    setCurrentStep('when');
  };
  const handleEditLocation = () => setCurrentStep('where');
  const handleEditQuorum = () => setCurrentStep('quorum');
  const handleEditDetails = () => setCurrentStep('why');
  const handleEditCosts = () => setCurrentStep('howMuch');
  const handleEditImage = () => setCurrentStep('what');

  // Save changes function
  const handleSaveChanges = async () => {
    if (!eventId || !originalData) {
      Alert.alert('Error', 'Unable to save changes');
      return;
    }

    try {
      // Build update payload with only changed fields
      const updatePayload: any = {};

      if (changedFields.name) updatePayload.name = eventName;
      if (changedFields.description) updatePayload.description = description;
      if (changedFields.image_url) updatePayload.image_url = imageUri;
      if (changedFields.location_name) updatePayload.location_name = locationName;
      if (changedFields.quorum) updatePayload.quorum = parseInt(minPeople);
      if (changedFields.max_participants) updatePayload.max_participants = parseInt(maxPeople) || null;
      if (changedFields.cost) updatePayload.cost = parseFloat(amount) || 0;
      if (changedFields.address_line1) updatePayload.address_line1 = addressLine1;
      if (changedFields.address_line2) updatePayload.address_line2 = addressLine2;
      if (changedFields.city) updatePayload.city = city;
      if (changedFields.state) updatePayload.state = state;
      if (changedFields.zip) updatePayload.zip = zip;
      if (changedFields.meeting_point) updatePayload.meeting_point = meetingPoint;
      if (changedFields.cost_purpose) updatePayload.cost_purpose = costDescription;
      if (changedFields.payment_type) updatePayload.payment_type = paymentType;
      if (changedFields.event_link) updatePayload.event_link = eventLink;
      if (changedFields.response_cutoff) updatePayload.response_cutoff = parseInt(cutoffTime) || undefined;

      // Handle dates if not quorum met and dates have changed
      if (!quorumMet) {
        // Check if dates have changed for multiple date events
        if (isMultiple && changedFields.dates) {
          try {
            // Validate and convert dates
            const validDates = dates.filter(d => d.date && d.time).map(d => {
              // Ensure proper date format (YYYY-MM-DD) and time format (HH:MM)
              const dateStr = d.date.includes('-') ? d.date : d.date;
              const timeStr = d.time.includes(':') ? d.time : d.time;

              console.log('Processing date:', dateStr, 'time:', timeStr);

              // Create date object and validate
              const dateObj = new Date(`${dateStr}T${timeStr}:00`);
              if (isNaN(dateObj.getTime())) {
                throw new Error(`Invalid date: ${dateStr}T${timeStr}`);
              }

              return dateObj.toISOString();
            });

            if (validDates.length > 0) {
              updatePayload.date = validDates;
              updatePayload.multiple_dates = true;
            }
          } catch (error) {
            console.error('Error processing multiple dates:', error);
            throw new Error(`Invalid date format: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
        // Check if single date has changed
        else if (!isMultiple && (changedFields.date || changedFields.time) && date && time) {
          try {
            console.log('Processing single date:', date, 'time:', time);
            const dateObj = new Date(`${date}T${time}:00`);
            if (isNaN(dateObj.getTime())) {
              throw new Error(`Invalid single date: ${date}T${time}`);
            }
            updatePayload.date = [dateObj.toISOString()];
            updatePayload.multiple_dates = false;
          } catch (error) {
            console.error('Error processing single date:', error);
            throw new Error(`Invalid date format: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      // Only make API call if there are changes
      if (Object.keys(updatePayload).length === 0) {
        Alert.alert('No Changes', 'No changes were made to save.');
        router.back();
        return;
      }

      console.log('Saving changes:', updatePayload);
      const response = await eventService.updateEvent(eventId, updatePayload);

      if (response.error) {
        Alert.alert('Error', response.error || 'Failed to save changes');
        return;
      }

      // In inline mode, navigate back immediately without showing alert
      if (inlineMode) {
        router.replace({
          pathname: '/(tabs)/event',
          params: {
            id: eventId.toString(),
            refresh: Date.now().toString() // Use timestamp to force refresh
          }
        });
        return;
      }

      // Show success alert for full editing mode
      Alert.alert('Success', 'Event updated successfully!', [
        {
          text: 'OK',
          onPress: () => {
            // Navigate back to the event screen with refresh parameter
            router.replace({
              pathname: '/(tabs)/event',
              params: {
                id: eventId.toString(),
                refresh: Date.now().toString() // Use timestamp to force refresh
              }
            });
          }
        }
      ]);

    } catch (error) {
      console.error('Error saving changes:', error);
      Alert.alert('Error', 'Failed to save changes. Please try again.');
    }
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.header}>
          {!inlineMode && (
            <View style={styles.progressBar}>
              <View
                style={[styles.progressFill, { width: `${progressPercentage}%` }]}
              />
            </View>
          )}
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              // Navigate back to the event screen with refresh parameter
              router.replace({
                pathname: '/(tabs)/event',
                params: {
                  id: eventId?.toString() || '',
                  refresh: Date.now().toString() // Use timestamp to force refresh
                }
              });
            }}
          >
            <ChevronLeft size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading event data...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <Button onPress={() => router.back()}>Go Back</Button>
          </View>
        ) : (
          <ScrollView
            style={styles.content}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
          {currentStep === 'what' && (
            <WhatStep
              eventName={eventName}
              onEventNameChange={setEventNameWithTracking}
              imageUri={imageUri}
              onImageUriChange={setImageUriWithTracking}
              onDescriptionChange={setDescriptionWithTracking}
              onLocationChange={setLocationNameWithTracking}
              onCostChange={(value) => setAmountWithTracking(typeof value === 'string' ? value : value.toString())}
              onDateChange={setDate}
              eventLink={eventLink}
              onEventLinkChange={setEventLinkWithTracking}
            />
          )}
          {currentStep === 'quorum' && (
            <QuorumStep
              minPeople={minPeople}
              maxPeople={maxPeople}
              onMinPeopleChange={setMinPeopleWithTracking}
              onMaxPeopleChange={setMaxPeopleWithTracking}
            />
          )}
          {currentStep === 'when' && (
            <WhenStep
              ref={whenStepRef}
              date={date}
              time={time}
              isFlexible={isFlexible}
              isMultiple={isMultiple}
              onDateChange={setDateWithTracking}
              onTimeChange={setTimeWithTracking}
              onFlexibleChange={setIsFlexible}
              onMultipleChange={setIsMultiple}
              dates={dates}
              onDatesChange={setDatesWithTracking}
              quorumMet={quorumMet}
            />
          )}
          {currentStep === 'where' && (
            <WhereStep
              locationName={locationName}
              addressLine1={addressLine1}
              addressLine2={addressLine2}
              city={city}
              state={state}
              zip={zip}
              meetingPoint={meetingPoint}
              onLocationNameChange={setLocationNameWithTracking}
              onAddressLine1Change={setAddressLine1WithTracking}
              onAddressLine2Change={setAddressLine2WithTracking}
              onCityChange={setCityWithTracking}
              onStateChange={setStateWithTracking}
              onZipChange={setZipWithTracking}
              onMeetingPointChange={setMeetingPointWithTracking}
            />
          )}
          {currentStep === 'howMuch' && (
            <HowMuchStep
              amount={amount}
              description={costDescription}
              paymentType={paymentType}
              onAmountChange={setAmountWithTracking}
              onDescriptionChange={setCostDescriptionWithTracking}
              onPaymentTypeChange={setPaymentTypeWithTracking}
            />
          )}
          {currentStep === 'why' && (
            <WhyStep
              description={description}
              cutoffTime={cutoffTime}
              onDescriptionChange={setDescriptionWithTracking}
              onCutoffTimeChange={setCutoffTimeWithTracking}
            />
          )}
          {currentStep === 'preview' && (
            <PreviewStep
              eventName={eventName}
              minPeople={minPeople}
              maxPeople={maxPeople}
              date={date}
              time={time}
              locationName={locationName}
              addressLine1={addressLine1}
              addressLine2={addressLine2}
              city={city}
              state={state}
              zip={zip}
              meetingPoint={meetingPoint}
              amount={amount}
              costDescription={costDescription}
              paymentType={paymentType}
              description={description}
              imageUri={imageUri}
              onNext={handleNext}
              onEditImage={handleEditImage}
              onEditBasics={handleEditBasics}
              onEditDateTime={handleEditDateTime}
              onEditLocation={handleEditLocation}
              onEditQuorum={handleEditQuorum}
              onEditDetails={handleEditDetails}
              onEditCosts={handleEditCosts}
              dates={dates}
              eventLink={eventLink}
              quorumMet={quorumMet}
              isEditing={true}
            />
          )}
          {currentStep === 'invite' && (
            <InviteStep
              eventTitle={eventName}
              eventData={{
                name: eventName,
                date: date,
                time: time,
                locationName: locationName,
                addressLine1: addressLine1,
                addressLine2: addressLine2,
                city: city,
                state: state,
                zip: zip,
                meetingPoint: meetingPoint,
                minPeople: minPeople,
                maxPeople: maxPeople,
                description: description,
                imageUri: imageUri,
                dates: dates,
                amount: amount,
                costDescription: costDescription,
                payment_type: paymentType,
                event_link: eventLink,
                isMultiple: isMultiple,
                cutoffTime: cutoffTime,
                invitees: originalData?.invitees || [],
              }}
              onNext={handleNext}
              onEventCreated={() => {
                // For editing, we don't create a new event, just continue to save
                console.log('Event editing completed');
              }}
              onResetForm={() => {
                // For editing, we don't reset the form, just go back
                router.back();
              }}
              isEditing={true}
              eventId={eventId}
              changedFields={changedFields}
              originalData={originalData}
            />
          )}

          {currentStep !== 'preview' && currentStep !== 'invite' && (
            <View style={styles.nextButtonContainer}>
              <Button
                variant="default"
                style={styles.nextButton}
                textStyle={styles.nextButtonText}
                onPress={handleNext}
              >
                {inlineMode ? 'UPDATE' : (currentStep === 'why' ? 'PREVIEW' : 'NEXT')}
              </Button>
            </View>
          )}
        </ScrollView>
        )}

        {/* Invite Button for Preview Step */}
        {!isLoading && !error && currentStep === 'preview' && (
          <View style={styles.nextButtonContainer}>
            <Button
              variant="default"
              onPress={handleNext}
              style={styles.nextButton}
              textStyle={styles.nextButtonText}
            >
              INVITE
            </Button>
          </View>
        )}
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.primary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#ff4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.background,
    zIndex: 10,
    flexDirection: 'column',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: `${Colors.primary}33`,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 20,
    width: '100%',
  },
  content: {
    paddingTop: 64,
    paddingBottom: 47,
    paddingLeft: 0,
    paddingRight: 0,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 100,
  },
  nextButtonContainer: {
    padding: 16,
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    marginTop: 40,
    marginBottom: 63,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButton: {
    backgroundColor: 'transparent',
    color: Colors.primary,
    borderColor: Colors.primary,
    borderWidth: 1,
    padding: 16,
    borderRadius: 0,
    fontWeight: '500',
    width: 200,
    height: 48,
    fontSize: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 16,
  },
});

export default EditEventPage;
