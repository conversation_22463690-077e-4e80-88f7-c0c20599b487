import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView, ActivityIndicator, Image } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ChevronLeft, Check } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import BlockContactModal from '@/components/BlockContactModal/BlockContactModal';
import Svg, { Path } from 'react-native-svg';
import { contactService, ContactResponse } from '@/services/contact';
import { userService, UserResponse } from '@/services/user';
import { groupService } from '@/services/group';
import { eventService } from '@/services/event';
import { cloudfrontService } from '@/services/cloudfront';
import { useAuthStore } from '@/stores/auth';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

export default function ContactPage() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { userData } = useAuthStore();
  const [contact, setContact] = useState<ContactResponse | null>(null);
  const [contactUser, setContactUser] = useState<UserResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [groups, setGroups] = useState<{id: number, name: string, isMember: boolean, members: number[], owner_id?: number}[]>([]);
  const [sharedHistory, setSharedHistory] = useState<{date: string, event: string}[]>([]);
  const [profileImageUri, setProfileImageUri] = useState<string | null>(null);
  const [eventsCreated, setEventsCreated] = useState(0);
  const [eventsAttended, setEventsAttended] = useState(0);
  const [hideEvents, setHideEvents] = useState(false);
  const [prioritizeEvents, setPrioritizeEvents] = useState(false);
  const [isBlockModalOpen, setIsBlockModalOpen] = useState(false);
  const [updatingGroups, setUpdatingGroups] = useState(false);
  const [updatingPriority, setUpdatingPriority] = useState(false);
  const [updatingHideEvents, setUpdatingHideEvents] = useState(false);

  useEffect(() => {
    const fetchContactData = async () => {
      if (!id) {
        setError('No contact ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch contact information
        const contactResponse = await contactService.getContactById(Number(id));

        if (contactResponse.error) {
          setError(contactResponse.error);
          setLoading(false);
          return;
        }

        const contactData = contactResponse.data || null;
        setContact(contactData);
        setPrioritizeEvents(contactData?.is_priority || false);
        setHideEvents(contactData?.hide_events || false);

        if (contactData?.user_id) {
          // Fetch user information for the contact
          const userResponse = await userService.getUserById(contactData.user_id);

          if (userResponse.data) {
            setContactUser(userResponse.data);

            // Get profile image if available
            if (userResponse.data.profile_image_url) {
              try {
                const signedUrl = await cloudfrontService.getSignedUrl(userResponse.data.profile_image_url);
                setProfileImageUri(signedUrl);
              } catch (imgError) {
                console.error('Error getting signed profile image URL:', imgError);
                setProfileImageUri(userResponse.data.profile_image_url);
              }
            }

            // Fetch events data for history, created count, and attended count
            if (userData?.id) {
              const eventsResponse = await eventService.getAllEvents();

              if (eventsResponse.data) {
                const allEvents = eventsResponse.data;
                const now = new Date();

                // Count events created by this contact
                const created = allEvents.filter(event =>
                  event.owner_id === contactData.user_id
                ).length;
                setEventsCreated(created);

                // Count events this contact has participated in
                const attended = allEvents.filter(event => {
                  const participants = event.participants || [];
                  return participants.includes(contactData.user_id);
                }).length;
                setEventsAttended(attended);

                // Filter for past events where both the current user and contact participated
                const pastEvents = allEvents.filter(event => {
                  // Check if the event has dates and the last date is in the past
                  if (!event.date || event.date.length === 0) return false;

                  const lastDate = new Date(event.date[event.date.length - 1]);
                  if (lastDate > now) return false;

                  // Check if both users participated
                  const participants = event.participants || [];
                  return participants.includes(userData.id) &&
                         participants.includes(contactData.user_id);
                });

                // Format the shared history
                const history = pastEvents.map(event => ({
                  date: new Date(event.date[0]).toLocaleDateString(),
                  event: event.name
                }));

                setSharedHistory(history);
              }
            }
          }
        }

        // Fetch groups owned by the current user
        const groupsResponse = await groupService.getAllGroups();
        if (groupsResponse.data && contactData && userData) {
          console.log('All groups:', groupsResponse.data);
          console.log('Current user ID:', userData.id);

          // Filter groups to only include those owned by the current user
          const userOwnedGroups = groupsResponse.data.filter(group => {
            const isOwner = group.owner_id === userData.id;
            console.log(`Group ${group.id} (${group.name}) owner_id:`, group.owner_id, 'Is current user owner:', isOwner);
            return isOwner;
          });

          console.log('User owned groups:', userOwnedGroups);

          // Map groups to include whether this contact is a member
          const groupsWithMembership = userOwnedGroups.map(group => {
            // Debug logging to understand the data
            console.log(`Group ${group.id} (${group.name}) members:`, group.members);
            console.log(`Contact ID:`, contactData.id);
            console.log(`Contact user_id:`, contactData.user_id);

            // Check if the contact's ID is in the group's members array
            // Important: groups store contact IDs in their members array, not user IDs
            let isMember = false;

            if (Array.isArray(group.members) && contactData.id !== undefined) {
              // Convert all member IDs to strings for comparison
              const memberIds = group.members.map(id => String(id));
              const contactId = String(contactData.id);

              console.log(`Checking if contact ID ${contactId} is in`, memberIds);
              isMember = memberIds.includes(contactId);
              console.log(`Is member: ${isMember}`);
            }

            return {
              id: group.id,
              name: group.name,
              isMember: isMember,
              // Store the original members array for debugging
              members: group.members || [],
              owner_id: group.owner_id
            };
          });
          setGroups(groupsWithMembership);
        }

      } catch (err) {
        console.error('Error fetching contact data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch contact data');
      } finally {
        setLoading(false);
      }
    };

    fetchContactData();
  }, [id, userData?.id]);

  const handleToggleBlock = async () => {
    if (!contact?.id) return;

    try {
      // Update the contact's is_blocked status in the database
      const updateResponse = await contactService.updateContact(contact.id, {
        is_blocked: !contact.is_blocked
      });

      if (updateResponse.error) {
        console.error('Error updating contact block status:', updateResponse.error);
        return;
      }

      // Update the local state
      setContact(prev => prev ? {...prev, is_blocked: !prev.is_blocked} : null);
      setIsBlockModalOpen(false);
    } catch (error) {
      console.error('Error toggling block status:', error);
    }
  };

  const handleToggleGroup = async (groupId: number, isMember: boolean) => {
    if (!contact?.id) {
      console.error('Cannot toggle group membership: contact has no ID');
      return;
    }

    try {
      setUpdatingGroups(true);

      // Get the current group from our local state
      const currentGroup = groups.find(g => g.id === groupId);

      if (!currentGroup) {
        console.error(`Group with ID ${groupId} not found in local state`);
        return;
      }

      console.log(`Updating group ${groupId} (${currentGroup.name})`);
      console.log(`Current members:`, currentGroup.members);
      console.log(`Contact ID:`, contact.id);
      console.log(`Is currently a member:`, isMember);

      // Make a copy of the members array
      const members = [...currentGroup.members];

      // Add or remove the contact's ID from the members array
      // Important: groups store contact IDs in their members array, not user IDs
      let updatedMembers;
      if (isMember) {
        // Remove the contact from the group
        console.log(`Removing contact ID ${contact.id} from members`);
        updatedMembers = members.filter(memberId => Number(memberId) !== Number(contact.id));
      } else {
        // Add the contact to the group
        console.log(`Adding contact ID ${contact.id} to members`);
        updatedMembers = [...members, Number(contact.id)];
      }

      console.log(`Updated members:`, updatedMembers);

      // Update the group with the new members array
      // Note: The backend expects 'contacts' parameter, not 'members'
      const updateResponse = await groupService.updateGroup(groupId, { contacts: updatedMembers });

      if (updateResponse.error) {
        console.error('Error updating group members:', updateResponse.error);

        // If we get a 404, it might be because the current user doesn't own this group
        if (updateResponse.status === 404) {
          console.error('This group might not belong to the current user');
          console.log('Group owner_id:', currentGroup.owner_id);
          console.log('Current user ID:', userData?.id);
        }

        return;
      }

      // Update the local state to reflect the change
      setGroups(prevGroups =>
        prevGroups.map(g =>
          g.id === groupId ? { ...g, isMember: !isMember, members: updatedMembers } : g
        )
      );
    } catch (error) {
      console.error('Error toggling group membership:', error);
    } finally {
      setUpdatingGroups(false);
    }
  };

  const handleTogglePriority = async (isPriority: boolean) => {
    if (!contact?.id) return;

    try {
      setUpdatingPriority(true);

      // Update the contact's priority status
      const updateResponse = await contactService.updateContact(contact.id, {
        is_priority: !isPriority
      });

      if (updateResponse.error) {
        console.error('Error updating contact priority:', updateResponse.error);
        return;
      }

      // Update the local state
      setPrioritizeEvents(!isPriority);
    } catch (error) {
      console.error('Error toggling priority:', error);
    } finally {
      setUpdatingPriority(false);
    }
  };

  const handleToggleHideEvents = async (isHidden: boolean) => {
    if (!contact?.id) return;

    try {
      setUpdatingHideEvents(true);

      // Update the contact's hide_events status in the database
      const updateResponse = await contactService.updateContact(contact.id, {
        hide_events: !isHidden
      });

      if (updateResponse.error) {
        console.error('Error updating contact hide_events:', updateResponse.error);
        return;
      }

      // Update the local state
      setHideEvents(!isHidden);
    } catch (error) {
      console.error('Error toggling hide events:', error);
    } finally {
      setUpdatingHideEvents(false);
    }
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeft size={24} color={Colors.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Contact</Text>
        </View>
        <ScrollView style={styles.content}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading contact...</Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <Button
                style={styles.retryButton}
                onPress={() => router.back()}
              >
                <Text style={styles.retryButtonText}>Go Back</Text>
              </Button>
            </View>
          ) : contact ? (
            <View style={styles.profileHeader}>
              {profileImageUri ? (
                <Image
                  source={{ uri: profileImageUri }}
                  style={styles.avatarImage}
                />
              ) : (
                <Svg style={styles.avatar} width={40} height={40} viewBox="0 0 17 18" fill="none">
                  <Path d="M8.5 10.818C8.5 10.818 2.318 10.818 0 16.227C2.72509 17.2209 5.59947 17.7437 8.5 17.773C11.4005 17.7437 14.2749 17.2209 17 16.227C14.682 10.818 8.5 10.818 8.5 10.818ZM8.5 10.045C10.818 10.045 12.364 7.727 12.364 3.863C12.364 -0.000999928 8.5 2.67573e-10 8.5 2.67573e-10C8.5 2.67573e-10 4.636 0 4.636 3.864C4.636 7.728 6.182 10.045 8.5 10.045Z" fill={Colors.secondary}/>
                </Svg>
              )}
              <View style={styles.profileInfo}>
                <Text style={styles.name}>{contact.first_name} {contact.last_name || ''}</Text>
                <View style={styles.stats}>
                  <Text style={styles.statsNumber}>{eventsCreated}</Text>
                  <Text style={styles.statsLabel}>CREATED</Text>
                  <Text style={styles.statsDivider}>•</Text>
                  <Text style={styles.statsNumber}>{eventsAttended}</Text>
                  <Text style={styles.statsLabel}>ATTENDED</Text>
                </View>
              </View>
            </View>
          ) : null}

          <View style={styles.priorityCheckbox}>
            {updatingPriority ? (
              <ActivityIndicator size="small" color={Colors.primary} style={{marginRight: 12}} />
            ) : (
              <TouchableOpacity
                style={styles.checkboxTouchable}
                onPress={() => handleTogglePriority(prioritizeEvents)}
                disabled={!contact?.id}
              >
                <View style={[styles.groupCheckbox, prioritizeEvents && styles.checked]}>
                  {prioritizeEvents && <Check width={15} height={15} color="white" />}
                </View>
                <Text style={styles.checkboxLabel}>
                  Prioritize events {contact?.first_name || ''} {contact?.last_name || ''} creates
                </Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.groupsSection}>
            <Text style={styles.groupsTitle}>GROUPS</Text>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={Colors.primary} />
                <Text style={styles.loadingText}>Loading groups...</Text>
              </View>
            ) : updatingGroups ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={Colors.primary} />
                <Text style={styles.loadingText}>Updating groups...</Text>
              </View>
            ) : (
              <View style={styles.groupsGrid}>
                {groups.map((group) => (
                  <View key={group.id} style={styles.groupItem}>
                    <TouchableOpacity
                      style={styles.checkboxTouchable}
                      onPress={() => handleToggleGroup(group.id, group.isMember)}
                      disabled={!contact?.user_id}
                    >
                      <View style={[styles.groupCheckbox, group.isMember && styles.checked]}>
                        {group.isMember && <Check width={15} height={15} color="white" />}
                      </View>
                      <Text style={styles.groupName}>{group.name}</Text>
                    </TouchableOpacity>
                  </View>
                ))}
                {groups.length === 0 && (
                  <Text style={styles.emptyHistoryText}>No groups available</Text>
                )}
              </View>
            )}
          </View>

          <View style={styles.historySection}>
            <Text style={styles.historyTitle}>YOUR HISTORY WITH {(contact?.first_name || '').toUpperCase()} {(contact?.last_name || '').toUpperCase()}</Text>
            <View style={styles.historyList}>
              {sharedHistory.length > 0 ? (
                sharedHistory.map((item, index) => (
                  <View key={index} style={styles.historyItem}>
                    <Text style={styles.historyDate}>{item.date}</Text>
                    <Text style={styles.historyEvent}> • {item.event}</Text>
                  </View>
                ))
              ) : (
                <Text style={styles.emptyHistoryText}>No shared event history</Text>
              )}
            </View>
          </View>

          <View style={styles.hideEventsSection}>
            <View style={styles.hideEventsCheckbox}>
              {updatingHideEvents ? (
                <ActivityIndicator size="small" color={Colors.primary} style={{marginRight: 12}} />
              ) : (
                <TouchableOpacity
                  style={styles.checkboxTouchable}
                  onPress={() => handleToggleHideEvents(hideEvents)}
                  disabled={!contact?.id}
                >
                  <View style={[styles.groupCheckbox, hideEvents && styles.checked]}>
                    {hideEvents && <Check width={15} height={15} color="white" />}
                  </View>
                  <Text style={styles.checkboxLabel}>
                    Hide all events {contact?.first_name || ''} {contact?.last_name || ''} creates
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            <View style={styles.blockContainer}>
              <Button
                style={styles.blockButton}
                onPress={() => setIsBlockModalOpen(true)}
              >
                <Text style={styles.blockButtonText}>
                  {contact?.is_blocked ? 'Unblock this contact' : 'Block this contact'}
                </Text>
              </Button>
              <Text style={styles.blockText}>
                When you block a contact they can no longer invite you to events.
              </Text>
            </View>
          </View>

          <BlockContactModal
            open={isBlockModalOpen}
            onOpenChange={setIsBlockModalOpen}
            contactName={`${contact?.first_name || ''} ${contact?.last_name || ''}`}
            onBlock={handleToggleBlock}
            isBlocked={contact?.is_blocked}
          />
        </ScrollView>
      </View>
    </View>
    </>
  );
}

const styles = StyleSheet.create({
  checkboxTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 8, // Increase the touchable area vertically
    paddingRight: 8, // Increase the touchable area horizontally
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  retryButtonText: {
    color: Colors.black,
    fontSize: 14,
    fontWeight: '500',
  },
  container: {
    backgroundColor: 'transparent',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    flex: 1,
  },
  content: {
    position: 'relative',
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.primary}33`,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 0,
    color: Colors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '700',
    color: Colors.white,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    margin: 0,
  },
  profileHeader: {
    backgroundColor: Colors.background,
    padding: 24,
    paddingVertical: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  avatar: {
    width: 40,
    height: 40,
    color: Colors.secondary,
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  profileInfo: {
    flex: 1,
  },
  name: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 4,
  },
  stats: {
    flexDirection: 'row',
    gap: 8,
    fontSize: 16,
  },
  statsNumber: {
    color: Colors.primary,
    fontSize: 16,
  },
  statsDivider: {
    color: Colors.primary,
    fontSize: 16,
  },
  statsLabel: {
    color: Colors.primary,
    fontSize: 16,
  },
  priorityCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.primary}33`,
  },
  checkboxLabel: {
    fontSize: 15,
    color: Colors.white,
  },
  groupsSection: {
    padding: 16,
    paddingHorizontal: 20,
  },
  groupsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 16,
  },
  groupsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  groupItem: {
    width: '48%',
    marginBottom: 0,
  },
  groupCheckbox: {
    width: 15,
    height: 15,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checked: {
    backgroundColor: Colors.primary,
  },
  groupName: {
    fontSize: 15,
    color: Colors.white,
  },
  historySection: {
    padding: 16,
    paddingHorizontal: 20,
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: `${Colors.primary}33`,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 16,
  },
  historyList: {
    gap: 16,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyDate: {
    fontSize: 13,
    color: Colors.white,
    fontWeight: '500',
  },
  historyEvent: {
    fontSize: 13,
    color: Colors.white,
  },
  emptyHistoryText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
    marginTop: 8,
  },
  hideEventsSection: {
    padding: 16,
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    marginTop: 16,
  },
  hideEventsCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 10,
    paddingLeft: 20, // Add consistent left padding for alignment
  },
  blockContainer: {
    paddingLeft: 20, // Match the left padding of the checkbox for alignment
  },
  blockButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingLeft: 0,
    paddingVertical: 0,
    height: 'auto',
    minHeight: 0,
    alignItems: 'flex-start',
    justifyContent: 'flex-start'
  },
  blockButtonText: {
    color: Colors.primary,
    fontSize: 12,
    textAlign: 'left',
    textDecorationLine: 'underline',
  },
  blockText: {
    fontSize: 9,
    color: '#666666',
    marginTop: 8,
  },
});
