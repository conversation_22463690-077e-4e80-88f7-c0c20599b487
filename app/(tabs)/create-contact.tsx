import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import { FlatTabs } from '@/components/ui/flatTabs';
import { ConnectContactsScreen } from '@/components/AddContacts/ConnectContactsScreen';
import { ContactsConnectedTab } from '@/components/AddContacts/ContactsConnectedTab';
import { PhoneTab } from '@/components/AddContacts/PhoneTab';
import { EmailTab } from '@/components/AddContacts/EmailTab';
import { useContactsStore } from '@/stores/contacts';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const AddContactsPage = () => {
  const { navigate } = useRouter();
  const [activeTab, setActiveTab] = React.useState<'CONTACTS' | 'PHONE' | 'EMAIL'>('CONTACTS');
  const [isConnected, setIsConnected] = useState(false);
  const { hasPermission, checkPermission } = useContactsStore();

  // Check if user already has permission on component mount
  useEffect(() => {
    const initializePermission = async () => {
      await checkPermission();
    };

    initializePermission();
  }, [checkPermission]);

  // Update isConnected when hasPermission changes
  useEffect(() => {
    if (hasPermission) {
      setIsConnected(true);
    }
  }, [hasPermission]);

  const handleConnect = () => {
    setIsConnected(true);
  };

  const renderContent = () => {
    if (!isConnected) {
      return <ConnectContactsScreen onConnect={handleConnect} />;
    }

    switch (activeTab) {
      case 'CONTACTS':
        return <ContactsConnectedTab showSearchBar={true} />;
      case 'PHONE':
        return <PhoneTab />;
      case 'EMAIL':
        return <EmailTab />;
      default:
        return null;
    }
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={() => navigate('/contacts')}>
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <Text style={styles.title}>ADD CONTACTS</Text>
          </View>

          <FlatTabs
            tabs={['CONTACTS', 'PHONE', 'EMAIL']}
            activeTab={activeTab}
            onTabChange={(tab) => setActiveTab(tab as 'CONTACTS' | 'PHONE' | 'EMAIL')}
            style={{ backgroundColor: Colors.backgroundDark }}
          />

          {renderContent()}
        </View>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    color: Colors.primary,
    display: 'flex',
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '800',
    color: Colors.white,
    margin: 0,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
  },
  mainContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: 80,
    paddingHorizontal: 20,
    textAlign: 'center',
  },
  icon: {
    width: 85,
    height: 85,
    marginBottom: 16,
    color: Colors.primary,
  },
  heading: {
    fontSize: 21,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
    marginBottom: 24,
  },
  description: {
    fontSize: 15,
    color: Colors.white,
    margin: 0,
    marginBottom: 48,
    maxWidth: 300,
    lineHeight: 1.5 * 15,
  },
  connectButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
    color: Colors.primary,
    paddingVertical: 13,
    fontSize: 16,
    fontWeight: '500',
    borderRadius: 4,
    width: 260,
    height: 'auto',
  },
});

export default AddContactsPage;