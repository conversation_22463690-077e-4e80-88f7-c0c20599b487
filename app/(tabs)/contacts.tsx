import React, { useEffect, useCallback, useState } from 'react';
import { useFocusEffect } from 'expo-router';
import { View, TouchableOpacity, StyleSheet, ScrollView, ActivityIndicator, Alert, Image } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { ChevronLeft, Plus } from 'lucide-react-native';
import { FlatTabs } from '@/components/ui/flatTabs';
import Svg, { Path } from 'react-native-svg';
import { useApiContactsStore } from '@/stores/apiContacts';
import { AvatarIcon, ClockIcon } from '@/assets/icons';
import { cloudfrontService } from '@/services/cloudfront';
import { connectRequestService, ConnectRequestResponse } from '@/services/connectRequest';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const ContactsPage = () => {
  const { navigate } = useRouter();
  const [activeTab, setActiveTab] = React.useState<'ALL' | 'PRIORITY'>('ALL');
  const { contacts, isLoading, error, fetchContacts, togglePriority } = useApiContactsStore();
  const [signedProfileUrls, setSignedProfileUrls] = useState<Record<number, string>>({});
  const [pendingConnectRequests, setPendingConnectRequests] = useState<ConnectRequestResponse[]>([]);
  const [loadingConnectRequests, setLoadingConnectRequests] = useState(false);

  // Function to fetch pending connect requests
  const fetchPendingConnectRequests = useCallback(async () => {
    setLoadingConnectRequests(true);
    try {
      const response = await connectRequestService.getAllConnectRequests();
      if (response.data) {
        // API already filters for current user's pending requests (owner_id and not accepted/declined)
        setPendingConnectRequests(response.data);
      }
    } catch (error) {
      console.error('Error fetching pending connect requests:', error);
    } finally {
      setLoadingConnectRequests(false);
    }
  }, []);

  console.log('Pending connect requests:');
  console.log(pendingConnectRequests);

  // Initial fetch
  useEffect(() => {
    fetchContacts();
    fetchPendingConnectRequests();
  }, [fetchPendingConnectRequests]);

  // Fetch contacts when screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchContacts();
      fetchPendingConnectRequests();
    }, [fetchContacts, fetchPendingConnectRequests])
  );

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
    }
  }, [error]);

  // Sign profile image URLs when contacts change
  useEffect(() => {
    const signProfileImages = async () => {
      const signedUrls: Record<number, string> = {};

      for (const contact of contacts) {
        if (contact.user?.profile_image_url) {
          try {
            // Only sign S3 URLs
            if (cloudfrontService.isS3Url(contact.user.profile_image_url)) {
              const signedUrl = await cloudfrontService.getSignedUrl(contact.user.profile_image_url);
              signedUrls[contact.id] = signedUrl;
            } else {
              signedUrls[contact.id] = contact.user.profile_image_url;
            }
          } catch (error) {
            console.error(`Error signing profile image URL for contact ${contact.id}:`, error);
            // Use original URL as fallback
            signedUrls[contact.id] = contact.user.profile_image_url;
          }
        }
      }

      setSignedProfileUrls(signedUrls);
    };

    if (contacts.length > 0) {
      signProfileImages();
    }
  }, [contacts]);

  // Create a combined list of contacts and pending connect requests
  const filteredContacts = contacts.filter(contact =>
    activeTab === 'ALL' || (activeTab === 'PRIORITY' && contact.is_priority)
  );

  // Sort contacts alphabetically by first name, then last name
  const sortedContacts = [...filteredContacts].sort((a, b) => {
    const nameA = `${a.first_name} ${a.last_name || ''}`.trim().toLowerCase();
    const nameB = `${b.first_name} ${b.last_name || ''}`.trim().toLowerCase();
    return nameA.localeCompare(nameB);
  });

  // For the ALL tab, also include pending connect requests
  const pendingRequestsForDisplay = activeTab === 'ALL' ? pendingConnectRequests : [];

  // Sort pending requests alphabetically by first name, then last name
  const sortedPendingRequests = [...pendingRequestsForDisplay].sort((a, b) => {
    const nameA = `${a.first_name} ${a.last_name || ''}`.trim().toLowerCase();
    const nameB = `${b.first_name} ${b.last_name || ''}`.trim().toLowerCase();
    return nameA.localeCompare(nameB);
  });

  // Combined list for display
  const allItemsForDisplay = [
    ...sortedContacts.map(contact => ({ type: 'contact' as const, data: contact })),
    ...sortedPendingRequests.map(request => ({ type: 'pending_request' as const, data: request }))
  ];

  // Navigate to create-contact when there are no contacts
  useEffect(() => {
    if (!isLoading && !loadingConnectRequests && allItemsForDisplay.length === 0) {
      navigate('/create-contact');
    }
  }, [isLoading, loadingConnectRequests, allItemsForDisplay.length, navigate]);

  const handleTogglePriority = async (contactId: number) => {
    await togglePriority(contactId);
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={() => navigate('/people')}>
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <Text style={styles.title}>CONTACTS</Text>
            <TouchableOpacity style={styles.newButton} onPress={() => navigate('/create-contact')}>
              <Plus size={24} color={Colors.primary} />
              <Text style={styles.newButtonText}>NEW</Text>
            </TouchableOpacity>
          </View>

          <FlatTabs
            tabs={['ALL', 'PRIORITY']}
            activeTab={activeTab}
            onTabChange={(tab) => setActiveTab(tab as 'ALL' | 'PRIORITY')}
            style={{ backgroundColor: Colors.backgroundDark }}
          />

          {(isLoading || loadingConnectRequests) ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FFFF00" />
              <Text style={styles.loadingText}>Loading contacts...</Text>
            </View>
          ) : allItemsForDisplay.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {activeTab === 'ALL' ? 'No contacts found' : 'No priority contacts found'}
              </Text>
              <TouchableOpacity
                style={styles.addContactButton}
                onPress={() => navigate('/create-contact')}
              >
                <Text style={styles.addContactButtonText}>Add Contacts</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <ScrollView style={styles.contactsList}>
              {allItemsForDisplay.map((item) => {
                if (item.type === 'contact') {
                  const contact = item.data;
                  return (
                    <TouchableOpacity
                      key={`contact-${contact.id}`}
                      style={styles.contactItem}
                      onPress={() => navigate(`/contact?id=${contact.id}`)}
                    >
                      {contact.user?.profile_image_url ? (
                        <Image
                          source={{ uri: signedProfileUrls[contact.id] || contact.user.profile_image_url }}
                          style={styles.profileImage}
                        />
                      ) : (
                        <View style={styles.avatarPlaceholder}>
                          <AvatarIcon size={24} color={Colors.hyperBlue} />
                        </View>
                      )}
                      <Text style={styles.name}>{contact.first_name} {contact.last_name || ''}</Text>
                      <TouchableOpacity
                        onPress={(e) => {
                          e.stopPropagation();
                          handleTogglePriority(contact.id);
                        }}
                      >
                        {contact.is_priority ? (
                          <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" style={styles.star}>
                            <Path d="M8 0L10.2047 5.27841L16 5.27841L11.1803 8.94318L13.0902 14.4216L8 11.0464L2.90983 14.4216L4.81966 8.94318L0 5.27841L5.79529 5.27841L8 0Z" fill={Colors.primary}/>
                          </Svg>
                        ) : (
                          <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" stroke={Colors.primary} style={styles.star}>
                            <Path d="M8 0L10.2047 5.27841L16 5.27841L11.1803 8.94318L13.0902 14.4216L8 11.0464L2.90983 14.4216L4.81966 8.94318L0 5.27841L5.79529 5.27841L8 0Z" stroke={Colors.primary} fill="none"/>
                          </Svg>
                        )}
                      </TouchableOpacity>
                    </TouchableOpacity>
                  );
                } else {
                  // Pending connect request
                  const request = item.data;
                  return (
                    <View
                      key={`pending-${request.id}`}
                      style={styles.contactItem}
                    >
                      <View style={styles.avatarPlaceholder}>
                        <AvatarIcon size={24} color={Colors.hyperBlue} />
                      </View>
                      <Text style={styles.name}>{request.first_name} {request.last_name || ''}</Text>
                      <View style={styles.clockIconContainer}>
                        <ClockIcon size={16} color={Colors.primary} />
                      </View>
                    </View>
                  );
                }
              })}
            </ScrollView>
          )}
        </View>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
  },
  addContactButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  addContactButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    padding: 0,
    color: Colors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '700',
    color: Colors.white,
    margin: 0,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
  },
  newButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    padding: 0,
    color: Colors.primary,
    marginLeft: 'auto',
  },
  newButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary,
  },
  contactsList: {
    paddingHorizontal: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primaryDark,
  },
  avatar: {
    marginRight: 12,
  },
  profileImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  name: {
    flex: 1,
    fontSize: 16,
    color: Colors.white,
  },
  star: {
    width: 16,
    height: 16,
  },
  clockIconContainer: {
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ContactsPage;