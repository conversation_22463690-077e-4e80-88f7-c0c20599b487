import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { Svg, Path } from 'react-native-svg';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

export default function PeoplePage() {
  const router = useRouter();

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <Text style={styles.title}>PEOPLE</Text>

          <View style={styles.tabs}>
            <TouchableOpacity
              style={[styles.tab, { borderBottomWidth: 1, borderBottomColor: `${Colors.primaryDark}33` }]}
              onPress={() => router.push('/contacts')}
            >
              <View style={styles.iconContainer}>
                <Svg width={20} height={26} viewBox="0 0 24 25" fill="none">
                  <Path d="M11.859 15.093C11.859 15.093 3.234 15.093 0 22.639C3.80204 24.0254 7.8123 24.7544 11.859 24.795C15.9057 24.7544 19.916 24.0254 23.718 22.639C20.484 15.093 11.859 15.093 11.859 15.093ZM11.859 14.015C15.093 14.015 17.25 10.781 17.25 5.39002C17.25 -0.000976562 11.859 -0.000976562 11.859 -0.000976562C11.859 -0.000976562 6.467 2.38419e-05 6.467 5.39102C6.467 10.782 8.625 14.015 11.859 14.015Z" fill={Colors.primary}/>
                </Svg>
              </View>
              <Text style={styles.tabText}>Contacts</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.tab}
              onPress={() => router.push('/groups')}
            >
              <View style={styles.iconContainer}>
                <Svg width={26} height={26} viewBox="0 0 39 29" fill="none">
                  <Path opacity="0.5" d="M27.679 14.486C27.679 14.486 19.973 14.486 17.079 21.229C20.4773 22.4683 24.0619 23.12 27.679 23.156C31.2961 23.12 34.8806 22.4683 38.279 21.229C35.386 14.486 27.679 14.486 27.679 14.486ZM27.679 13.523C30.569 13.523 32.496 10.633 32.496 5.817C32.496 1.001 27.679 1 27.679 1C27.679 1 22.862 1 22.862 5.817C22.862 10.634 24.789 13.523 27.679 13.523Z" fill={Colors.primary} stroke="white"/>
                  <Path opacity="0.8" d="M11.596 14.486C11.596 14.486 3.88997 14.486 0.995972 21.229C4.39432 22.4683 7.97888 23.12 11.596 23.156C15.2131 23.12 18.7976 22.4683 22.196 21.229C19.303 14.486 11.596 14.486 11.596 14.486ZM11.596 13.523C14.486 13.523 16.413 10.633 16.413 5.817C16.413 1.001 11.596 1 11.596 1C11.596 1 6.77897 1 6.77897 5.817C6.77897 10.634 8.70597 13.523 11.596 13.523Z" fill={Colors.primary} stroke="white"/>
                  <Path d="M19.638 18.954C19.638 18.954 11.932 18.954 9.03796 25.697C12.4363 26.9363 16.0209 27.588 19.638 27.624C23.2551 27.588 26.8396 26.9363 30.238 25.697C27.345 18.954 19.638 18.954 19.638 18.954ZM19.638 17.991C22.528 17.991 24.455 15.101 24.455 10.285C24.455 5.46902 19.638 5.46802 19.638 5.46802C19.638 5.46802 14.821 5.46802 14.821 10.285C14.821 15.102 16.748 17.991 19.638 17.991Z" fill={Colors.primary} stroke="white"/>
                </Svg>
              </View>
              <Text style={styles.tabText}>Groups</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',  // changed from 100vh to 100%
  },
  content: {
    position: 'relative',
    minHeight: '100%',  // changed from 100vh to 100%
    backgroundColor: Colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.white,
    margin: 0,
    padding: 20,
    backgroundColor: Colors.backgroundDark,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
  },
  tabs: {
    flexDirection: 'column',
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 10,
    marginHorizontal: 10,
    backgroundColor: 'transparent',
    width: '100%',
  },
  iconContainer: {
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    color: Colors.primary,
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 12,
  },
  tabActive: {
    backgroundColor: 'white',
  },
  tabTextActive: {
    color: Colors.primary,
  },
});