import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet, SafeAreaView, ActivityIndicator, Alert } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { ChevronLeft, Check } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import AddMembers, { Contact } from '@/components/AddMembers/AddMembers';
import Svg, { Path } from 'react-native-svg';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useGroupsStore } from '@/stores/groups';
import { useApiContactsStore } from '@/stores/apiContacts';
import { useAuthStore } from '@/stores/auth';
import { ContactResponse } from '@/services/contact';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

// Helper function to convert API contacts to the format expected by the components
const mapContactToComponentFormat = (contact: ContactResponse, selected: boolean = false): Contact => ({
  id: contact.id.toString(),
  name: `${contact.first_name} ${contact.last_name || ''}`.trim(),
  selected
});

export default function EditGroupPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  // Get the group ID from params, don't use a default value
  const groupId = params.id ? parseInt(params.id as string) : 0;

  // Get group and contacts data from stores
  const { selectedGroup, isLoading: isLoadingGroup, error: groupError, getGroupById, updateGroup } = useGroupsStore();
  const { contacts, isLoading: isLoadingContacts, error: contactsError, fetchContacts } = useApiContactsStore();
  const { userData } = useAuthStore();

  // Local state for selected members and contacts
  const [members, setMembers] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [groupName, setGroupName] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch group and contacts data
  useEffect(() => {
    getGroupById(groupId);
    fetchContacts();
  }, [groupId]);

  // Handle errors
  useEffect(() => {
    if (groupError) {
      Alert.alert('Error', groupError, [
        { text: 'Go Back', onPress: () => router.back() }
      ]);
    }
    if (contactsError) {
      Alert.alert('Error', contactsError);
    }
  }, [groupError, contactsError, router]);

  // Update members and group name when group data changes
  useEffect(() => {
    if (selectedGroup) {
      // Set the group name
      setGroupName(selectedGroup.name || '');

      // Map group members to the format expected by the component
      if (selectedGroup.members) {
        const formattedMembers = selectedGroup.members.map(member =>
          mapContactToComponentFormat(member, true)
        );
        setMembers(formattedMembers);
      }
    }
  }, [selectedGroup]);

  const toggleMember = (contactId: string) => {
    setMembers(prev => {
      const updated = prev.map(member =>
        member.id === contactId
          ? { ...member, selected: !member.selected }
          : member
      );
      console.log('Updated members after toggle:', updated);

      // If a member was deselected, also remove it from selectedContacts if it's there
      const toggledMember = updated.find(member => member.id === contactId);
      if (toggledMember && !toggledMember.selected) {
        setSelectedContacts(prev => prev.filter(id => id !== contactId));
      }

      return updated;
    });
  };

  const toggleContact = (contactId: string) => {
    setSelectedContacts(prev =>
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  // Filter out contacts that are already members of the group, contacts without user_id, and the current user's contact
  const memberIds = members.map(member => member.id);
  const availableContacts = contacts
    .filter(contact => contact.user_id && contact.user_id !== userData?.id && !memberIds.includes(contact.id.toString()))
    .map(contact => mapContactToComponentFormat(contact, selectedContacts.includes(contact.id.toString())));

  // Handle updating the group
  const handleUpdateGroup = async () => {
    // Validate that we have a valid group ID
    if (!groupId) {
      Alert.alert('Error', 'Invalid group ID', [
        { text: 'Go Back', onPress: () => router.back() }
      ]);
      return;
    }

    // Validate group name
    if (!groupName.trim()) {
      Alert.alert('Error', 'Group name is required');
      return;
    }

    setIsUpdating(true);
    try {
      // Get the IDs of members that are still selected
      console.log('All members before filtering:', members);
      const selectedMemberIds = members
        .filter(member => member.selected)
        .map(member => parseInt(member.id));

        console.log('Selected member IDs:', selectedMemberIds);

      // Get the IDs of newly selected contacts
      const newContactIds = selectedContacts.map(id => parseInt(id));

      // Combine both sets of IDs and remove duplicates
      const allSelectedIds = [...new Set([...selectedMemberIds, ...newContactIds])];

      console.log('New contact IDs:', newContactIds);
      console.log('All selected IDs (deduplicated):', allSelectedIds);

      // Call the API to update both group name and members
      const success = await updateGroup(groupId, groupName.trim(), allSelectedIds);

      if (success) {
        Alert.alert('Success', 'Group updated successfully', [
          { text: 'OK', onPress: () => router.navigate(`/group?id=${groupId}`) }
        ]);
      } else {
        // If not successful, show an error message
        Alert.alert('Error', 'Failed to update group. The group may not exist.', [
          { text: 'Go Back', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      console.error('Error updating group:', error);
      Alert.alert('Error', 'Failed to update group. Please try again.', [
        { text: 'Go Back', onPress: () => router.back() }
      ]);
    } finally {
      setIsUpdating(false);
    }
  };

  const renderMember = ({ item }: { item: Contact }) => (
    <TouchableOpacity
      style={styles.memberItem}
      onPress={() => toggleMember(item.id)}
    >
      <View style={[styles.checkbox, item.selected && styles.checkboxChecked]}>
        {item.selected && <Check size={12} color={Colors.black} />}
      </View>
      <Svg style={styles.avatar} width={17} height={18} viewBox="0 0 17 18" fill="none">
        <Path
          d="M8.5 10.818C8.5 10.818 2.318 10.818 0 16.227C2.72509 17.2209 5.59947 17.7437 8.5 17.773C11.4005 17.7437 14.2749 17.2209 17 16.227C14.682 10.818 8.5 10.818 8.5 10.818ZM8.5 10.045C10.818 10.045 12.364 7.727 12.364 3.863C12.364 -0.000999928 8.5 2.67573e-10 8.5 2.67573e-10C8.5 2.67573e-10 4.636 0 4.636 3.864C4.636 7.728 6.182 10.045 8.5 10.045Z"
          fill={Colors.secondary}
        />
      </Svg>
      <Text style={styles.name}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <SafeAreaView style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <Text style={styles.title}>Edit Group: {groupName || 'Loading...'}</Text>
          </View>

          <Text style={styles.membersTitle}>Current members</Text>

          {isLoadingGroup ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading members...</Text>
            </View>
          ) : members.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No members in this group</Text>
            </View>
          ) : (
            <FlatList
              data={members}
              renderItem={renderMember}
              keyExtractor={(item) => item.id}
              style={styles.membersList}
              contentContainerStyle={{ paddingHorizontal: 20 }}
            />
          )}

          <Text style={styles.addMembersTitle}>Add members</Text>

          {isLoadingContacts ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading contacts...</Text>
            </View>
          ) : availableContacts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No additional contacts available</Text>
            </View>
          ) : (
            <AddMembers
              contacts={availableContacts}
              onContactToggle={toggleContact}
              withPadding={true}
            />
          )}

          <Text style={styles.groupNameTitle}>Group name</Text>
          <View style={styles.groupNameSection}>
            <TextInput
              style={styles.groupNameInput}
              placeholder="Name your group"
              placeholderTextColor={Colors.primary}
              value={groupName}
              onChangeText={setGroupName}
              maxLength={75}
            />
            <Text style={styles.counter}>{groupName.length}/75</Text>
          </View>

          <Button
            style={styles.updateButton}
            textStyle={styles.updateButtonText}
            onPress={handleUpdateGroup}
            disabled={isUpdating}
          >
            {isUpdating ? 'UPDATING...' : 'UPDATE GROUP'}
          </Button>
        </View>
      </View>
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 10,
  },
  container: {
    backgroundColor: 'transparent',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    flex: 1,
  },
  content: {
    position: 'relative',
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 100, // Increased to avoid tab navigator overlap
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 0,
    color: Colors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '700',
    color: Colors.white,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    margin: 0,
  },
  membersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 20,
  },
  membersList: {
    marginVertical: 0,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    gap: 12,
  },
  checkbox: {
    width: 15,
    height: 15,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
  },
  avatar: {
    width: 17,
    height: 17,
    color: Colors.secondary,
  },
  name: {
    fontSize: 16,
    color: Colors.white,
  },
  addMembersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 20,
  },
  groupNameTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 20,
  },
  groupNameSection: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  groupNameInput: {
    width: '100%',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    fontSize: 16,
    color: Colors.primary,
    backgroundColor: 'transparent',
  },
  counter: {
    textAlign: 'right',
    fontSize: 12,
    color: Colors.white,
    marginTop: 4,
  },
  searchInput: {
    width: '100%',
    marginHorizontal: 20,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary,
    fontSize: 16,
    color: Colors.primary,
    backgroundColor: 'transparent',
  },
  updateButton: {
    backgroundColor: Colors.primary,
    color: Colors.black,
    boxShadow: `3px 3px 0px ${Colors.primaryDark}`,
    elevation: 4, // For Android
    width: 280,
    alignSelf: 'center',
    marginTop: 50,
    marginBottom: 0,
    borderWidth: 1,
    borderColor: Colors.primary,
    height: 48,
    borderRadius: 0,
  },
  updateButtonText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '500',
  },
});