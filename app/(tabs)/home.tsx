import {
  PlusIcon,
  SearchIcon,
  Check,
} from "lucide-react-native";
import React, { useState, useEffect, useCallback } from "react";
import { useFocusEffect } from "expo-router";
import { View, Image, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Text as NativeText } from "react-native";
import { Text, TextInput } from "@/components/Themed";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Event } from "@/data/events";
import { eventService, EventResponse } from "@/services/event";
import { contactService, ContactResponse } from "@/services/contact";
import { useAuthStore } from "@/stores/auth";
import { cloudfrontService } from "@/services/cloudfront";
import Colors from "@/constants/Colors";
import DynamicStatusBar from "@/components/DynamicStatusBar";

// Helper function to convert API event to frontend event model
const mapApiEventToFrontend = (apiEvent: EventResponse, currentUserId?: number): Event => {
  // Determine if the current user is the owner
  const isOwner = apiEvent.owner_id === currentUserId;

  // Determine if the current user is invited
  const isInvited = Array.isArray(apiEvent.invitees) && apiEvent.invitees.includes(currentUserId || -1) || false;

  // Determine if the current user is a participant
  let isParticipant = false;
  let userStatus: 'invited' | 'going' | null = null;

  // Check if the current user is in the participants array
  if (Array.isArray(apiEvent.participants)) {
    // Handle both array of objects and array of numbers
    if (typeof apiEvent.participants[0] === 'object') {
      // Array of participant objects
      isParticipant = apiEvent.participants.some(p => p.user_id === currentUserId);

      if (isParticipant) {
        const participant = apiEvent.participants.find(p => p.user_id === currentUserId);
        if (participant?.status === 'going') userStatus = 'going';
      }
    } else {
      // Array of user IDs
      isParticipant = apiEvent.participants.includes(currentUserId || -1);
      if (isParticipant) userStatus = 'going';
    }
  }

  // Set invited status if not already a participant
  if (!isParticipant && isInvited) userStatus = 'invited';

  // Determine if the event is at capacity
  const maxParticipants = apiEvent.max_participants || Number.MAX_SAFE_INTEGER;
  const isAtCapacity = (apiEvent.participants_count || 0) >= maxParticipants;

  // Determine if the event is in the past
  let isPastEvent = false;

  // Check if the confirmed date is in the past
  if (apiEvent.confirmed_date) {
    const confirmedDate = new Date(apiEvent.confirmed_date);
    isPastEvent = confirmedDate < new Date();
  }
  // If no confirmed date, check if the last date in the event dates is in the past
  else if (apiEvent.eventDates && apiEvent.eventDates.length > 0) {
    // Sort dates and check if the last one is in the past
    const sortedDates = [...apiEvent.eventDates].sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
    const lastDate = new Date(sortedDates[sortedDates.length - 1].date);
    isPastEvent = lastDate < new Date();
  } else if (apiEvent.date && apiEvent.date.length > 0) {
    // Sort dates and check if the last one is in the past
    const sortedDates = [...apiEvent.date].sort((a, b) => {
      return new Date(a).getTime() - new Date(b).getTime();
    });
    const lastDate = new Date(sortedDates[sortedDates.length - 1]);
    isPastEvent = lastDate < new Date();
  }

  // Determine if quorum is met
  const quorumMet = apiEvent.quorum_met ||
    ((apiEvent.participants_count || 0) >= (apiEvent.quorum || 1));

  // Extract dates from eventDates array
  let dates: string[] = [];

  // If confirmed_date exists and quorum is met, use only that date
  if (apiEvent.confirmed_date && quorumMet) {
    dates = [apiEvent.confirmed_date];
  } else if (apiEvent.eventDates && apiEvent.eventDates.length > 0) {
    dates = apiEvent.eventDates.map(eventDate => eventDate.date);
  } else if (apiEvent.date && apiEvent.date.length > 0) {
    // Fallback to date property if it exists
    dates = apiEvent.date;
  }

  return {
    id: apiEvent.id,
    name: apiEvent.name,
    date: dates, // Use the extracted dates
    location: apiEvent.location, // Include the location property
    address: apiEvent.address,
    owner: apiEvent.owner,
    owner_id: apiEvent.owner_id, // Include the owner_id
    imageURL: apiEvent.image_url || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png', // We'll get signed URL later
    hasOptions: apiEvent.has_options,
    quorumMet: quorumMet,
    isAtCapacity: isAtCapacity,
    userStatus: userStatus,
    isOwner: isOwner,
    isPastEvent: isPastEvent,
    quorum: apiEvent.quorum,
    max_participants: apiEvent.max_participants,
    confirmed_date: apiEvent.confirmed_date, // Include the confirmed date
    response_cutoff: apiEvent.response_cutoff, // Include the response cutoff
    created_at: apiEvent.created_at,
    updated_at: apiEvent.updated_at,
  };
};

// Helper function to get display text for sort options
const getSortDisplayText = (sortValue: string): string => {
  switch (sortValue) {
    case "happening-soonest":
      return "Happening Soonest";
    case "posted-recently":
      return "Posted Most Recently";
    case "priority":
      return "Priority";
    case "past-events":
      return "Past Events";
    default:
      return sortValue;
  }
};

const Home = (): JSX.Element => {
  const [sortBy, setSortBy] = useState("happening-soonest");
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("ALL");
  const [contacts, setContacts] = useState<any[]>([]);
  const currentUser = useAuthStore(state => state.userData);
  const { navigate } = useRouter();

  // Get refresh parameter from URL
  const params = useLocalSearchParams();
  const refreshParam = params.refresh;

  // Function to fetch contacts
  const fetchContacts = useCallback(async () => {
    try {
      const response = await contactService.getAllContacts();
      if (response.data) {
        setContacts(response.data);
      }
    } catch (err) {
      console.error('Error fetching contacts:', err);
    }
  }, []);

  // Function to fetch events
  const fetchEvents = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First fetch contacts to get hide_events preferences
      await fetchContacts();

      const response = await eventService.getAllEvents();

      if (response.error) {
        setError(response.error);
        return;
      }

      if (response.data) {
        // Map API events to frontend model
        const mappedEvents = response.data.map(event =>
          mapApiEventToFrontend(event, currentUser?.id)
        );

        // console.log("RESPONSE DATA EVENTS")
        // console.log(response.data)

        // Get signed URLs for all event images
        for (const event of mappedEvents) {
          if (event.imageURL) {
            try {
              // Only sign S3 URLs, leave other URLs as is
              const signedUrl = await cloudfrontService.getSignedUrl(event.imageURL);
              event.imageURL = signedUrl;
            } catch (error) {
              console.error('Error getting signed URL:', error);
              // Keep the original URL if signing fails
            }
          }
        }

        setEvents(mappedEvents);
      }
    } catch (err) {
      console.error('Error fetching events:', err);
      setError('Failed to load events. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id, fetchContacts]);

  // Fetch events when refreshParam changes
  useEffect(() => {
    fetchEvents();
  }, [refreshParam, fetchEvents]);

  // Fetch events when screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchEvents();
    }, [fetchEvents])
  );

  const handleEventClick = (eventId: number) => {
    // Navigate to the event page with the event ID as a parameter
    navigate(`/(tabs)/event?id=${eventId}`);
  };

  const formatOwnerDisplay = (event: Event): string => {
    // If the current user is the owner, show "Created by you"
    if (event.isOwner) {
      return "Created by you";
    }
    // Otherwise show "Invited by [owner]"
    return `Invited by ${event.owner}`;
  };

  // Toggle search visibility and clear search when closing
  const toggleSearch = () => {
    if (isSearchVisible) {
      setSearchQuery(""); // Clear search when closing
    }
    setIsSearchVisible(!isSearchVisible);
  };

  // Filter events by title based on search query, active tab, hide_events preference, and response cutoff
  const filteredEvents = events.filter(event => {
    // First filter by search query
    const matchesSearch = searchQuery.trim() === "" ||
      event.name.toLowerCase().includes(searchQuery.toLowerCase());

    if (!matchesSearch) return false;

    // Filter out events created by contacts with hide_events set to true
    // Skip this check for events created by the current user
    if (!event.isOwner && event.owner_id) {
      // Find the contact that corresponds to the event owner
      const eventOwnerContact = contacts.find(contact =>
        contact.user_id === event.owner_id
      );

      // If we found a contact and hide_events is true, filter out this event
      if (eventOwnerContact && eventOwnerContact.hide_events) {
        return false;
      }
    }

    // Check if the event is within the response cutoff period and mark it
    if (event.date && event.date.length > 0 && event.response_cutoff) {
      try {
        const earliestDate = event.date.reduce((earliest, dateStr) => {
          const date = new Date(dateStr);
          return date < earliest ? date : earliest;
        }, new Date(event.date[0]));

        const cutoffTimeMs = event.response_cutoff * 60 * 60 * 1000; // Convert hours to milliseconds
        const now = new Date();

        // Check if the event is within the response cutoff period
        if (earliestDate.getTime() - now.getTime() < cutoffTimeMs) {
          // Mark the event for styling purposes
          event.isWithinResponseCutoff = true;

          // Filter out events within response cutoff period ONLY if:
          // 1. The user is not the owner of the event, AND
          // 2. We're not in the CREATED tab, AND
          // 3. We're not in the ALL tab (ALL should show all events)
          if (!event.isOwner && activeTab !== "CREATED" && activeTab !== "ALL") {
            return false;
          }
        }
      } catch (error) {
        console.error('Error checking response cutoff:', error);
      }
    }

    // Then filter by tab
    switch (activeTab) {
      case "INVITED":
        return event.userStatus === 'invited' && !event.isOwner;
      case "CREATED":
        return event.isOwner;
      case "SIGNED UP":
        // Show events the user has signed up for AND events they created
        return event.userStatus === 'going' || event.isOwner;
      case "ALL":
      default:
        // For ALL tab, show all events regardless of user relationship
        return true;
    }
  });

  // Filter past events based on sort selection (independent of tab selection)
  const pastEventsFilteredEvents = filteredEvents.filter(event => {
    if (sortBy === "past-events") {
      // When "past-events" is selected, only show past events
      return event.isPastEvent;
    } else {
      // For all other sort options, hide past events
      return !event.isPastEvent;
    }
  });

  // Sort events based on selected option
  const sortedEvents = [...pastEventsFilteredEvents].sort((a, b) => {
    if (sortBy === "happening-soonest") {
      // Compare the earliest date in each array
      if (!a.date || a.date.length === 0) {
        if (!b.date || b.date.length === 0) return 0;
        return 1; // Empty dates go last
      }
      if (!b.date || b.date.length === 0) return -1;

      // Find the earliest date in each event's date array
      try {
        const earliestDateA = a.date.reduce((earliest, dateStr) => {
          const date = new Date(dateStr);
          return date < earliest ? date : earliest;
        }, new Date(a.date[0]));

        const earliestDateB = b.date.reduce((earliest, dateStr) => {
          const date = new Date(dateStr);
          return date < earliest ? date : earliest;
        }, new Date(b.date[0]));

        return earliestDateA.getTime() - earliestDateB.getTime();
      } catch (error) {
        console.error('Error sorting dates:', error);
        return 0;
      }
    } else if (sortBy === "posted-recently") {
      // Sort by created_at timestamp (most recent first)
      const createdAtA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const createdAtB = b.created_at ? new Date(b.created_at).getTime() : 0;
      return createdAtB - createdAtA; // Descending order (newest first)
    }
    return 0;
  });

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <Text style={[styles.title]}>EVENTS</Text>

          <View style={styles.header}>
            <TouchableOpacity onPress={toggleSearch}>
              <SearchIcon
                width={24}
                height={24}
                color={Colors.primary}
              />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => navigate('/(tabs)/create-event')}>
              <PlusIcon
                width={24}
                height={24}
                color={Colors.primary}
              />
            </TouchableOpacity>
          </View>

          {isSearchVisible && (
            <View style={styles.searchContainer}>
              <View style={styles.searchInputWrapper}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search events by title"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoFocus
                  placeholderTextColor={Colors.gray}
                />
              </View>
            </View>
          )}

          <Tabs
            defaultValue="ALL"
            value={activeTab}
            onValueChange={setActiveTab}
            style={[styles.tabs, isSearchVisible && { marginTop: 60 }]}
          >
            <TabsList style={styles.tabsList}>
              <TabsTrigger value="ALL" style={styles.tab}>
                ALL
              </TabsTrigger>
              <TabsTrigger value="INVITED" style={styles.tab}>
                INVITED
              </TabsTrigger>
              <TabsTrigger value="CREATED" style={styles.tab}>
                CREATED
              </TabsTrigger>
              <TabsTrigger value="SIGNED UP" style={styles.tab}>
                SIGNED UP
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger style={[styles.sortDropdown, isSearchVisible && { marginTop: 60 }]}>
              <Text style={[styles.sortLabel]}>{getSortDisplayText(sortBy)}</Text>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="happening-soonest">Happening Soonest</SelectItem>
              <SelectItem value="posted-recently">Posted Most Recently</SelectItem>
              <SelectItem value="priority">Priority</SelectItem>
              <SelectItem value="past-events">Past Events</SelectItem>
            </SelectContent>
          </Select>

          <ScrollView
            style={[styles.eventList, isSearchVisible && { marginTop: 60 }]}
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.eventListContent}
          >
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={Colors.secondary} />
                <Text style={[styles.loadingText]}>Loading events...</Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Text style={[styles.errorText]}>{error}</Text>
                <TouchableOpacity
                  style={styles.retryButton}
                  onPress={fetchEvents}
                >
                  <Text style={[styles.retryButtonText]}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : sortedEvents.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText]}>Nothing coming up at the moment.</Text>
                <TouchableOpacity onPress={() => navigate('/create-event')}>
                  <Text style={[styles.emptyLinkText]}>Create an event!</Text>
                </TouchableOpacity>
              </View>
            ) : (
              sortedEvents.map((event) => (
                <TouchableOpacity
                  key={event.id}
                  onPress={() => handleEventClick(event.id)}
                >
                <Card
                  style={[
                    styles.eventCard,
                    {
                      backgroundColor: "transparent",
                      borderWidth: 1.5,
                      // Dashed border if quorum not met
                      // Red solid border if at capacity AND user has not joined
                      // Blue solid border if quorum met
                      borderStyle: event.quorumMet ? "solid" : "dashed",
                      borderColor: event.isPastEvent ? "#cccccc" :
                                   (event.isAtCapacity && event.userStatus !== 'going') ? "#E35D5D" :
                                   event.quorumMet ? Colors.secondary : Colors.primary,
                      // Keep the visual indication but make all events clickable
                      // Apply 50% opacity to past events and events within response cutoff period that are owned by the user
                      opacity: event.isPastEvent || (event.isWithinResponseCutoff && event.isOwner) ? 0.5 : 1
                    }
                  ]}
                >
                  <CardContent style={styles.cardContent}>
                    {/* Only show checkmark when user has signed up (going) */}
                    {event.userStatus === 'going' && (
                      <View style={[
                        styles.checkmark,
                        event.quorumMet ? styles.quorumMetCheckmark : styles.joinedCheckmark
                      ]}>
                        <Check width={12} height={12} strokeWidth={4} color={event.quorumMet ? Colors.black : Colors.primary} />
                      </View>
                    )}

                    <View style={styles.eventInfo}>
                      <Image
                        style={styles.eventImage}
                        source={{ uri: event.imageURL }}
                        onError={(e) => {
                          console.error('Image loading error:', e.nativeEvent.error, 'URL:', event.imageURL);
                        }}
                      />

                      <View style={styles.eventDetails}>
                        <Text style={[styles.createdBy]}>{formatOwnerDisplay(event)}</Text>

                        <View>
                          <Text style={[styles.eventTitle]}>{event.name}</Text>
                          <Text style={[styles.eventDate]}>
                            {event.date && event.date.length > 0 ?
                              event.date.length > 1 ?
                                'Flexible dates'
                              : (() => {
                                try {
                                  return new Date(event.date[0]).toLocaleString('en-US', {
                                    hour: 'numeric',
                                    minute: '2-digit',
                                    hour12: true,
                                    weekday: 'short',
                                    month: 'short',
                                    day: 'numeric'
                                  });
                                } catch (error) {
                                  console.error(`Error formatting date for event ${event.id}:`, error);
                                  return 'Date TBD';
                                }
                              })()
                              : 'Date TBD'
                            }
                          </Text>
                          {(event.location || event.address) && (
                            <Text style={[styles.eventLocation, { fontFamily: 'WorkSans_400Regular' }]}>
                              {event.location ?
                                (event.address && event.address.trim() !== '' ?
                                  `${event.location}, ${event.address}` :
                                  event.location) :
                                event.address}
                            </Text>
                          )}
                        </View>
                      </View>
                    </View>
                  </CardContent>
                </Card>
              </TouchableOpacity>
              ))
            )}
          </ScrollView>
        </View>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
  },
  title: {
    position: 'absolute',
    top: 10,
    left: 22,
    fontSize: 28,
    fontWeight: '800',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 1)',
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
  },
  header: {
    position: 'absolute',
    top: 20,
    right: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  tabs: {
    width: 340,
    top: 60,
    left: 20,
  },
  searchContainer: {
    position: 'absolute',
    top: 60,
    left: 24,
    right: 24,
    zIndex: 10,
    paddingTop: 0,
    paddingBottom: 20, // Add padding below the search input
  },
  searchInputWrapper: {
    height: 40,
  },
  searchInput: {
    width: '100%',
    height: 40,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#FFFF00',
    borderRadius: 4,
    paddingHorizontal: 12,
    fontSize: 16,
    color: Colors.background,
  },
  tabsList: {
    padding: 0,
    height: 'auto',
    backgroundColor: 'transparent',
    display: 'flex',
    gap: 6,
    width: '100%',
  },
  tab: {
    fontSize: 11,
    fontWeight: '500',
    borderRadius: 0,
    height: 27,
    backgroundColor: 'transparent',
    color: Colors.primary,
    borderWidth: 1,
    borderColor: Colors.primary,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 'auto',
    flex: 0,
  },
  sortLabel: {
    fontSize: 13,
    color: Colors.white,
  },
  sortDropdown: {
    position: 'absolute',
    width: 180,
    height: 24,
    paddingVertical: 1,
    top: 102,
    left: 20,
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: Colors.white,
    borderRadius: 0,
    fontSize: 13,
    color: Colors.white,
    paddingHorizontal: 8,
  },
  eventList: {
    position: 'absolute',
    top: 143,
    left: 11,
    width: '100%',
    bottom: 0,
    paddingRight: 22,
  },
  eventListContent: {
    flexDirection: 'column',
    gap: 7,
    paddingBottom: 80, // Extra padding at the bottom to account for tab bar
  },
  eventCard: {
    width: '100%',
    minHeight: 95,
    borderRadius: 0,
  },
  cardContent: {
    padding: 0,
    position: 'relative'
  },
  checkmark: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  invitedCheckmark: {
    backgroundColor: "transparent",
  },
  joinedCheckmark: {
    backgroundColor: "transparent",
    borderColor: Colors.primary,
  },
  quorumMetCheckmark: {
    backgroundColor: Colors.secondary,
    borderColor: Colors.secondary,
  },
  eventInfo: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center', // Center items vertically
    minHeight: 94, // Minimum height to ensure consistent sizing
  },
  eventImage: {
    width: 70,
    height: 70, // Make it square
    aspectRatio: 1, // Ensure it stays square
  },
  eventDetails: {
    marginLeft: 20,
    flexDirection: 'column',
    flex: 1, // Take remaining space
  },
  createdBy: {
    fontWeight: '500',
    color: Colors.white,
    fontSize: 13,
    lineHeight: 19,
  },
  eventTitle: {
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 20, // Increased line height for better readability
    marginTop: 0,
    color: Colors.primary,
    flexWrap: 'wrap', // Allow text to wrap
  },
  eventDate: {
    fontWeight: '500',
    fontSize: 13,
    lineHeight: 19,
    color: Colors.white,
  },
  eventLocation: {
    fontSize: 13,
    lineHeight: 19,
    color: Colors.white,
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.secondary,
  },
  // Error state styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  errorText: {
    marginBottom: 15,
    fontSize: 16,
    color: '#E35D5D',
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.white,
    textAlign: 'center',
    marginBottom: 10,
  },
  emptyLinkText: {
    fontSize: 16,
    color: Colors.primary,
    textAlign: 'center',
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
  },
});

export default Home;
