import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, ActivityIndicator, Alert, StyleSheet, ScrollView } from 'react-native';
import { Text, TextInput } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';
import { userPreferenceService, UserPreferenceRequest } from '@/services/userPreference';
import { useAuthStore } from '@/stores/auth';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const PaymentsSettingsPage = () => {
  const { navigate } = useRouter();
  const { userData } = useAuthStore(state => ({
    userData: state.userData
  }));

  const [venmoEnabled, setVenmoEnabled] = useState(false);
  const [paypalEnabled, setPaypalEnabled] = useState(false);
  const [venmoUsername, setVenmoUsername] = useState('');
  const [paypalUsername, setPaypalUsername] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [preferenceId, setPreferenceId] = useState<number | null>(null);

  // Load user preferences when component mounts
  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    setIsLoading(true);
    try {
      if (!userData?.id) {
        Alert.alert('Error', 'User ID not found. Please log in again.');
        return;
      }

      const response = await userPreferenceService.getOrCreateForCurrentUser();

      if (response.data) {
        setVenmoEnabled(response.data.venmo_enabled);
        setPaypalEnabled(response.data.paypal_enabled);
        setVenmoUsername(response.data.venmo_username || '');
        setPaypalUsername(response.data.paypal_username || '');
        setPreferenceId(response.data.id);
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      Alert.alert('Error', 'Failed to load payment settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!preferenceId) {
      Alert.alert('Error', 'User preference ID not found. Please try again.');
      return;
    }

    // Validate that usernames are provided when payment methods are enabled
    if (venmoEnabled && !venmoUsername.trim()) {
      Alert.alert('Username Required', 'Please enter your Venmo username or disable Venmo.');
      return;
    }

    if (paypalEnabled && !paypalUsername.trim()) {
      Alert.alert('Username Required', 'Please enter your PayPal username or disable PayPal.');
      return;
    }

    setIsSaving(true);
    try {
      const preferenceData: UserPreferenceRequest = {
        venmo_enabled: venmoEnabled,
        paypal_enabled: paypalEnabled,
        venmo_username: venmoUsername,
        paypal_username: paypalUsername
      };

      const response = await userPreferenceService.updateUserPreference(preferenceId, preferenceData);

      if (response.data) {
        Alert.alert('Success', 'Payment settings updated successfully');
        navigate('/settings');
      } else if (response.error) {
        Alert.alert('Error', response.error);
      }
    } catch (error) {
      console.error('Error saving payment settings:', error);
      Alert.alert('Error', 'Failed to save payment settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <>
        <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
        <View style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading payment settings...</Text>
          </View>
        </View>
      </>
    );
  }

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <ScrollView style={styles.content}>
          <View style={styles.topBar}>
            <TouchableOpacity style={styles.backButton} onPress={() => navigate('/settings')}>
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave} disabled={isSaving}>
              {isSaving ? (
                <ActivityIndicator color={Colors.primary} size="small" />
              ) : (
                <Text style={styles.saveButtonText}>SAVE</Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.header}>
            <Text style={styles.title}>EDIT PAYMENTS</Text>
          </View>

          <Text style={styles.description}>
            Link accounts to send and receive payments for events.
          </Text>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Connect payment methods</Text>

            <View style={styles.paymentMethods}>
              <View style={styles.paymentMethodContainer}>
                <View style={styles.paymentMethod}>
                  <Text style={styles.methodName}>Venmo</Text>
                  <TouchableOpacity
                    style={styles.toggle}
                    onPress={() => {
                      // Only allow enabling if username is provided
                      if (!venmoEnabled || venmoUsername.trim()) {
                        setVenmoEnabled(!venmoEnabled);
                      } else {
                        Alert.alert('Username Required', 'Please enter your Venmo username before enabling.');
                      }
                    }}
                  >
                    <View style={[
                      styles.toggleHandle,
                      venmoEnabled && styles.toggleHandleActive
                    ]} />
                  </TouchableOpacity>
                </View>
                <TextInput
                  style={[styles.usernameInput, !venmoUsername.trim() && venmoEnabled && styles.inputError]}
                  placeholder="Enter your Venmo username"
                  value={venmoUsername}
                  onChangeText={setVenmoUsername}
                  placeholderTextColor="#999"
                />
              </View>

              <View style={styles.paymentMethodContainer}>
                <View style={styles.paymentMethod}>
                  <Text style={styles.methodName}>PayPal</Text>
                  <TouchableOpacity
                    style={styles.toggle}
                    onPress={() => {
                      // Only allow enabling if username is provided
                      if (!paypalEnabled || paypalUsername.trim()) {
                        setPaypalEnabled(!paypalEnabled);
                      } else {
                        Alert.alert('Username Required', 'Please enter your PayPal username before enabling.');
                      }
                    }}
                  >
                    <View style={[
                      styles.toggleHandle,
                      paypalEnabled && styles.toggleHandleActive
                    ]} />
                  </TouchableOpacity>
                </View>
                <TextInput
                  style={[styles.usernameInput, !paypalUsername.trim() && paypalEnabled && styles.inputError]}
                  placeholder="Enter your PayPal username"
                  value={paypalUsername}
                  onChangeText={setPaypalUsername}
                  placeholderTextColor="#999"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    height: '100%',
  },
  content: {
    position: 'relative',
    height: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    paddingHorizontal: 25,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    padding: 0,
    marginLeft: -5,
    color: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: 'transparent',
    padding: 0,
  },
  saveButtonText: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '700',
  },
  header: {
    paddingHorizontal: 25,
    paddingBottom: 16,
    marginBottom: 16,
    backgroundColor: Colors.backgroundDark,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: Colors.white,
    margin: 0,
  },
  description: {
    paddingHorizontal: 25,
    fontSize: 16,
    color: Colors.white,
    marginBottom: 32,
  },
  section: {
    paddingHorizontal: 25,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 16,
  },
  paymentMethods: {
    flexDirection: 'column',
    gap: 24,
  },
  paymentMethodContainer: {
    marginBottom: 8,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  usernameInput: {
    height: 40,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 4,
    paddingHorizontal: 10,
    fontSize: 16,
    color: Colors.white,
    backgroundColor: Colors.background,
  },
  inputError: {
    borderColor: '#E35D5D',
    borderWidth: 2,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  toggle: {
    width: 45,
    height: 15,
    backgroundColor: `${Colors.primary}33`,
    borderRadius: 7.5,
    position: 'relative',
  },
  toggleHandle: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: `${Colors.primary}99`,
    backgroundColor: 'white',
    borderRadius: 11,
    position: 'absolute',
    top: -3.5,
    left: 0,
  },
  toggleHandleActive: {
    transform: [{ translateX: 23 }],
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.primary,
  },
});

export default PaymentsSettingsPage;