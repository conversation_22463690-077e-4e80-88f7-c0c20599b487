import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView, ActivityIndicator, Image, Alert, Linking } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/stores/auth';
import { useTutorialStore } from '@/stores/tutorial';
import { authService } from '@/services/auth';
import * as ImagePicker from 'expo-image-picker';
import { uploadService } from '@/services/upload';
import { userService } from '@/services/user';
import { cloudfrontService } from '@/services/cloudfront';
import { AvatarIcon, CameraIcon } from '@/assets/icons';
import DeleteAccountModal from '@/components/DeleteAccountModal/DeleteAccountModal';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const SettingsPage = () => {
  const { navigate } = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [profileImageUri, setProfileImageUri] = useState<string | undefined>(undefined);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);
  const { logout, userData, updateUserData } = useAuthStore(state => ({
    logout: state.logout,
    userData: state.userData,
    updateUserData: state.updateUserData
  }));
  const resetTutorialState = useTutorialStore(state => state.resetTutorialState);

  // Load profile image when component mounts
  useEffect(() => {
    const loadProfileImage = async () => {
      if (userData?.profileImageUrl) {
        try {
          // Get signed URL for the profile image
          const signedUrl = await cloudfrontService.getSignedUrl(userData.profileImageUrl);
          setProfileImageUri(signedUrl);
        } catch (error) {
          console.error('Error getting signed profile image URL:', error);
          // Use the original URL if signing fails
          setProfileImageUri(userData.profileImageUrl);
        }
      } else {
        console.log('No profile image URL found in userData');
      }
    };

    loadProfileImage();
  }, [userData?.profileImageUrl]);

  // Function to pick an image from the library
  const pickImage = async () => {
    try {
      // Request permission to access the media library
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photos');
        return;
      }

      // Launch the image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImageUri = result.assets[0].uri;

        // Start the upload process
        setIsUploading(true);
        try {
          // Upload the image to S3 and get the URL
          const uploadedUrl = await uploadService.uploadImageToS3(selectedImageUri);

          // Update the profile image URI state
          setProfileImageUri(uploadedUrl);

          // Update the user's profile_image_url in the database
          if (userData?.id) {

            try {
              const response = await userService.updateUser(userData.id, {
                profile_image_url: uploadedUrl
              });

              if (response.error) {
                console.error('Error updating profile image:', response.error);
                Alert.alert('Update Error', response.error);
                return;
              }

              // Update the auth store with the new profile image URL
              updateUserData({
                ...userData,
                profileImageUrl: uploadedUrl
              });
            } catch (updateError) {
              console.error('Exception updating profile image:', updateError);
              Alert.alert('Update Error', 'Failed to update profile image. Please try again.');
              return;
            }

            Alert.alert('Success', 'Profile image updated successfully');
          }
        } catch (error) {
          console.error('Error uploading profile image:', error);
          Alert.alert('Upload Error', 'Failed to upload image. Please try again.');
        } finally {
          setIsUploading(false);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const handleLogout = async () => {
    setIsLoggingOut(true);


    try {
      // Call the API to logout if we have a token
      if (userData?.token) {
        await authService.logout(userData.token);
      }

      // Add a small delay to allow any ongoing API requests to complete
      // This prevents 401 errors from background polling
      await new Promise(resolve => setTimeout(resolve, 500));

      // Clear local auth state
      logout();

      // Navigate to sign-in screen
      navigate('/(auth)/sign-in');
    } catch (error) {
      console.error('Logout screen error:', error);
      // Display error message if needed
      // Even if the API call fails, we still want to log out locally

      // Add delay here too
      await new Promise(resolve => setTimeout(resolve, 500));
      logout();
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsDeleteModalOpen(false);
    setIsDeletingAccount(true);

    try {
      if (!userData?.id) {
        Alert.alert('Error', 'User data not found');
        return;
      }

      // Update user's enabled status to false
      const response = await userService.updateUser(userData.id, {
        enabled: false
      });

      if (response.error) {
        console.error('Error disabling account:', response.error);
        Alert.alert('Error', response.error);
        return;
      }

      // Log out the user after successful account deletion
      if (userData?.token) {
        await authService.logout(userData.token);
      }

      // Add delay to allow ongoing requests to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Clear local auth state
      logout();

      // Navigate to sign-in screen
      navigate('/(auth)/sign-in');

      Alert.alert('Account Deleted', 'Your account has been successfully deleted.');
    } catch (error) {
      console.error('Delete account error:', error);
      Alert.alert('Error', 'Failed to delete account. Please try again.');
    } finally {
      setIsDeletingAccount(false);
    }
  };

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.avatar} onPress={pickImage} disabled={isUploading}>
              {profileImageUri ? (
                <>
                  <Image
                    source={{ uri: profileImageUri }}
                    style={styles.profileImage}
                    resizeMode="cover"
                    onError={(e) => {
                      console.error('Error loading profile image:', e.nativeEvent.error);
                      // If there's an error loading the image, try using the original URL
                      if (userData?.profileImageUrl && profileImageUri !== userData.profileImageUrl) {
                        console.log('Falling back to original URL');
                        setProfileImageUri(userData.profileImageUrl);
                      }
                    }}
                  />
                  <View style={styles.cameraIconContainer}>
                    <CameraIcon size={16} color={Colors.primary} />
                  </View>
                </>
              ) : (
                <>
                  <AvatarIcon size={48} color={Colors.hyperBlue} />
                  <View style={styles.cameraIconContainer}>
                    <CameraIcon size={16} color={Colors.primary} />
                  </View>
                </>
              )}
              {isUploading && (
                <View style={styles.uploadingOverlay}>
                  <ActivityIndicator size="small" color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
            <Text style={styles.name}>{userData?.displayName || 'User'}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>ACCOUNT SETTINGS</Text>
            <View style={styles.menuItems}>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => navigate('/settings-personal-info')}
              >
                <Text style={styles.menuItemText}>Personal Information</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => navigate('/settings-payments')}
              >
                <Text style={styles.menuItemText}>Payments</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => navigate('/settings-notifications')}
              >
                <Text style={styles.menuItemText}>Notifications</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem}>
                <Text style={styles.menuItemText}>Event Settings</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => {
                  resetTutorialState();
                  navigate('/tutorial');
                }}
              >
                <Text style={styles.menuItemText}>Redo Tutorial</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>SUPPORT</Text>
            <View style={styles.menuItems}>
              <TouchableOpacity style={styles.menuItem}>
                <Text style={styles.menuItemText}>Glossary</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem}>
                <Text style={styles.menuItemText}>FAQ</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem}>
                <Text style={styles.menuItemText}>Give Us Feedback</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>LEGAL</Text>
            <View style={styles.menuItems}>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => Linking.openURL('https://www.qwrm.app/terms-and-conditions/')}
              >
                <Text style={styles.menuItemText}>Terms of Service</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => Linking.openURL('https://www.qwrm.app/privacy/')}
              >
                <Text style={styles.menuItemText}>Privacy Policy</Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={styles.deleteAccountButton}
            onPress={() => setIsDeleteModalOpen(true)}
            disabled={isDeletingAccount}
          >
            {isDeletingAccount ? (
              <ActivityIndicator color="#DC2626" />
            ) : (
              <Text style={styles.deleteAccountButtonText}>Delete Account</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
            disabled={isLoggingOut}
          >
            {isLoggingOut ? (
              <ActivityIndicator color={Colors.primary} />
            ) : (
              <Text style={styles.logoutButtonText}>Log Out</Text>
            )}
          </TouchableOpacity>

          <DeleteAccountModal
            open={isDeleteModalOpen}
            onOpenChange={setIsDeleteModalOpen}
            onDelete={handleDeleteAccount}
          />

        </ScrollView>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    padding: 25,
    paddingBottom: 47,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    paddingVertical: 24,
    backgroundColor: Colors.background,
  },
  avatar: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    borderRadius: 24,
    // overflow: 'hidden',
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  cameraIconContainer: {
    position: 'absolute',
    bottom: -3,
    right: -2,
    zIndex: 10,
  },
  cameraIcon: {
    width: 16,
    height: 14,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 24,
  },
  name: {
    fontSize: 24,
    fontWeight: '500',
    color: Colors.white,
    margin: 0,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary,
    marginTop: 24,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  menuItems: {
    backgroundColor: 'transparent',
  },
  menuItem: {
    width: '100%',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.primary}33`,
  },
  menuItemText: {
    fontSize: 16,
    color: Colors.white,
    fontWeight: '400',
  },
  deleteAccountButton: {
    width: '100%',
    paddingVertical: 16,
    backgroundColor: 'transparent',
    marginTop: 8,
  },
  deleteAccountButtonText: {
    fontSize: 16,
    color: '#DC2626',
    fontWeight: '600',
  },
  logoutButton: {
    width: '100%',
    paddingVertical: 16,
    backgroundColor: 'transparent',
    marginTop: 8,
    marginBottom: 60, // Added bottom margin to ensure button is visible above tab navigator
  },
  logoutButtonText: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '600',
  },
  contentContainer: {
    paddingBottom: 100, // Add extra padding at the bottom to ensure content is not hidden by tab navigator
  }
});

export default SettingsPage;