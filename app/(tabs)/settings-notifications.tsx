import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, ScrollView, ActivityIndicator, Alert, StyleSheet } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { ChevronLeft, Check } from 'lucide-react-native';
import { userPreferenceService, UserPreferenceRequest } from '@/services/userPreference';
import { useAuthStore } from '@/stores/auth';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

interface NotificationOption {
  id: string;
  text: string;
  checked: boolean;
}

const NotificationsSettingsPage = () => {
  const { navigate } = useRouter();
  const { userData } = useAuthStore(state => ({
    userData: state.userData
  }));

  const [pushEnabled, setPushEnabled] = useState(false);
  const [emailEnabled, setEmailEnabled] = useState(false);
  const [reminderEnabled, setReminderEnabled] = useState(false);
  const [notifications, setNotifications] = useState<NotificationOption[]>([
    {
      id: 'cancel',
      text: "An event I've signed up for is cancelled",
      checked: false
    },
    // {
    //   id: 'details',
    //   text: 'Details change for a signed-up event',
    //   checked: false
    // },
    {
      id: 'question',
      text: "A contact has asked a question about an event I've created",
      checked: false
    },
    // {
    //   id: 'toptier',
    //   text: 'A top-tier contact has posted an event',
    //   checked: false
    // }
  ]);

  const [reminders, setReminders] = useState<NotificationOption[]>([
    {
      id: 'dues',
      text: "They owe dues for an event I've created",
      checked: false
    },
    {
      id: 'tickets',
      text: "They haven't bought tickets for an event",
      checked: false
    }
  ]);

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [preferenceId, setPreferenceId] = useState<number | null>(null);

  // Load user preferences when component mounts
  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    setIsLoading(true);
    try {
      if (!userData?.id) {
        Alert.alert('Error', 'User ID not found. Please log in again.');
        return;
      }

      const response = await userPreferenceService.getOrCreateForCurrentUser();

      if (response.data) {
        const prefs = response.data;
        setPreferenceId(prefs.id);

        // Set main toggles
        setPushEnabled(prefs.push_notifications_enabled);
        setEmailEnabled(prefs.email_notifications_enabled);
        setReminderEnabled(prefs.participant_reminders_enabled);

        // Update notification checkboxes
        setNotifications([
          {
            id: 'cancel',
            text: "An event I've signed up for is cancelled",
            checked: prefs.notify_event_cancellation
          },
          // {
          //   id: 'details',
          //   text: 'Details change for a signed-up event',
          //   checked: prefs.notify_event_changes
          // },
          {
            id: 'question',
            text: "A contact has asked a question about an event I've created",
            checked: prefs.notify_contact_questions
          },
          // {
          //   id: 'toptier',
          //   text: 'A top-tier contact has posted an event',
          //   checked: prefs.notify_top_tier_events
          // }
        ]);

        // Update reminder checkboxes
        setReminders([
          {
            id: 'dues',
            text: "They owe dues for an event I've created",
            checked: prefs.notify_dues_owed
          },
          {
            id: 'tickets',
            text: "They haven't bought tickets for an event",
            checked: prefs.notify_tickets_not_purchased
          }
        ]);
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      Alert.alert('Error', 'Failed to load notification settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!preferenceId) {
      Alert.alert('Error', 'User preference ID not found. Please try again.');
      return;
    }

    setIsSaving(true);
    try {
      // Find notification options by ID
      const cancelNotification = notifications.find(n => n.id === 'cancel');
      const detailsNotification = notifications.find(n => n.id === 'details');
      const questionNotification = notifications.find(n => n.id === 'question');
      const toptierNotification = notifications.find(n => n.id === 'toptier');

      // Find reminder options by ID
      const duesReminder = reminders.find(r => r.id === 'dues');
      const ticketsReminder = reminders.find(r => r.id === 'tickets');

      const preferenceData: UserPreferenceRequest = {
        notifications_enabled: pushEnabled || emailEnabled, // Enable notifications if either push or email is enabled
        push_notifications_enabled: pushEnabled,
        email_notifications_enabled: emailEnabled,
        participant_reminders_enabled: reminderEnabled,
        notify_event_cancellation: cancelNotification?.checked || false,
        notify_event_changes: detailsNotification?.checked || false,
        notify_contact_questions: questionNotification?.checked || false,
        notify_top_tier_events: toptierNotification?.checked || false,
        notify_dues_owed: duesReminder?.checked || false,
        notify_tickets_not_purchased: ticketsReminder?.checked || false
      };

      const response = await userPreferenceService.updateUserPreference(preferenceId, preferenceData);

      if (response.data) {
        Alert.alert('Success', 'Notification settings updated successfully');
        navigate('/settings');
      } else if (response.error) {
        Alert.alert('Error', response.error);
      }
    } catch (error) {
      console.error('Error saving notification settings:', error);
      Alert.alert('Error', 'Failed to save notification settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const toggleNotification = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, checked: !notification.checked }
          : notification
      )
    );
  };

  const toggleReminder = (id: string) => {
    setReminders(prev =>
      prev.map(reminder =>
        reminder.id === id
          ? { ...reminder, checked: !reminder.checked }
          : reminder
      )
    );
  };

  if (isLoading) {
    return (
      <>
        <DynamicStatusBar backgroundColor={Colors.background} barStyle="light-content" />
        <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading notification settings...</Text>
        </View>
      </View>
      </>
    );
  }

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <ScrollView style={styles.content}>
          <View style={styles.topBar}>
            <TouchableOpacity style={styles.backButton} onPress={() => navigate('/settings')}>
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave} disabled={isSaving}>
              {isSaving ? (
                <ActivityIndicator color={Colors.primary} size="small" />
              ) : (
                <Text style={styles.saveButtonText}>SAVE</Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.header}>
            <Text style={styles.title}>EDIT NOTIFICATIONS</Text>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Push Notifications</Text>
              <TouchableOpacity
                style={[styles.toggle, pushEnabled && styles.toggleActive]}
                onPress={() => setPushEnabled(!pushEnabled)}
              >
                <View style={[styles.toggleHandle, pushEnabled && styles.toggleHandleActive]} />
              </TouchableOpacity>
            </View>

            <Text style={styles.notifyTitle}>Notify me when:</Text>

            <View style={styles.notificationList}>
              {notifications.map(notification => (
                <View key={notification.id} style={styles.notificationItem}>
                  <TouchableOpacity
                    style={[styles.checkbox, notification.checked && styles.checkboxChecked]}
                    onPress={() => toggleNotification(notification.id)}
                  >
                    {notification.checked && <Check width={15} height={15} color={Colors.black} />}
                  </TouchableOpacity>
                  <Text style={styles.notificationText}>{notification.text}</Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Email Notifications</Text>
              <TouchableOpacity
                style={[styles.toggle, emailEnabled && styles.toggleActive]}
                onPress={() => setEmailEnabled(!emailEnabled)}
              >
                <View style={[styles.toggleHandle, emailEnabled && styles.toggleHandleActive]} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Participant Reminders</Text>
              <TouchableOpacity
                style={[styles.toggle, reminderEnabled && styles.toggleActive]}
                onPress={() => setReminderEnabled(!reminderEnabled)}
              >
                <View style={[styles.toggleHandle, reminderEnabled && styles.toggleHandleActive]} />
              </TouchableOpacity>
            </View>

            {/* <Text style={styles.notifyTitle}>Notify participants every 72 hours when:</Text>

            <View style={styles.notificationList}>
              {reminders.map(reminder => (
                <View key={reminder.id} style={styles.notificationItem}>
                  <TouchableOpacity
                    style={[styles.checkbox, reminder.checked && styles.checkboxChecked]}
                    onPress={() => toggleReminder(reminder.id)}
                  >
                    {reminder.checked && <Check width={15} height={15} color={Colors.black} />}
                  </TouchableOpacity>
                  <Text style={styles.notificationText}>{reminder.text}</Text>
                </View>
              ))}
            </View> */}
          </View>
        </ScrollView>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    height: '100%',
  },
  content: {
    position: 'relative',
    height: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    paddingHorizontal: 25,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    backgroundColor: 'transparent',
    marginLeft: -5,
    padding: 0,
    color: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: 'transparent',
    padding: 0,
  },
  saveButtonText: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '700',
  },
  header: {
    paddingHorizontal: 25,
    paddingBottom: 16,
    marginBottom: 16,
    backgroundColor: Colors.backgroundDark,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: Colors.white,
    textShadowColor: Colors.black,
    textShadowOffset: { width: 3, height: 3 },
    textShadowRadius: 0.1,
    elevation: 4, // For Android
    margin: 0,
  },
  section: {
    paddingHorizontal: 25,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    margin: 0,
  },
  toggle: {
    width: 45,
    height: 15,
    backgroundColor: `${Colors.primary}33`,
    borderRadius: 7.5,
    position: 'relative',
  },
  toggleActive: {
    backgroundColor: `${Colors.primary}33`,
  },
  toggleHandle: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: `${Colors.primary}99`,
    backgroundColor: 'white',
    borderRadius: 11,
    position: 'absolute',
    top: -3.5,
    left: 0,
  },
  toggleHandleActive: {
    transform: [{ translateX: 23 }],
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  notifyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: 16,
  },
  notificationList: {
    flexDirection: 'column',
    gap: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginBottom: 16,
  },
  checkbox: {
    width: 15,
    height: 15,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
  },
  notificationText: {
    fontSize: 16,
    color: Colors.white,
    margin: 0,
    lineHeight: 22,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.primary,
  },
});

export default NotificationsSettingsPage;