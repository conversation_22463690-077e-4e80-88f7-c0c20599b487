import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet, SafeAreaView, ActivityIndicator, Alert } from 'react-native';
import { Text } from "@/components/Themed";
import { ChevronLeft } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import { useRouter, useLocalSearchParams } from 'expo-router';
import Svg, { Path } from 'react-native-svg';
import { useGroupsStore } from '@/stores/groups';
import DeleteGroupModal from '@/components/DeleteGroupModal/DeleteGroupModal';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

export default function GroupDetailsPage() {
  const { navigate } = useRouter();
  const params = useLocalSearchParams();
  // Get the group ID from params, don't use a default value
  const groupId = params.id ? parseInt(params.id as string) : 0;

  const { selectedGroup, isLoading, error, getGroupById, deleteGroup } = useGroupsStore();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  useEffect(() => {
    getGroupById(groupId);
  }, [groupId]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'Go Back', onPress: () => navigate('/groups') }
      ]);
    }
  }, [error, navigate]);

  // Check if we have a valid group ID and group data
  useEffect(() => {
    if (!groupId) {
      Alert.alert('Error', 'Invalid group ID', [
        { text: 'Go Back', onPress: () => navigate('/groups') }
      ]);
    }
  }, [groupId, navigate]);

  const groupName = selectedGroup?.name || 'Loading...';
  const members = selectedGroup?.members || [];

  const handleDeleteGroup = async () => {
    setIsDeleteModalOpen(false);
    const success = await deleteGroup(groupId);
    if (success) {
      navigate('/groups');
    }
  };

  const renderMember = ({ item }: { item: any }) => (
    <View style={styles.memberItem}>
      <Svg width={17} height={18} viewBox="0 0 17 18" fill="none">
        <Path
          d="M8.5 10.818C8.5 10.818 2.318 10.818 0 16.227C2.72509 17.2209 5.59947 17.7437 8.5 17.773C11.4005 17.7437 14.2749 17.2209 17 16.227C14.682 10.818 8.5 10.818 8.5 10.818ZM8.5 10.045C10.818 10.045 12.364 7.727 12.364 3.863C12.364 -0.000999928 8.5 2.67573e-10 8.5 2.67573e-10C8.5 2.67573e-10 4.636 0 4.636 3.864C4.636 7.728 6.182 10.045 8.5 10.045Z"
          fill={Colors.hyperBlue}
        />
      </Svg>
      <Text style={styles.name}>{`${item.first_name} ${item.last_name || ''}`}</Text>
    </View>
  );

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <SafeAreaView style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigate('/groups')}
            >
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <Text style={styles.title}>{groupName}</Text>
          </View>

          <Text style={styles.membersTitle}>Current members</Text>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FFFF00" />
              <Text style={styles.loadingText}>Loading members...</Text>
            </View>
          ) : members.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No members found</Text>
            </View>
          ) : (
            <FlatList
              data={members}
              renderItem={renderMember}
              keyExtractor={(item) => item.id.toString()}
              style={styles.membersList}
              ItemSeparatorComponent={() => null}
              contentContainerStyle={{ paddingHorizontal: 12 }}
            />
          )}

          <Button
            style={styles.editButton}
            textStyle={styles.editButtonText}
            onPress={() => navigate(`/edit-group?id=${groupId}`)}
          >
            EDIT GROUP
          </Button>

          <Button
            style={styles.deleteButton}
            textStyle={styles.deleteButtonText}
            onPress={() => setIsDeleteModalOpen(true)}
          >
            DELETE GROUP
          </Button>

          <DeleteGroupModal
            open={isDeleteModalOpen}
            onOpenChange={setIsDeleteModalOpen}
            groupName={groupName}
            onDelete={handleDeleteGroup}
          />
        </View>
      </View>
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.primaryDark,
    marginBottom: 20,
  },
  container: {
    backgroundColor: 'transparent',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    flex: 1,
  },
  content: {
    position: 'relative',
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 100, // Increased to avoid tab navigator overlap
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 0,
    color: Colors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '700',
    color: Colors.white,
    margin: 0,
  },
  membersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 20,
  },
  membersList: {
    flex: 1,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingHorizontal: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.primaryDark,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primaryDark,
    gap: 12,
    marginBottom: -1,
  },
  avatar: {
    width: 17,
    height: 17,
    color: '#ABD4DD',
  },
  name: {
    fontSize: 16,
    color: Colors.white,
  },
  editButton: {
    width: 280,
    alignSelf: 'center',
    marginTop: 50,
    marginBottom: 0,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
    height: 48,
    borderRadius: 0,
  },
  editButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  deleteButton: {
    width: 280,
    alignSelf: 'center',
    marginTop: 15,
    marginBottom: 0,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.error,
    height: 48,
    borderRadius: 0,
  },
  deleteButtonText: {
    color: Colors.error,
    fontSize: 16,
    fontWeight: '500',
  },
});