import React, { useEffect, useCallback } from 'react';
import { useFocusEffect } from 'expo-router';
import { View, TouchableOpacity, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { Text } from "@/components/Themed";
import { useRouter } from 'expo-router';
import { ChevronLeft, Plus } from 'lucide-react-native';
import Svg, { Path } from 'react-native-svg';
import DraggableFlatList, { RenderItemParams } from 'react-native-draggable-flatlist';
import { useGroupsStore } from '@/stores/groups';
import { GroupResponse } from '@/services/group';
import Colors from '@/constants/Colors';
import DynamicStatusBar from '@/components/DynamicStatusBar';

const GroupsPage = () => {
  const { navigate } = useRouter();
  const { groups, isLoading, error, fetchGroups, reorderGroups } = useGroupsStore();

  // Initial fetch
  useEffect(() => {
    fetchGroups();
  }, []);

  // Fetch groups when screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchGroups();
    }, [fetchGroups])
  );

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
    }
  }, [error]);

  const handleDragEnd = useCallback(async ({ data }: { data: GroupResponse[] }) => {
    // Update the local state immediately for smooth UX
    await reorderGroups(data);
  }, [reorderGroups]);

  const renderGroupItem = useCallback(({ item, drag, isActive }: RenderItemParams<GroupResponse>) => {
    return (
      <TouchableOpacity
        style={[styles.groupItem, isActive && styles.groupItemActive]}
        onPress={() => navigate(`/group?id=${item.id}`)}
        onLongPress={drag}
        disabled={isActive}
      >
        <Svg width={18} height={7} viewBox="0 0 18 7" fill="none">
          <Path
            d="M18 0.954545C18 0.425568 17.5701 0 17.0357 0H0.964286C0.429911 0 0 0.425568 0 0.954545C0 1.48352 0.429911 1.90909 0.964286 1.90909H17.0357C17.5701 1.90909 18 1.48352 18 0.954545ZM18 6.04545C18 5.51648 17.5701 5.09091 17.0357 5.09091H0.964286C0.429911 5.09091 0 5.51648 0 6.04545C0 6.57443 0.429911 7 0.964286 7H17.0357C17.5701 7 18 6.57443 18 6.04545Z"
            fill={Colors.primary}
            fillOpacity="0.7"
          />
        </Svg>
        <Text style={styles.groupName}>{item.name}</Text>
      </TouchableOpacity>
    );
  }, [navigate]);

  return (
    <>
      <DynamicStatusBar backgroundColor={Colors.backgroundDark} barStyle="light-content" />
      <View style={styles.container}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={() => navigate('/people')}>
              <ChevronLeft size={24} color={Colors.primary} />
            </TouchableOpacity>
            <Text style={styles.title}>Groups</Text>
            <TouchableOpacity
              style={styles.newButton}
              onPress={() => navigate('/create-group')}
            >
              <Plus size={24} color={Colors.primary} />
              <Text style={styles.newButtonText}>NEW</Text>
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading groups...</Text>
            </View>
          ) : groups.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No groups found</Text>
              <TouchableOpacity
                style={styles.createGroupButton}
                onPress={() => navigate('/create-group')}
              >
                <Text style={styles.createGroupButtonText}>Create a Group</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <DraggableFlatList
              data={groups}
              onDragEnd={handleDragEnd}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderGroupItem}
              style={styles.groupsList}
            />
          )}
        </View>
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.white,
    marginBottom: 20,
  },
  createGroupButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  createGroupButtonText: {
    color: Colors.black,
    fontSize: 14,
    fontWeight: '500',
  },
  container: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  wrapper: {
    width: '100%',
    minHeight: '100%',
  },
  content: {
    position: 'relative',
    minHeight: '100%',
    backgroundColor: Colors.background,
    paddingBottom: 47,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 15,
    paddingHorizontal: 20,
    backgroundColor: Colors.backgroundDark,
  },
  backButton: {
    backgroundColor: 'transparent',
    padding: 0,
    color: Colors.primary,
    display: 'flex',
    alignItems: 'center',
  },
  title: {
    fontSize: 21,
    fontWeight: '700',
    color: Colors.white,
  },
  newButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: 'transparent',
    padding: 0,
    color: Colors.primary,
    marginLeft: 'auto',
  },
  newButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary,
  },
  groupsList: {
    padding: 0,
    margin: 0,
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(23, 150, 172, 0.2)',
    backgroundColor: Colors.background,
    marginHorizontal: 12,
  },
  groupIcon: {
    marginRight: 16,
  },
  groupName: {
    fontSize: 16,
    color: Colors.white,
    marginLeft: 16,
  },
  groupItemActive: {
    backgroundColor: 'rgba(23, 150, 172, 0.1)',
    transform: [{ scale: 1.02 }],
  },
});

export default GroupsPage;
