# Email Service Documentation

This document provides information on how to use the email service in the QWRM API.

> **New Feature**: Emails are now automatically sent whenever a notification is created!

## Overview

The email service uses SendGrid to send emails to users. It supports:

- Sending emails to individual users
- Sending bulk emails to multiple users
- Respecting user email notification preferences
- Using HTML templates with variable substitution

## Configuration

The email service requires the following environment variables:

- `SENDGRID_KEY`: Your SendGrid API key

Add this to your `.env` file:

```
SENDGRID_KEY=your_sendgrid_api_key_here
```

## Email Templates

Email templates are stored in the `api/src/templates` directory. The default template is `default-template.html`.

Templates use a simple placeholder system with double curly braces:

```html
<h1>Hello, {{name}}!</h1>
```

## Usage Examples

### Basic Usage

```typescript
import emailService from '../services/emailService';

// Send a simple email
await emailService.sendEmail(
  '<EMAIL>',
  'Email Subject',
  '<h1>Hello World</h1><p>This is a test email.</p>'
);
```

### Using Templates

```typescript
import emailService from '../services/emailService';
import { loadTemplate } from '../utils/emailTemplateUtils';

// Load a template and replace placeholders
const html = loadTemplate('default-template', {
  message: 'John Doe wants to connect.',
  image_url: 'https://example.com/profile.jpg',
  request_url: 'qwrm://notifications',
  app_download_url: 'https://qwrm.app/download'
});

// Send email with the template
await emailService.sendEmail(
  '<EMAIL>',
  'Connection Request',
  html
);
```

### Sending to Users with Preferences

```typescript
import emailService from '../services/emailService';
import { loadTemplate } from '../utils/emailTemplateUtils';

// Load a template
const html = loadTemplate('default-template', {
  message: 'John Doe invited you to an event.',
  image_url: 'https://example.com/profile.jpg',
  request_url: 'qwrm://event?id=123',
  app_download_url: 'https://qwrm.app/download'
});

// Send email to a user (respects their email notification preferences)
await emailService.sendEmailToUser(
  userId,
  'Connection Request',
  html
);

// Send emails to multiple users (respects their email notification preferences)
await emailService.sendEmailToUsers(
  [userId1, userId2, userId3],
  'Event Invitation',
  html
);
```

### Event Notifications

```typescript
import { sendEventInvitationEmails, sendEventCancellationEmails } from '../utils/emailNotificationUtils';

// Send event invitation emails
await sendEventInvitationEmails(
  eventId,
  hostId,
  [participant1Id, participant2Id]
);

// Send event cancellation emails
await sendEventCancellationEmails(
  eventId,
  hostId,
  [participant1Id, participant2Id]
);
```

## Testing

You can test the email service using the test endpoint:

```
GET /api/test/email?email=<EMAIL>
```

This will send a test email to the specified address using the default template.

## Notification Emails

The system now automatically sends an email whenever a notification is created. This is handled by the `notificationUtils.ts` file, which calls the email service when creating notifications.

### How It Works

1. When `createNotificationWithPush` or `createNotificationsWithPush` is called, it creates a notification in the database
2. It sends a push notification to the user's device
3. It now also sends an email notification to the user's email address using the default template

### Testing Notification Emails

You can test the notification email feature using the test endpoint:

```
GET /api/test/notification-email?senderId=1&receiverId=2
```

This will create a test notification and send both a push notification and an email to the specified recipient.

## Troubleshooting

If emails are not being sent:

1. Check that the `SENDGRID_KEY` is correctly set in your `.env` file
2. Verify that the recipient's email address is valid
3. Check the server logs for any SendGrid API errors
4. Ensure that the user has email notifications enabled in their preferences
5. For notification emails, make sure the notification is being created correctly
