import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();

// Create a Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME || 'signup_sheet',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || 'postgres',
  {
    host: process.env.DB_HOST || 'localhost',
    port: 5432,
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    timezone: '+00:00', // Set timezone to UTC
    dialectOptions: {
      ssl: { require: true, rejectUnauthorized: false },
      useUTC: true, // Ensure UTC is used
    },
    pool: {
      max: 30, // use up to 30 connections
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// Test the connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('Database connection successful');
  } catch (error) {
    console.error('Database connection error:', error);
  }
};

console.log('Database config:', sequelize.config);

testConnection();

export default sequelize;
