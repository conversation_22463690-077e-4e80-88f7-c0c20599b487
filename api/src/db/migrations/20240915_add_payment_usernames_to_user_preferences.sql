-- Migration: Add payment usernames to user_preferences table
-- Description: This migration adds venmo_username and paypal_username columns to the user_preferences table

-- Start transaction
BEGIN;

-- Add venmo_username column to user_preferences table
ALTER TABLE user_preferences ADD COLUMN venmo_username VARCHAR(100);

-- Add paypal_username column to user_preferences table
ALTER TABLE user_preferences ADD COLUMN paypal_username VARCHAR(100);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'user_preferences' AND column_name IN ('venmo_username', 'paypal_username');
