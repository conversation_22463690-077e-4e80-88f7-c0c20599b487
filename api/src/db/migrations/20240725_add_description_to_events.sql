-- Migration: Add description to events table
-- Description: This migration adds a description column to the events table

-- Start transaction
BEGIN;

-- Add description column to events table
ALTER TABLE events ADD COLUMN description TEXT;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'description';