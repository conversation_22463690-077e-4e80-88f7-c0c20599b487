-- Migration: Add participants to events table
-- Description: This migration adds a participants column to the events table to store user IDs of participants

-- Start transaction
BEGIN;

-- Add participants column to events table as a JSON array
ALTER TABLE events ADD COLUMN participants JSONB DEFAULT '[]'::jsonb;

-- Add a participants_count column for quick access to the number of participants
ALTER TABLE events ADD COLUMN participants_count INTEGER DEFAULT 0;

-- Create an index on the participants column for better performance
CREATE INDEX idx_events_participants ON events USING GIN (participants);

-- Commit transaction
COMMIT;
