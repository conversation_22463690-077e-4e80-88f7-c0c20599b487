-- Migration: Create event_dates table
-- Description: This migration creates a new event_dates table to store event dates and participants for each date

-- Start transaction
BEGIN;

-- Create event_dates table
CREATE TABLE event_dates (
    id SERIAL PRIMARY KEY,
    event_id INTEGER NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    date TIMESTAMP NOT NULL,
    participants INTEGER[] DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_event_dates_event_id ON event_dates(event_id);
CREATE INDEX idx_event_dates_date ON event_dates(date);
CREATE INDEX idx_event_dates_participants ON event_dates USING GIN (participants);

-- Migrate existing data from events.date to event_dates
INSERT INTO event_dates (event_id, date, created_at, updated_at)
SELECT 
    id AS event_id, 
    unnest(date) AS date,
    created_at,
    updated_at
FROM events
WHERE array_length(date, 1) > 0;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'event_dates';
