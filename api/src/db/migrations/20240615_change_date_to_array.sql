-- Migration: Change date column to array of timestamps
-- Description: This migration changes the date column in the events table from a string to an array of timestamps

-- Start transaction
BEGIN;

-- First, create a temporary column to store the new array format
ALTER TABLE events ADD COLUMN date_array TIMESTAMP[] DEFAULT '{}';

-- Update the temporary column with the current date value
-- Convert the existing string date to a timestamp and store it in an array
UPDATE events SET date_array = ARRAY[CASE 
    WHEN date ~ E'^\\d{4}-\\d{2}-\\d{2}' THEN date::TIMESTAMP 
    ELSE NOW() 
END];

-- Drop the old date column
ALTER TABLE events DROP COLUMN date;

-- Rename the temporary column to date
ALTER TABLE events RENAME COLUMN date_array TO date;

-- Make the date column NOT NULL
ALTER TABLE events ALTER COLUMN date SET NOT NULL;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'date';
