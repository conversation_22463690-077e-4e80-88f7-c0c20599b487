-- Migration: Make email optional in contacts table
-- Description: This migration modifies the contacts table to make the email field optional
-- and updates other fields to match the model definition.

-- Start transaction
BEGIN;

-- 1. Make email field optional (allow NULL)
ALTER TABLE contacts 
ALTER COLUMN email DROP NOT NULL;

-- 2. Make last_name field optional (allow NULL)
ALTER TABLE contacts 
ALTER COLUMN last_name DROP NOT NULL;

-- 3. Make sure is_priority field exists with default value
DO $$
BEGIN
    -- Check if is_priority column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'contacts' AND column_name = 'is_priority'
    ) THEN
        -- Add is_priority column if it doesn't exist
        ALTER TABLE contacts 
        ADD COLUMN is_priority BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- 4. Make sure enabled field has a default value and is optional
ALTER TABLE contacts 
ALTER COLUMN enabled SET DEFAULT TRUE,
ALTER COLUMN enabled DROP NOT NULL;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'contacts';
