-- Migration: Change participants from JSONB to INTEGER ARRAY
-- Description: This migration changes the participants column in the events table from JSONB to an array of integers

-- Start transaction
BEGIN;

-- First, create a temporary column to store the new array format
ALTER TABLE events ADD COLUMN participants_array INTEGER[] DEFAULT '{}';

-- Update the temporary column with data from the current participants column
-- This extracts user_id values from the JSONB array and converts them to a PostgreSQL integer array
UPDATE events 
SET participants_array = (
    SELECT ARRAY(
        SELECT (jsonb_array_elements(participants)->>'user_id')::INTEGER
        WHERE participants IS NOT NULL AND jsonb_typeof(participants) = 'array'
    )
);

-- Drop the old participants column
ALTER TABLE events DROP COLUMN participants;

-- Rename the temporary column to participants
ALTER TABLE events RENAME COLUMN participants_array TO participants;

-- Make the participants column NOT NULL with default empty array
ALTER TABLE events ALTER COLUMN participants SET NOT NULL;
ALTER TABLE events ALTER COLUMN participants SET DEFAULT '{}';

-- Drop the index on the old JSONB column if it exists
DROP INDEX IF EXISTS idx_events_participants;

-- Create a GIN index on the new array column for better performance
CREATE INDEX idx_events_participants ON events USING GIN (participants);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'participants';
