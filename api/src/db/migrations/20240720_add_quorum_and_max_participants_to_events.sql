-- Migration: Add quorum and max_participants to events table
-- Description: This migration adds quorum and max_participants columns to the events table

-- Start transaction
BEGIN;

-- Add quorum column to events table
ALTER TABLE events ADD COLUMN quorum INTEGER DEFAULT 1;

-- Add max_participants column to events table
ALTER TABLE events ADD COLUMN max_participants INTEGER DEFAULT NULL;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND (column_name = 'quorum' OR column_name = 'max_participants');
