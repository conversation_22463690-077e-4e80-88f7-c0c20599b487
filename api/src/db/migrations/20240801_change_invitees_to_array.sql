-- Migration: Change invitees from JSONB to INTEGER ARRAY
-- Description: This migration changes the invitees column in the events table from JSONB to an array of integers

-- Start transaction
BEGIN;

-- First, create a temporary column to store the new array format
ALTER TABLE events ADD COLUMN invitees_array INTEGER[] DEFAULT '{}';

-- Update the temporary column with data from the current invitees column
-- This converts the JSONB array to a PostgreSQL integer array
UPDATE events 
SET invitees_array = (
    SELECT ARRAY(
        SELECT jsonb_array_elements(invitees)::INTEGER
        WHERE invitees IS NOT NULL AND jsonb_typeof(invitees) = 'array'
    )
);

-- Drop the old invitees column
ALTER TABLE events DROP COLUMN invitees;

-- Rename the temporary column to invitees
ALTER TABLE events RENAME COLUMN invitees_array TO invitees;

-- Make the invitees column NOT NULL with default empty array
ALTER TABLE events ALTER COLUMN invitees SET NOT NULL;
ALTER TABLE events ALTER COLUMN invitees SET DEFAULT '{}';

-- Drop the index on the old JSONB column if it exists
DROP INDEX IF EXISTS idx_events_invitees;

-- Create a GIN index on the new array column for better performance
CREATE INDEX idx_events_invitees ON events USING GIN (invitees);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'invitees';
