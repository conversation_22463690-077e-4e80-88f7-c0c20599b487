-- Migration: Add event_id to notifications table
-- Description: This migration adds an event_id column to the notifications table for event-related notifications

-- Start transaction
BEGIN;

-- Add event_id column to notifications table
ALTER TABLE notifications ADD COLUMN event_id INTEGER;

-- Add foreign key constraint
ALTER TABLE notifications 
ADD CONSTRAINT fk_notifications_event 
FOREIGN KEY (event_id) 
REFERENCES events(id) 
ON DELETE SET NULL;

-- Add index for better performance
CREATE INDEX idx_notifications_event_id ON notifications(event_id);

-- Commit transaction
COMMIT;
