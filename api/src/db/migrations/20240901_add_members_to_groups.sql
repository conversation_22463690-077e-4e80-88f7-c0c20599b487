-- Migration: Add members to contact_groups table
-- Description: This migration adds a members column to the contact_groups table to store contact IDs of group members

-- Start transaction
BEGIN;

-- Add members column to contact_groups table as an array of integers
ALTER TABLE contact_groups ADD COLUMN members INTEGER[] DEFAULT '{}';

-- Create an index on the members column for better performance
CREATE INDEX idx_contact_groups_members ON contact_groups USING GIN (members);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'contact_groups' AND column_name = 'members';
