-- Migration: Seed events with date arrays
-- Description: This migration adds sample events with date arrays

-- Start transaction
BEGIN;

-- Insert sample events with date arrays
INSERT INTO events (
    name, 
    date, 
    location, 
    address, 
    owner, 
    owner_id, 
    image_url, 
    has_options, 
    quorum_met, 
    enabled
) VALUES 
(
    '<PERSON>''s American Utopia',
    ARRAY['2023-11-16 17:30:00'::TIMESTAMP, '2023-12-05 20:00:00'::TIMESTAMP, '2023-12-13 20:00:00'::TIMESTAMP],
    'Hudson Theater',
    'Hudson Theater, Brooklyn, NY',
    '<PERSON>',
    1,
    'https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000',
    TRUE,
    FALSE,
    TRUE
),
(
    '<PERSON> at Baby''s All Right',
    ARRAY['2023-10-18 19:00:00'::TIMESTAMP],
    'Baby''s All Right',
    'Baby''s All Right, Brooklyn, NY',
    '<PERSON>',
    2,
    'https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000',
    FALSE,
    TRUE,
    TRUE
),
(
    'Movie Night: Girlfriends',
    ARRAY['2023-10-13 19:30:00'::TIMESTAMP],
    'Metrograph',
    'Metrograph, New York, NY',
    'Stephanie B.',
    3,
    'https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000',
    FALSE,
    TRUE,
    TRUE
),
(
    'Handball at Greenwood Playground',
    ARRAY['2023-10-07 14:00:00'::TIMESTAMP, '2023-10-14 14:00:00'::TIMESTAMP],
    'Greenwood Playground',
    'Greenwood Playground, Brooklyn, NY',
    'Ben H.',
    4,
    'https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000',
    TRUE,
    FALSE,
    TRUE
),
(
    'Jazz at Lincoln Center',
    ARRAY['2023-10-21 20:00:00'::TIMESTAMP],
    'Lincoln Center',
    'Lincoln Center, New York, NY',
    'Sarah M.',
    5,
    'https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000',
    FALSE,
    TRUE,
    TRUE
),
(
    'Book Club: The Overstory',
    ARRAY['2023-10-22 15:00:00'::TIMESTAMP],
    'Prospect Park',
    'Prospect Park, Brooklyn, NY',
    'Alex K.',
    6,
    'https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000',
    FALSE,
    TRUE,
    TRUE
);

-- Commit transaction
COMMIT;
