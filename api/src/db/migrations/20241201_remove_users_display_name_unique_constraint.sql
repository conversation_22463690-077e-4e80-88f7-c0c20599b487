-- Migration: Remove unique constraint from display_name in users table
-- Description: This migration removes the unique constraint from the display_name field in the users table
-- to allow multiple users with the same display name.

-- Start transaction
BEGIN;

-- Drop the unique constraint on the display_name field
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_display_name_key;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'users' AND column_name = 'display_name';
