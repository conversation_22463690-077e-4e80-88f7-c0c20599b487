-- Migration: Add address fields to events table
-- Description: This migration adds separate address fields to the events table

-- Start transaction
BEGIN;

-- Add new address fields to events table
ALTER TABLE events ADD COLUMN location_name VARCHAR(255);
ALTER TABLE events ADD COLUMN address_line1 VARCHAR(255);
ALTER TABLE events ADD COLUMN address_line2 VARCHAR(255);
ALTER TABLE events ADD COLUMN city VARCHAR(100);
ALTER TABLE events ADD COLUMN state VARCHAR(50);
ALTER TABLE events ADD COLUMN zip VARCHAR(20);
ALTER TABLE events ADD COLUMN meeting_point VARCHAR(255);

-- Update the new fields with data from existing fields where possible
-- Extract location_name from location
UPDATE events SET location_name = location WHERE location IS NOT NULL AND location != '';

-- For existing events, we'll set address_line1 to the full address since we can't reliably parse it
UPDATE events SET address_line1 = address WHERE address IS NOT NULL AND address != '';

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name IN ('location_name', 'address_line1', 'address_line2', 'city', 'state', 'zip', 'meeting_point');
