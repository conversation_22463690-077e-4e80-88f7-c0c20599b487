-- Migration: Add sort_order to contact_groups table
-- Description: This migration adds a sort_order column to the contact_groups table to enable custom ordering of groups

-- Start transaction
BEGIN;

-- Add sort_order column to contact_groups table
ALTER TABLE contact_groups ADD COLUMN sort_order INTEGER DEFAULT 0;

-- Create an index on the sort_order column for better performance
CREATE INDEX idx_contact_groups_sort_order ON contact_groups(sort_order);

-- Update existing groups to have sequential sort_order values based on their creation date
UPDATE contact_groups
SET sort_order = subquery.rn
FROM (
    SELECT id, row_number() OVER (PARTITION BY owner_id ORDER BY created_at) as rn
    FROM contact_groups
    WHERE sort_order = 0
) AS subquery
WHERE contact_groups.id = subquery.id;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default
-- FROM information_schema.columns
-- WHERE table_name = 'contact_groups' AND column_name = 'sort_order';
