-- Migration: Create email_verification_tokens table
-- Date: 2025-01-30
-- Description: Create table to store email verification tokens

CREATE TABLE email_verification_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    used BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- <PERSON>reate indexes for performance
CREATE INDEX idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE UNIQUE INDEX idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);

-- Add comments to document the table and columns
COMMENT ON TABLE email_verification_tokens IS 'Stores email verification tokens for user email verification';
COMMENT ON COLUMN email_verification_tokens.user_id IS 'Foreign key reference to the user';
COMMENT ON COLUMN email_verification_tokens.token IS 'Unique verification token';
COMMENT ON COLUMN email_verification_tokens.expires_at IS 'When the token expires';
COMMENT ON COLUMN email_verification_tokens.used IS 'Whether the token has been used for verification';
