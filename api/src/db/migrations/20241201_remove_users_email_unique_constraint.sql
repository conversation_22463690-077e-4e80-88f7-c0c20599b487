-- Migration: Remove unique constraint from email in users table
-- Description: This migration removes the unique constraint from the email field in the users table
-- to allow multiple users with the same email address as long as only one is enabled.

-- Start transaction
BEGIN;

-- Drop the unique constraint on the email field
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_email_key;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'users' AND column_name = 'email';
