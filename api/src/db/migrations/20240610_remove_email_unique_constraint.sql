-- Migration: Remove unique constraint from email in contacts table
-- Description: This migration removes the unique constraint from the email field in the contacts table
-- to allow multiple contacts with the same email address as long as they have different owners.

-- Start transaction
BEGIN;

-- Drop the unique constraint on the email field
ALTER TABLE contacts DROP CONSTRAINT IF EXISTS contacts_email_key;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'contacts' AND column_name = 'email';
