-- Migration: Add paid_participants to events table
-- Description: This migration adds a paid_participants column to the events table to store user IDs of participants who have paid

-- Start transaction
BEGIN;

-- Add paid_participants column to events table as an array of integers
ALTER TABLE events ADD COLUMN paid_participants INTEGER[] DEFAULT '{}';

-- Create an index on the paid_participants column for better performance
CREATE INDEX idx_events_paid_participants ON events USING GIN (paid_participants);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'paid_participants';
