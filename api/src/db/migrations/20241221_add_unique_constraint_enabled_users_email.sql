-- Migration: Add partial unique constraint for enabled users' emails
-- Description: This migration adds a partial unique index to ensure that only one enabled user
-- can exist with the same email address, while allowing multiple disabled users with the same email.

-- Start transaction
BEGIN;

-- Create a partial unique index on email where enabled = true
-- This ensures that only one enabled user can have the same email address
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_enabled_unique 
ON users (email) 
WHERE enabled = true;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the index was created)
-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'users' AND indexname = 'idx_users_email_enabled_unique';
