-- Migration: Add connect_request_id to notifications table
-- Description: This migration adds a connect_request_id column to the notifications table

-- Start transaction
BEGIN;

-- Add connect_request_id column to notifications table
ALTER TABLE notifications ADD COLUMN connect_request_id INTEGER;

-- Add foreign key constraint
ALTER TABLE notifications 
ADD CONSTRAINT fk_notifications_connect_request 
FOREIGN KEY (connect_request_id) 
REFERENCES connect_requests(id) 
ON DELETE SET NULL;

-- Add index for better performance
CREATE INDEX idx_notifications_connect_request_id ON notifications(connect_request_id);

-- Commit transaction
COMMIT;
