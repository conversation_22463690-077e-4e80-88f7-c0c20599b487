-- Create ConnectRequests table
CREATE TABLE connect_requests (
    id SERIAL PRIMARY KEY,
    owner_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    email VARCHAR(150),
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    phone_number <PERSON><PERSON><PERSON><PERSON>(15),
    is_accepted BOOLEAN NOT NULL DEFAULT FALSE,
    is_declined BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX connect_requests_owner_id_idx ON connect_requests(owner_id);
CREATE INDEX connect_requests_user_id_idx ON connect_requests(user_id);
CREATE INDEX connect_requests_email_idx ON connect_requests(email);
