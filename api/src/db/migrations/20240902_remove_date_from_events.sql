-- Migration: Remove date column from events table
-- Description: This migration removes the date column from the events table as it's being replaced by the event_dates table

-- Start transaction
BEGIN;

-- Remove the date column from events table
ALTER TABLE events DROP COLUMN date;

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events';
