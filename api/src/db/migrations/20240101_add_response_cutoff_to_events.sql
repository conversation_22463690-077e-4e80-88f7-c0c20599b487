-- Migration: Add response_cutoff to events table
-- Description: This migration adds a response_cutoff column to the events table to store the number of hours before an event when users can no longer sign up

-- Start transaction
BEGIN;

-- Add response_cutoff column to events table
ALTER TABLE events ADD COLUMN response_cutoff INTEGER;

-- Add comment to the column
COMMENT ON COLUMN events.response_cutoff IS 'Number of hours before the event when users can no longer sign up';

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'response_cutoff';
