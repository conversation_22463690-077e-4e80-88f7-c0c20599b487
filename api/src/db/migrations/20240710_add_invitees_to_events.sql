-- Migration: Add invitees to events table
-- Description: This migration adds an invitees column to the events table to store user IDs of invited users

-- Start transaction
BEGIN;

-- Add invitees column to events table as a JSON array
ALTER TABLE events ADD COLUMN invitees JSONB DEFAULT '[]'::jsonb;

-- Create an index on the invitees column for better performance
CREATE INDEX idx_events_invitees ON events USING GIN (invitees);

-- Commit transaction
COMMIT;
