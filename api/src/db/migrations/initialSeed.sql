CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    display_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    email VARCHAR(150) NOT NULL UNIQUE,
    password_hash TEXT,
    access_level VARCHAR(20) NOT NULL DEFAULT 'user',
    provider VARCHAR(20) NOT NULL,
    provider_user_id VARCHAR(250) NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    location JSONB,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    auto_locate BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Contacts table
CREATE TABLE contacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100), -- Optional field
    email VARCHAR(150) UNIQUE, -- Optional field
    phone_number VA<PERSON><PERSON><PERSON>(15),
    owner_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    is_priority BOOLEAN DEFAULT FALSE,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create UserPreferences table
CREATE TABLE user_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    notifications_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    language VARCHAR(10) DEFAULT 'en',
    push_notifications_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    notify_event_cancellation BOOLEAN NOT NULL DEFAULT TRUE,
    notify_event_changes BOOLEAN NOT NULL DEFAULT FALSE,
    notify_contact_questions BOOLEAN NOT NULL DEFAULT TRUE,
    notify_top_tier_events BOOLEAN NOT NULL DEFAULT TRUE,
    email_notifications_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    participant_reminders_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    notify_dues_owed BOOLEAN NOT NULL DEFAULT TRUE,
    notify_tickets_not_purchased BOOLEAN NOT NULL DEFAULT FALSE,
    venmo_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    paypal_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Sessions table
CREATE TABLE sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    user_agent VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Notifications table
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    sender_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    receiver_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subject VARCHAR(255) NOT NULL,
    notice_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Groups table
CREATE TABLE contact_groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    owner_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Events table
CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    date TIMESTAMP[] NOT NULL DEFAULT '{}', -- Array of timestamps instead of a string
    location VARCHAR(255) DEFAULT '', -- Added as separate field from address
    address VARCHAR(255) NOT NULL,
    owner VARCHAR(100) NOT NULL,
    owner_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    image_url VARCHAR(255) NOT NULL,
    has_options BOOLEAN NOT NULL DEFAULT FALSE,
    quorum_met BOOLEAN NOT NULL DEFAULT FALSE,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);