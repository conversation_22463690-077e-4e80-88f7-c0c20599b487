-- Migration: Add email_verified field to users table
-- Date: 2025-01-30
-- Description: Add email verification functionality to the users table

-- Add email_verified column to users table
-- Default to TRUE for existing users to avoid breaking current functionality
-- New users will have this set to FALSE and need to verify their email
ALTER TABLE users 
ADD COLUMN email_verified BOOLEAN DEFAULT TRUE NOT NULL;

-- For new users going forward, we want email_verified to default to FALSE
-- But we keep existing users as verified to avoid disruption
-- The application logic will handle setting this to FALSE for new registrations

-- Add index for performance when querying by email verification status
CREATE INDEX idx_users_email_verified ON users(email_verified);

-- Add a comment to document the field
COMMENT ON COLUMN users.email_verified IS 'Indicates whether the user has verified their email address. Defaults to TRUE for existing users, FALSE for new registrations.';
