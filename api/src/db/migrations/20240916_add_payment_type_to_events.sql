-- Migration: Add payment_type column to events table
-- Description: This migration adds a payment_type column to the events table to store the payment type selected by the user

-- Start transaction
BEGIN;

-- Add payment_type column to events table
ALTER TABLE events ADD COLUMN payment_type VARCHAR(50);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'payment_type';
