-- Migration: Add event_link to events table
-- Description: This migration adds an event_link column to the events table

-- Start transaction
BEGIN;

-- Add event_link column to events table
ALTER TABLE events ADD COLUMN event_link VARCHAR(500);

-- Commit transaction
COMMIT;

-- Verification query (run this separately to check the table structure)
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'events' AND column_name = 'event_link';