interface EventInfo {
  image_url: string | null;
  name: string | null;
  dates: string[];
  location: string | null;
  description: string | null;
  cost: number | null;
  costCurrency?: string | null;
}

export const LINK_EXTRACTOR_SYSTEM_PROMPT = `
  You are a specialized event information extractor. Your task is to analyze webpage content and extract structured event details.

  Guidelines:
  - Extract information with high confidence only
  - Prefer structured data (JSON-LD, meta tags) over raw content when available
  - For dates, convert all formats to ISO 8601 UTC (e.g., "2024-03-15T19:00:00Z")
  - For costs, extract numerical values only (e.g., 25.00, not "$25")
  - For images, prefer high-resolution event images over logos or thumbnails
  - Maintain original spelling and formatting of names and locations
  - Format location as a clean string without extra commas or unnecessary punctuation
  - If information is ambiguous or unclear, use null

  Response must be valid JSON matching this structure:
  {
    "image_url": string | null,    // Full URL to event image
    "name": string | null,         // Event title/name
    "dates": string[],             // Array of ISO 8601 UTC dates
    "location": string | null,     // Full venue details
    "description": string | null,  // Brief event description
    "cost": number | null,         // Numerical price value
    "costCurrency": string | null  // 3-letter currency code (USD, EUR, etc.)
  }

  No MD syntax.

  If any field cannot be determined, use null. For dates, provide an empty array if no dates are found.
  All dates must be in ISO 8601 format with timezone (prefer UTC/Z).
  Cost must be a number (not a string) or null.
`;

export const LINK_EXTRACTOR_USER_PROMPT = (link: string, scrapedData?: {
  meta: Record<string, string>,
  jsonLd: any[],
  title: string,
  headings: string,
  prices: string,
  mainContent: string
}) => {
  // Truncate function that preserves word boundaries
  const truncate = (str: string, maxLength: number) => {
    if (str.length <= maxLength) return str;
    return str.substr(0, str.lastIndexOf(' ', maxLength)) + '...';
  };

  // Process scraped data with truncation
  const processedData = {
    title: truncate(scrapedData?.title || '', 500),
    headings: truncate(scrapedData?.headings || '', 500),
    prices: truncate(scrapedData?.prices || '', 500),
    meta: Object.fromEntries(
      Object.entries(scrapedData?.meta || {}).map(([k, v]) => [k, truncate(v, 2500)])
    ),
    jsonLd: scrapedData?.jsonLd || []
  };

  return `
    Analyze this event URL: ${truncate(link, 1000)}

    Scraped webpage data:
    ${JSON.stringify(processedData, null, 2)}

    Main content excerpt:
    ${truncate(scrapedData?.mainContent || '', 1000)}
    Analyze this event URL: ${link}


    Extract the event information and return it as JSON. Prioritize:
    1. Structured data (JSON-LD)
    2. Meta tags
    3. Main content and headings
    4. URL structure

    Remember:
    - All dates must be in ISO 8601 UTC format
    - Costs must be numbers without currency symbols
    - Use null for any uncertain or missing fields
    - Include currency code in costCurrency field if available
    - Dates array should be empty if no dates found
    - Format location as a clean string without extra commas, trailing commas, or unnecessary punctuation

    Please analyze the provided scraped data and enhance it where possible. If the scraped data is incomplete or missing, try to extract additional information from the link.

    Return the complete event information in JSON format. Use the scraped data as a base and only modify/add information if you're confident about the changes.

    No MD syntax.

    Example response:
    {
      "image_url": "https://example.com/image.jpg",
      "name": "Taylor Swift | The Eras Tour",
      "dates": ["2024-08-15T19:00:00Z", "2024-08-16T19:00:00Z"],
      "location": "Gillette Stadium, Foxborough, MA",
      "description": "Taylor Swift brings her record-breaking Eras Tour to Gillette Stadium",
      "cost": 49.99,
      "costCurrency": "USD"
    }

    For location, provide a clean string without extra commas. Examples of good location formats:
    - "Gillette Stadium, Foxborough, MA"
    - "Central Park, New York City"
    - "Virtual Event"
    - "123 Main Street, Suite 456, Chicago, IL 60601"

    If you can't find certain information, use null for that particular field, otherwise populate it:
    {
      "image_url": null,
      "name": "Local Meetup",
      "dates": [],
      "location": null,
      "description": null,
      "cost": null,
      "costCurrency": null
    }
  `
};

export type { EventInfo };
