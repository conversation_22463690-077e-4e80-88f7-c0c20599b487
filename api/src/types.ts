import { Request } from 'express';

export type AuthProvider = 'google' | 'facebook';
export type AccessLevel = 'admin' | 'user' | 'editor' | 'viewer';
export type NoticeType = 
  'connection_request' | 
  'event_cancellation' | 
  'event_update' | 
  'payment_due' | 
  'payment_received' | 
  'event_quorum_reached' | 
  'event_date_confirmed' | 
  'new_comment';

export interface User {
  id: number;
  display_name: string | null;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone_number: string | null;
  password_hash: string | null;
  access_level: AccessLevel;
  provider: AuthProvider;
  provider_user_id: string;
  access_token: string | null;
  refresh_token: string | null;
  location: any | null; // JSONB
  enabled: boolean;
  auto_locate: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface JwtPayload {
  id: number;
  email: string;
  access_level: AccessLevel;
  iat?: number;
  exp?: number;
}

export interface AuthRequest extends Request {
  user?: JwtPayload;
}
