import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { JwtPayload, AccessLevel } from '../types';

export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  // Allow internal requests to bypass authentication
  const isInternalRequest = req.headers['x-internal-request'] === 'true';
  if (isInternalRequest) {
    console.log('Internal request detected, bypassing authentication');
    // Set a default user for internal requests
    (req as any).user = { id: 0, email: 'internal@system', access_level: 'admin' };
    next();
    return;
  }

  // Get token from Authorization header
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    res.status(401).json({ message: 'Authentication token required' });
    return;
  }

  try {
    const secret = process.env.JWT_SECRET || 'fallback_secret';
    const decoded = jwt.verify(token, secret) as JwtPayload;
    (req as any).user = decoded;
    next();
  } catch (err) {
    res.status(403).json({ message: 'Invalid or expired token' });
  }
};

export const checkRole = (roles: AccessLevel[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as any;

    if (!authReq.user) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    if (!roles.includes(authReq.user.access_level)) {
      res.status(403).json({ message: 'Not authorized for this action' });
      return;
    }

    next();
  };
};
