import { Request, Response, NextFunction } from 'express';
import axios from 'axios';
import * as cheerio from 'cheerio';
import dotenv from 'dotenv';
import {
  LINK_EXTRACTOR_SYSTEM_PROMPT,
  LINK_EXTRACTOR_USER_PROMPT,
  type EventInfo
} from '../prompts/linkExtractor';

dotenv.config();
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

async function scrapeEventDetails(url: string): Promise<{
  meta: Record<string, string>,
  jsonLd: any[],
  pageContent: {
    title: string,
    mainContent: string,
    headings: string[],
    prices: string[]
  }
}> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      },
      timeout: 10000,
      validateStatus: (status) => status < 400
    });

    if (!response?.data) {
      throw new Error('No HTML content received');
    }

    const html = typeof response.data === 'string' ? response.data : response.data.toString();

    // Initialize cheerio with the HTML content
    const $ = cheerio.load(html);

    // Collect all meta tags
    const meta: Record<string, string> = {};
    $('meta').each((_: any, element: any) => {
      const property = $(element).attr('property') || $(element).attr('name');
      const content = $(element).attr('content');
      if (property && content) {
        meta[property] = content;
      }
    });

    // Collect all LD+JSON data
    const jsonLdData: any[] = [];
    $('script[type="application/ld+json"]').each((_: any, el: any) => {
      try {
        const json = JSON.parse($(el).html() || '');
        jsonLdData.push(json);
      } catch (e) {
        console.error('Error parsing JSON-LD:', e);
      }
    });

    // Extract page content
    const pageContent = {
      title: $('title').text().trim(),
      mainContent: '',
      headings: [] as string[],
      prices: [] as string[]
    };

    // Get main content (prioritize common content containers)
    const mainContentSelectors = [
      'main',
      '[role="main"]',
      '#main-content',
      '.main-content',
      'article',
      '.content',
      '#content'
    ];

    for (const selector of mainContentSelectors) {
      const content = $(selector).text().trim();
      if (content) {
        pageContent.mainContent = content;
        break;
      }
    }

    // If no main content found, get body text
    if (!pageContent.mainContent) {
      pageContent.mainContent = $('body').text().trim();
    }

    // Get all headings
    pageContent.headings = [];
    $('h1, h2, h3').each((_, el) => {
      const headingText = $(el).text().trim();
      if (headingText) {
        pageContent.headings.push(headingText);
      }
    });

    // Extract potential price information
    pageContent.prices = [];
    $('*').each((_, el) => {
      const text = $(el).text().trim();
      // Match common price patterns (e.g., $XX.XX, XX.XX USD)
      const priceMatches = text.match(/\$?\d+(\.\d{2})?\s*(USD|EUR|GBP)?/g);
      if (priceMatches) {
        pageContent.prices.push(...priceMatches);
      }
    });

    // Clean up the main content
    pageContent.mainContent = pageContent.mainContent
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n+/g, '\n') // Replace multiple newlines with single newline
      .trim();

    return { meta, jsonLd: jsonLdData, pageContent };
  } catch (error) {
    console.error("Error scraping event:", error);
    return {
      meta: {},
      jsonLd: [],
      pageContent: {
        title: '',
        mainContent: '',
        headings: [],
        prices: []
      }
    };
  }
}

export const extractEventFromLink = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    const { link } = req.body;

    if (!link) {
      res.status(400).json({ message: 'Link is required' });
      return;
    }

    // First try to scrape the event details
    const scrapedData = await scrapeEventDetails(link);

    if (!OPENAI_API_KEY) {
      console.error('OpenAI API key is not configured');
      res.status(500).json({ message: 'OpenAI API key is not configured' });
      return;
    }

    // Prepare a summary of the scraped data for the AI
    const scrapedSummary = {
      meta: scrapedData.meta,
      jsonLd: scrapedData.jsonLd,
      title: scrapedData.pageContent.title,
      headings: scrapedData.pageContent.headings.join('\n'),
      prices: scrapedData.pageContent.prices.join(', '),
      mainContent: scrapedData.pageContent.mainContent.substring(0, 2000) // Limit content length
    };

    const apiResponse = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: LINK_EXTRACTOR_SYSTEM_PROMPT
          },
          {
            role: 'user',
            content: LINK_EXTRACTOR_USER_PROMPT(link, scrapedSummary)
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        }
      }
    );

    const content = apiResponse.data.choices[0]?.message?.content;

    if (!content) {
      res.status(500).json({ message: 'No content received from OpenAI' });
      return;
    }

    console.log('Content from OpenAI:', content);
    // console.log('Scraped data:', scrapedSummary);

    const eventInfo = JSON.parse(content);

    // Helper function to clean up location string
    const cleanLocationString = (location: string | null): string | null => {
      if (!location) return null;

      // Remove extra commas and spaces
      return location
        .replace(/\s*,\s*,+\s*/g, ', ') // Replace multiple commas with a single comma
        .replace(/^\s*,\s*|\s*,\s*$/g, '') // Remove commas at the beginning or end
        .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
        .trim();
    };

    // Validate the event info from AI
    const validatedEventInfo: EventInfo = {
      image_url: typeof eventInfo.image_url === 'string' ? eventInfo.image_url : null,
      name: typeof eventInfo.name === 'string' ? eventInfo.name : null,
      dates: Array.isArray(eventInfo.dates) ? eventInfo.dates : [],
      location: typeof eventInfo.location === 'string' ? cleanLocationString(eventInfo.location) : null,
      description: typeof eventInfo.description === 'string' ? eventInfo.description : null,
      cost: typeof eventInfo.cost === 'number' ? eventInfo.cost : null,
      costCurrency: typeof eventInfo.costCurrency === 'string' ? eventInfo.costCurrency : null
    };

    res.json(validatedEventInfo);
  } catch (error) {
    console.error('Error extracting event from link:', error);
    next(error);
  }
};
