import { Request, Response, NextFunction } from 'express';
import Event from '../models/Event';
import EventDate from '../models/EventDate';
import User from '../models/User';
import { AuthRequest } from '../types';
import { Op } from 'sequelize';
import notificationUtils from '../utils/notificationUtils';
import { sendEventInvitationEmailsToAddresses } from '../utils/emailNotificationUtils';

// Get all events
export const getAllEvents = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get the user ID from the authenticated request
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }



    // Find events where the user is either the owner or in the invitees array
    const events = await Event.findAll({
      where: {
        [Op.or]: [
          { owner_id: userId },
          { invitees: { [Op.contains]: [userId] } }
        ]
      },
      include: [{
        model: EventDate,
        as: 'eventDates',
        attributes: ['id', 'date', 'participants']
      }]
    });



    // Add user information for each participant
    for (const event of events) {
      // Ensure participants is initialized
      if (!event.participants) {
        event.participants = [];
      }

      // Ensure invitees is initialized
      if (!event.invitees) {
        event.invitees = [];
      }

      // Convert event dates to the format expected by the frontend
      const eventJSON: any = event.toJSON();

      // Make sure eventDates is properly mapped to date array
      if (eventJSON.eventDates && eventJSON.eventDates.length > 0) {
        eventJSON.date = eventJSON.eventDates.map((eventDate: any) => eventDate.date);

      } else {
        eventJSON.date = [];

      }

      // Replace the event with the modified JSON
      Object.assign(event, eventJSON);
    }

    res.json(events);
  } catch (error) {
    console.error('Error getting all events:', error);
    next(error);
  }
};

// Get event by ID
export const getEventById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Find the event by ID and include its dates
    const event = await Event.findByPk(req.params.id, {
      include: [{
        model: EventDate,
        as: 'eventDates',
        attributes: ['id', 'date', 'participants']
      }]
    });

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Ensure participants is initialized
    if (!event.participants) {
      event.participants = [];
    }

    // Ensure invitees is initialized
    if (!event.invitees) {
      event.invitees = [];
    }

    // Fetch user details for participants
    const participantIds = event.participants;
    let participantDetails: Array<{
      user_id: number;
      status: string;
      joined_at: string;
      user?: {
        id: number;
        display_name: string;
        first_name?: string;
        last_name?: string;
        email: string;
      };
    }> = [];

    if (participantIds.length > 0) {
      // Fetch user details for each participant
      const users = await User.findAll({
        where: { id: participantIds },
        attributes: ['id', 'display_name', 'first_name', 'last_name', 'email', 'profile_image_url']
      });

      // Map user IDs to user objects
      participantDetails = participantIds.map(userId => {
        const user = users.find(u => u.id === userId);
        return {
          user_id: userId,
          status: 'going', // Default status
          joined_at: new Date().toISOString(),
          user: user ? {
            id: user.id,
            display_name: user.display_name,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            profile_image_url: user.profile_image_url
          } : undefined
        };
      });
    }

    // Fetch user details for invitees
    const inviteeIds = event.invitees;
    let inviteeDetails: Array<{
      user_id: number;
      user?: {
        id: number;
        display_name: string;
        first_name?: string;
        last_name?: string;
        email: string;
      };
    }> = [];

    if (inviteeIds.length > 0) {
      // Fetch user details for each invitee
      const inviteeUsers = await User.findAll({
        where: { id: inviteeIds },
        attributes: ['id', 'display_name', 'first_name', 'last_name', 'email', 'profile_image_url']
      });

      // Map invitee user IDs to user objects
      inviteeDetails = inviteeIds.map(userId => {
        const user = inviteeUsers.find(u => u.id === userId);
        return {
          user_id: userId,
          user: user ? {
            id: user.id,
            display_name: user.display_name,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            profile_image_url: user.profile_image_url
          } : undefined
        };
      });
    }

    // Convert event to JSON and add participant details
    const eventJSON: any = event.toJSON();

    // Convert event dates to the format expected by the frontend
    if (eventJSON.eventDates && eventJSON.eventDates.length > 0) {
      eventJSON.date = eventJSON.eventDates.map((eventDate: any) => eventDate.date);

    } else {
      eventJSON.date = [];

    }

    // Add participant details and invitee details
    eventJSON.participantDetails = participantDetails;
    eventJSON.inviteeDetails = inviteeDetails;

    res.json(eventJSON);
  } catch (error) {
    console.error(`Error getting event by ID ${req.params.id}:`, error);
    next(error);
  }
};

// Create a new event
export const createEvent = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Log the request body for debugging






    // Ensure invitees are integers
    if (req.body.invitees && Array.isArray(req.body.invitees)) {
      req.body.invitees = req.body.invitees.map((id: string | number) => parseInt(id.toString()));

    }
    const {
      name,
      date,
      location = '', // Provide a default empty string if location is not provided
      address,
      // New address fields
      location_name,
      address_line1,
      address_line2,
      city,
      state,
      zip,
      meeting_point,
      owner,
      owner_id,
      image_url,
      event_link, // Add this field
      has_options = false,
      quorum_met = false,
      enabled = true,
      cost,
      cost_purpose,
      payment_type,
      description, // Add this field
      invitees = [],
      quorum = 1,
      max_participants,
      multiple_dates = false, // Whether users can select multiple dates
      response_cutoff
    } = req.body;

    // Process date field - convert to array of Date objects if it's not already
    let dateArray: Date[] = [];





    if (Array.isArray(date)) {
      // If date is already an array, convert each item to a Date object
      dateArray = date.map(d => {
        if (d instanceof Date) return d;
        // Try to parse the date string
        const parsedDate = new Date(d);

        return parsedDate;
      }).filter(d => !isNaN(d.getTime())); // Filter out invalid dates

    } else if (typeof date === 'string') {
      // If date is a string, try to parse it
      // Check if it contains 'OR' which indicates multiple dates
      if (date.includes('OR')) {
        // Split by 'OR' and trim each part
        const dateParts = date.split('OR').map(part => part.trim());
        dateArray = dateParts.map(d => {
          const parsedDate = new Date(d);

          return parsedDate;
        }).filter(d => !isNaN(d.getTime()));

      } else {
        // Single date string
        const parsedDate = new Date(date);

        if (!isNaN(parsedDate.getTime())) {
          dateArray = [parsedDate];
        }
      }
    }

    // Ensure we have at least one date
    if (dateArray.length === 0) {
      dateArray = [new Date()];
    }

    // Initialize participants array with the owner_id
    const initialParticipants = owner_id ? [owner_id] : [];

    // Log the payment_type before creating the event


    // Create the event without dates first
    const newEvent = await Event.create({
      name,
      location: (() => {
        // Helper function to check if a string is not empty
        const isNotEmpty = (str?: string) => str && str.trim() !== '';

        // Use location_name if provided and not empty, otherwise use location if not empty
        if (isNotEmpty(location_name)) return location_name;
        if (isNotEmpty(location)) return location;
        return '';
      })(), // Ensure empty string if both are empty
      address: (() => {
        // Helper function to check if a string is not empty
        const isNotEmpty = (str?: string) => str && str.trim() !== '';

        // Check if we have any non-empty address data
        const hasAddressData = isNotEmpty(address_line1) || isNotEmpty(city) || isNotEmpty(state);

        if (!hasAddressData) {
          return address && address.trim() !== '' ? address : '';
        }

        // Create city-state-zip part only if we have non-empty city or state
        let cityStateZip = '';
        if (isNotEmpty(city) || isNotEmpty(state)) {
          cityStateZip = [
            isNotEmpty(city) ? city : null,
            isNotEmpty(state) ? (isNotEmpty(city) ? state : state) : null,
            isNotEmpty(zip) ? zip : null
          ].filter(Boolean).join(' ');
        }

        // Combine all parts, filtering out empty strings
        return [
          isNotEmpty(address_line1) ? address_line1 : null,
          isNotEmpty(address_line2) ? address_line2 : null,
          cityStateZip !== '' ? cityStateZip : null
        ].filter(Boolean).join(', ');
      })(), // Format address from components if available, ensure empty string if address is empty
      // New address fields
      location_name,
      address_line1,
      address_line2,
      city,
      state,
      zip,
      meeting_point,
      owner,
      owner_id,
      image_url: image_url || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png',
      event_link,
      has_options,
      quorum_met,
      enabled,
      cost,
      cost_purpose,
      payment_type,
      description, // Add this field
      participants: initialParticipants,
      participants_count: initialParticipants.length, // Will be 1 if owner_id exists, 0 otherwise
      invitees: Array.isArray(invitees) ? invitees : [],
      paid_participants: [], // Initialize with empty array
      quorum,
      max_participants,
      multiple_dates, // Whether users can select multiple dates
      response_cutoff, // Add response cutoff field
      created_at: new Date(),
      updated_at: new Date()
    });

    // Create event dates for each date in the array
    // Filter out any invalid dates first
    const validDates = dateArray.filter(date => date && !isNaN(date.getTime()));



    if (validDates.length === 0) {

      validDates.push(new Date());
    }

    const eventDates = await Promise.all(validDates.map(date => {


      return EventDate.create({
        event_id: newEvent.id,
        date,
        participants: initialParticipants, // Initialize with the same participants as the event
        created_at: new Date(),
        updated_at: new Date()
      });
    }));

    // Fetch the event with its dates to return in the response
    const eventWithDates = await Event.findByPk(newEvent.id, {
      include: [{
        model: EventDate,
        as: 'eventDates',
        attributes: ['id', 'date', 'participants']
      }]
    });

    // Convert event to JSON and add dates in the expected format
    const eventJSON: any = eventWithDates?.toJSON() || newEvent.toJSON();
    eventJSON.date = eventDates.map(eventDate => eventDate.date);

    // Log the created event to verify payment_type was saved


    res.status(201).json(eventJSON);
  } catch (error) {
    console.error('Error creating event:', error);
    next(error);
  }
};

// Update an event
export const updateEvent = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log("=======REQ BODY UPDATE EVENT=======");
    console.log(req.body);
    console.log("=================================");

    // Validation for partial updates
    const validationErrors: string[] = [];

    // Validate numeric fields if provided
    if ('quorum' in req.body) {
      const quorum = parseInt(req.body.quorum);
      if (isNaN(quorum) || quorum < 1) {
        validationErrors.push('Quorum must be a positive integer');
      }
    }

    if ('max_participants' in req.body && req.body.max_participants !== null) {
      const maxParticipants = parseInt(req.body.max_participants);
      if (isNaN(maxParticipants) || maxParticipants < 1) {
        validationErrors.push('Max participants must be a positive integer or null');
      }
    }

    if ('cost' in req.body && req.body.cost !== null && req.body.cost !== undefined) {
      const cost = parseFloat(req.body.cost);
      if (isNaN(cost) || cost < 0) {
        validationErrors.push('Cost must be a non-negative number');
      }
    }

    if ('response_cutoff' in req.body && req.body.response_cutoff !== null && req.body.response_cutoff !== undefined) {
      const cutoff = parseInt(req.body.response_cutoff);
      if (isNaN(cutoff) || cutoff < 0) {
        validationErrors.push('Response cutoff must be a non-negative integer');
      }
    }

    // Validate string fields if provided
    if ('name' in req.body && (!req.body.name || req.body.name.trim() === '')) {
      validationErrors.push('Event name cannot be empty');
    }

    if ('payment_type' in req.body && req.body.payment_type) {
      const validPaymentTypes = ['pay_me_back', 'chip_in', 'buy_ticket', 'bring_wallet'];
      if (!validPaymentTypes.includes(req.body.payment_type)) {
        validationErrors.push('Invalid payment type');
      }
    }

    // Validate boolean fields if provided
    const booleanFields = ['has_options', 'quorum_met', 'enabled', 'multiple_dates'];
    booleanFields.forEach(field => {
      if (field in req.body && typeof req.body[field] !== 'boolean') {
        validationErrors.push(`${field} must be a boolean value`);
      }
    });

    // Return validation errors if any
    if (validationErrors.length > 0) {
      res.status(400).json({
        message: 'Validation failed',
        errors: validationErrors
      });
      return;
    }

    // Process invitees if provided
    if (req.body.invitees && Array.isArray(req.body.invitees)) {
      req.body.invitees = req.body.invitees.map((id: string | number) => parseInt(id.toString()));
    }

    // Get the existing event for additional validation
    const existingEvent = await Event.findByPk(req.params.id);
    if (!existingEvent) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Additional business logic validation
    if ('quorum' in req.body && 'max_participants' in req.body) {
      const quorum = parseInt(req.body.quorum);
      const maxParticipants = req.body.max_participants ? parseInt(req.body.max_participants) : null;
      if (maxParticipants && quorum > maxParticipants) {
        validationErrors.push('Quorum cannot be greater than max participants');
      }
    } else if ('quorum' in req.body) {
      const quorum = parseInt(req.body.quorum);
      if (existingEvent.max_participants && quorum > existingEvent.max_participants) {
        validationErrors.push('Quorum cannot be greater than existing max participants');
      }
    } else if ('max_participants' in req.body) {
      const maxParticipants = req.body.max_participants ? parseInt(req.body.max_participants) : null;
      if (maxParticipants && existingEvent.quorum && existingEvent.quorum > maxParticipants) {
        validationErrors.push('Max participants cannot be less than existing quorum');
      }
    }

    // Check if trying to modify dates when quorum is met
    if ('date' in req.body && existingEvent.quorum_met) {
      validationErrors.push('Cannot modify dates when quorum is already met');
    }

    // Return additional validation errors if any
    if (validationErrors.length > 0) {
      res.status(400).json({
        message: 'Validation failed',
        errors: validationErrors
      });
      return;
    }

    // Build update object with only provided fields
    const updateFields: any = {};

    // Only add fields that are actually present in the request body
    if ('name' in req.body) updateFields.name = req.body.name;
    if ('address' in req.body) updateFields.address = req.body.address;
    if ('owner' in req.body) updateFields.owner = req.body.owner;
    if ('owner_id' in req.body) updateFields.owner_id = req.body.owner_id;
    if ('image_url' in req.body) updateFields.image_url = req.body.image_url;
    if ('event_link' in req.body) updateFields.event_link = req.body.event_link;
    if ('has_options' in req.body) updateFields.has_options = req.body.has_options;
    if ('quorum_met' in req.body) updateFields.quorum_met = req.body.quorum_met;
    if ('enabled' in req.body) updateFields.enabled = req.body.enabled;
    if ('cost' in req.body) updateFields.cost = req.body.cost;
    if ('cost_purpose' in req.body) updateFields.cost_purpose = req.body.cost_purpose;
    if ('payment_type' in req.body) updateFields.payment_type = req.body.payment_type;
    if ('description' in req.body) updateFields.description = req.body.description;
    if ('invitees' in req.body) updateFields.invitees = Array.isArray(req.body.invitees) ? req.body.invitees : [];
    if ('quorum' in req.body) updateFields.quorum = req.body.quorum;
    if ('max_participants' in req.body) updateFields.max_participants = req.body.max_participants;
    if ('multiple_dates' in req.body) updateFields.multiple_dates = req.body.multiple_dates;
    if ('response_cutoff' in req.body) updateFields.response_cutoff = req.body.response_cutoff;

    // Handle address fields
    if ('location_name' in req.body) updateFields.location_name = req.body.location_name;
    if ('address_line1' in req.body) updateFields.address_line1 = req.body.address_line1;
    if ('address_line2' in req.body) updateFields.address_line2 = req.body.address_line2;
    if ('city' in req.body) updateFields.city = req.body.city;
    if ('state' in req.body) updateFields.state = req.body.state;
    if ('zip' in req.body) updateFields.zip = req.body.zip;
    if ('meeting_point' in req.body) updateFields.meeting_point = req.body.meeting_point;

    // Handle location field with special logic
    if ('location' in req.body || 'location_name' in req.body) {
      const isNotEmpty = (str?: string) => str && str.trim() !== '';
      const location_name = req.body.location_name;
      const location = req.body.location;

      // Use location_name if provided and not empty, otherwise use location if not empty
      if (isNotEmpty(location_name)) {
        updateFields.location = location_name;
      } else if (isNotEmpty(location)) {
        updateFields.location = location;
      } else if ('location' in req.body || 'location_name' in req.body) {
        updateFields.location = '';
      }
    }

    // Process date field only if provided - convert to array of Date objects
    let dateArray: Date[] | undefined;

    if ('date' in req.body && req.body.date) {
      const date = req.body.date;
      dateArray = [];

      if (Array.isArray(date)) {
        // If date is already an array, convert each item to a Date object
        dateArray = date.map((d: any) => {
          if (d instanceof Date) return d;
          const parsedDate = new Date(d);
          return parsedDate;
        }).filter((d: Date) => !isNaN(d.getTime())); // Filter out invalid dates
      } else if (typeof date === 'string') {
        // If date is a string, try to parse it
        // Check if it contains 'OR' which indicates multiple dates
        if (date.includes('OR')) {
          // Split by 'OR' and trim each part
          const dateParts = date.split('OR').map(part => part.trim());
          dateArray = dateParts.map((d: string) => {
            const parsedDate = new Date(d);
            return parsedDate;
          }).filter((d: Date) => !isNaN(d.getTime()));
        } else {
          // Single date string
          const parsedDate = new Date(date);
          if (!isNaN(parsedDate.getTime())) {
            dateArray = [parsedDate];
          }
        }
      }

      // Ensure we have at least one date if date was provided
      if (dateArray.length === 0) {
        dateArray = [new Date()];
      }
    }

    // Handle special address formatting if address components are provided
    if ('address_line1' in req.body || 'address_line2' in req.body || 'city' in req.body || 'state' in req.body || 'zip' in req.body) {
      const isNotEmpty = (str?: string) => str && str.trim() !== '';
      const address_line1 = req.body.address_line1;
      const address_line2 = req.body.address_line2;
      const city = req.body.city;
      const state = req.body.state;
      const zip = req.body.zip;

      // Check if we have any non-empty address data
      const hasAddressData = isNotEmpty(address_line1) || isNotEmpty(city) || isNotEmpty(state);

      if (hasAddressData) {
        // Create city-state-zip part only if we have non-empty city or state
        let cityStateZip = '';
        if (isNotEmpty(city) || isNotEmpty(state)) {
          cityStateZip = [
            isNotEmpty(city) ? city : null,
            isNotEmpty(state) ? (isNotEmpty(city) ? state : state) : null,
            isNotEmpty(zip) ? zip : null
          ].filter(Boolean).join(' ');
        }

        // Combine all parts, filtering out empty strings
        updateFields.address = [
          isNotEmpty(address_line1) ? address_line1 : null,
          isNotEmpty(address_line2) ? address_line2 : null,
          cityStateZip !== '' ? cityStateZip : null
        ].filter(Boolean).join(', ');
      } else if ('address' in req.body) {
        // Use the provided address field if no components are provided
        updateFields.address = req.body.address || '';
      }
    }

    // Always set updated_at when updating
    updateFields.updated_at = new Date();

    // Update the event without dates
    const [updatedCount, updatedEvents] = await Event.update(updateFields, {
      where: { id: req.params.id },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    const eventId = parseInt(req.params.id);

    // If dates were provided, update the event dates
    if (dateArray && dateArray.length > 0) {
      // Get existing event dates
      const existingEventDates = await EventDate.findAll({
        where: { event_id: eventId }
      });

      // Delete existing event dates that are not in the new array
      const existingDates = existingEventDates.map(ed => ed.date.getTime());
      const newDates = dateArray.map(d => d.getTime());

      // Find dates to delete (in existing but not in new)
      const datesToDelete = existingEventDates.filter(ed => !newDates.includes(ed.date.getTime()));
      if (datesToDelete.length > 0) {
        await EventDate.destroy({
          where: {
            id: { [Op.in]: datesToDelete.map(d => d.id) }
          }
        });
      }

      // Find dates to add (in new but not in existing)
      // Filter out any invalid dates first
      const validDates = dateArray.filter(date => date && !isNaN(date.getTime()));
      const datesToAdd = validDates.filter(d => !existingDates.includes(d.getTime()));
      if (datesToAdd.length > 0) {
        // Get the event to get its participants
        const event = await Event.findByPk(eventId);
        const participants = event?.participants || [];

        // Create new event dates
        await Promise.all(datesToAdd.map(date => {
          return EventDate.create({
            event_id: eventId,
            date,
            participants, // Use the event's participants
            created_at: new Date(),
            updated_at: new Date()
          });
        }));
      }
    }

    // Fetch the updated event with its dates
    const updatedEventWithDates = await Event.findByPk(eventId, {
      include: [{
        model: EventDate,
        as: 'eventDates',
        attributes: ['id', 'date', 'participants']
      }]
    });

    // Convert event to JSON and add dates in the expected format
    const eventJSON: any = updatedEventWithDates?.toJSON() || updatedEvents[0].toJSON();
    eventJSON.date = eventJSON.eventDates?.map((eventDate: any) => eventDate.date) || [];

    res.json(eventJSON);
  } catch (error) {
    console.error(`Error updating event ${req.params.id}:`, error);
    next(error);
  }
};

// Delete an event
export const deleteEvent = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get the event details before deleting
    const event = await Event.findByPk(req.params.id);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Get the current user ID from the auth token
    const currentUserId = req.user?.id;

    if (!currentUserId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Check if the current user is the owner of the event
    if (event.owner_id !== currentUserId) {
      res.status(403).json({ message: 'You are not authorized to delete this event' });
      return;
    }

    // Get the participants of the event to send notifications
    const participants = event.participants || [];

    // Delete the event
    const deletedCount = await Event.destroy({
      where: { id: req.params.id }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Send notifications to all participants about the event cancellation
    if (participants.length > 0) {


      await notificationUtils.createNotificationsWithPush(
        currentUserId, // Current user (owner) is the sender
        participants,
        'Event Cancelled',
        'event_cancellation',
        `"${event.name}" has been cancelled`,
        {
          eventId: parseInt(req.params.id),
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'home',
          params: {}
        }
      );
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting event ${req.params.id}:`, error);
    next(error);
  }
};

// Send event invitation emails to email addresses (for non-users)
export const sendEventInvitationEmailsToEmailAddresses = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.id);
    const { emailAddresses } = req.body;
    const hostId = req.user?.id;

    // Validate inputs
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    if (!hostId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    if (!emailAddresses || !Array.isArray(emailAddresses) || emailAddresses.length === 0) {
      res.status(400).json({ message: 'Email addresses array is required and must not be empty' });
      return;
    }

    // Validate email addresses
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emailAddresses.filter(email => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      res.status(400).json({ message: `Invalid email addresses: ${invalidEmails.join(', ')}` });
      return;
    }

    // Check if event exists
    const event = await Event.findByPk(eventId);
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Check if user is the event owner
    if (event.owner_id !== hostId) {
      res.status(403).json({ message: 'Only the event owner can send invitations' });
      return;
    }

    // Send event invitation emails
    console.log(`Sending event invitation emails for event ${eventId} to ${emailAddresses.length} email addresses`);

    await sendEventInvitationEmailsToAddresses(eventId, hostId, emailAddresses);

    res.status(200).json({
      message: `Event invitation emails sent successfully to ${emailAddresses.length} recipients`,
      emailAddresses: emailAddresses
    });
  } catch (error) {
    console.error(`Error sending event invitation emails for event ${req.params.id}:`, error);
    next(error);
  }
};
