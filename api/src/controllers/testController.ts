import { Request, Response, NextFunction } from 'express';
import emailService from '../services/emailService';
import { loadTemplate } from '../utils/emailTemplateUtils';
import notificationUtils from '../utils/notificationUtils';
import { isS3Url } from '../utils/imageUtils';
import s3SigningService from '../services/s3SigningService';
import { generateNotificationEmail } from '../utils/notificationEmailUtils';
import Event from '../models/Event';
import EventDate from '../models/EventDate';
import Notification from '../models/Notification';
import EmailVerificationToken from '../models/EmailVerificationToken';
import User from '../models/User';
import { formatNotificationDate } from '../utils/dateUtils';
import crypto from 'crypto';

// Deep link scheme for the app
const DEEP_LINK_SCHEME = 'qwrm://';

/**
 * Test the email service with the default template
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testEmailService = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    // Load the default template
    const html = await loadTemplate('default-template', {
      message: 'This is a test email from QWRM.',
      image_url: 'https://via.placeholder.com/120',
      request_url: `${DEEP_LINK_SCHEME}home`,
      app_download_url: 'https://qwrm.app/download'
    });

    // Send the test email
    await emailService.sendEmail(
      email as string,
      'QWRM Test Email',
      html
    );

    res.status(200).json({
      message: 'Test email sent successfully',
      recipient: email
    });
  } catch (error) {
    console.error('Error in test email endpoint:', error);
    next(error);
  }
};

/**
 * Test the notification system with email integration
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testNotificationWithEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { senderId, receiverId } = req.query;

    if (!senderId || !receiverId) {
      res.status(400).json({ message: 'senderId and receiverId parameters are required' });
      return;
    }

    // Create a test notification with push and email
    const notification = await notificationUtils.createNotificationWithPush(
      parseInt(senderId as string),
      parseInt(receiverId as string),
      'Test Notification',
      'new_comment',
      'This is a test notification with email integration.',
      {
        testData: 'This is test data',
        screen: 'home'
      }
    );

    res.status(200).json({
      message: 'Test notification with email sent successfully',
      notification
    });
  } catch (error) {
    console.error('Error in test notification endpoint:', error);
    next(error);
  }
};

/**
 * Test email with different image URLs to debug image loading issues
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testEmailWithImage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email, imageUrl } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    // Use provided image URL or default
    const testImageUrl = imageUrl as string || 'https://via.placeholder.com/120';
    console.log('Test email with image URL:', testImageUrl);

    // Load the default template
    const html = await loadTemplate('default-template', {
      message: 'This is a test email to debug image loading.',
      image_url: testImageUrl,
      request_url: `${DEEP_LINK_SCHEME}home`,
      app_download_url: 'https://qwrm.app/download'
    });

    // Send the test email
    await emailService.sendEmail(
      email as string,
      'QWRM Image Test Email',
      html
    );

    res.status(200).json({
      message: 'Test email with image sent successfully',
      imageUrl: testImageUrl
    });
  } catch (error) {
    console.error('Error sending test email with image:', error);
    next(error);
  }
};

/**
 * Test email with event notification
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testEventNotificationEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email, eventId } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    if (!eventId) {
      res.status(400).json({ message: 'Event ID parameter is required' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(parseInt(eventId as string));
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    console.log('Event image URL:', event.image_url);

    // Create a test notification with event image
    const html = await generateNotificationEmail(
      'Event Notification Test',
      `This is a test notification for event "${event.name}".`,
      'event_update',
      {
        eventId: event.id,
        eventName: event.name,
        eventImageUrl: event.image_url
      }
    );

    // Send the test email
    await emailService.sendEmail(
      email as string,
      'QWRM Event Notification Test',
      html
    );

    res.status(200).json({
      message: 'Test event notification email sent successfully',
      eventId: event.id,
      eventName: event.name,
      eventImageUrl: event.image_url
    });
  } catch (error) {
    console.error('Error sending test event notification email:', error);
    next(error);
  }
};

/**
 * Test date confirmed notification
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testDateConfirmedNotification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { eventId } = req.query;

    if (!eventId) {
      res.status(400).json({ message: 'Event ID parameter is required' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(parseInt(eventId as string));
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Format a date for the notification
    const formattedDate = formatNotificationDate(new Date());

    // Create a test notification
    const notification = await Notification.create({
      sender_id: 1, // Assuming user ID 1 exists
      receiver_id: 1, // Sending to the same user
      subject: 'Date Confirmed',
      notice_type: 'event_date_confirmed',
      message: `The date for "${event.name}" has been confirmed: ${formattedDate}`,
      read: false,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Send email notification
    const html = await generateNotificationEmail(
      'Date Confirmed',
      `The date for "${event.name}" has been confirmed: ${formattedDate}`,
      'event_date_confirmed',
      {
        eventId: event.id,
        eventName: event.name
      }
    );

    res.status(200).json({
      message: 'Test date confirmed notification created successfully',
      notification
    });
  } catch (error) {
    console.error('Error creating test date confirmed notification:', error);
    next(error);
  }
};

/**
 * Test email with logo
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testEmailWithLogo = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    // Load the default template
    const html = await loadTemplate('default-template', {
      message: 'This is a test email to verify the logo is displayed correctly.',
      image_url: 'https://via.placeholder.com/120',
      request_url: `${DEEP_LINK_SCHEME}home`,
      app_download_url: 'https://qwrm.app/download'
    });

    // Send the test email
    await emailService.sendEmail(
      email as string,
      'QWRM Logo Test Email',
      html
    );

    res.status(200).json({
      message: 'Test email with logo sent successfully'
    });
  } catch (error) {
    console.error('Error sending test email with logo:', error);
    next(error);
  }
};

/**
 * Test quorum notification
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testQuorumNotification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, eventId } = req.query;

    if (!userId) {
      res.status(400).json({ message: 'User ID parameter is required' });
      return;
    }

    if (!eventId) {
      res.status(400).json({ message: 'Event ID parameter is required' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(parseInt(eventId as string));
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Send a test quorum notification
    await notificationUtils.createNotificationWithPush(
      parseInt(userId as string), // Sender ID
      parseInt(userId as string), // Receiver ID (same as sender for testing)
      'Event Quorum Reached',
      'event_quorum_reached',
      `The event "${event.name}" has reached its quorum of ${event.quorum} participants!`,
      {
        eventId: event.id,
        eventName: event.name,
        eventImageUrl: event.image_url,
        screen: 'event',
        params: { id: event.id }
      }
    );

    res.status(200).json({
      message: 'Test quorum notification sent successfully'
    });
  } catch (error) {
    console.error('Error sending test quorum notification:', error);
    next(error);
  }
};

/**
 * Test date confirmed notification
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testDateConfirmedNotification2 = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, eventId } = req.query;

    if (!userId) {
      res.status(400).json({ message: 'User ID parameter is required' });
      return;
    }

    if (!eventId) {
      res.status(400).json({ message: 'Event ID parameter is required' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(parseInt(eventId as string));
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Format a date for the notification
    const formattedDate = formatNotificationDate(new Date());

    // Send a test date confirmed notification
    await notificationUtils.createNotificationWithPush(
      parseInt(userId as string), // Sender ID
      parseInt(userId as string), // Receiver ID (same as sender for testing)
      'Date Confirmed',
      'event_date_confirmed',
      `The date for "${event.name}" has been confirmed: ${formattedDate}`,
      {
        eventId: event.id,
        eventName: event.name,
        eventImageUrl: event.image_url,
        screen: 'event',
        params: { id: event.id }
      }
    );

    res.status(200).json({
      message: 'Test date confirmed notification sent successfully'
    });
  } catch (error) {
    console.error('Error sending test date confirmed notification:', error);
    next(error);
  }
};

/**
 * Test flexible date notification
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testFlexibleDateNotification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, eventId } = req.query;

    if (!userId) {
      res.status(400).json({ message: 'User ID parameter is required' });
      return;
    }

    if (!eventId) {
      res.status(400).json({ message: 'Event ID parameter is required' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(parseInt(eventId as string));
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Count the event dates
    const eventDatesCount = await EventDate.count({ where: { event_id: event.id } });
    console.log(`Event ${event.id} - Event dates count: ${eventDatesCount}`);

    // Format a date for the notification
    const formattedDate = formatNotificationDate(new Date());

    // Send a test date confirmed notification
    await notificationUtils.createNotificationWithPush(
      parseInt(userId as string), // Sender ID
      parseInt(userId as string), // Receiver ID (same as sender for testing)
      'Date Confirmed',
      'event_date_confirmed',
      `The date for "${event.name}" has been confirmed: ${formattedDate}`,
      {
        eventId: event.id,
        eventName: event.name,
        eventImageUrl: event.image_url,
        screen: 'event',
        params: { id: event.id }
      }
    );

    res.status(200).json({
      message: 'Test flexible date notification sent successfully',
      eventDatesCount
    });
  } catch (error) {
    console.error('Error sending test flexible date notification:', error);
    next(error);
  }
};

/**
 * Test email notification for date confirmed
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testDateConfirmedEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, eventId } = req.query;

    if (!userId) {
      res.status(400).json({ message: 'User ID parameter is required' });
      return;
    }

    if (!eventId) {
      res.status(400).json({ message: 'Event ID parameter is required' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(parseInt(eventId as string));
    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Format a date for the notification
    const formattedDate = formatNotificationDate(new Date());

    // Send a test email notification directly
    const emailResult = await emailService.sendEmailToUser(
      parseInt(userId as string),
      'Date Confirmed',
      await generateNotificationEmail(
        'Date Confirmed',
        `The date for "${event.name}" has been confirmed: ${formattedDate}`,
        'event_date_confirmed',
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url,
          screen: 'event',
          params: { id: event.id }
        }
      )
    );

    res.status(200).json({
      message: 'Test date confirmed email sent successfully',
      emailResult: emailResult ? 'sent' : 'not sent'
    });
  } catch (error) {
    console.error('Error sending test date confirmed email:', error);
    next(error);
  }
};

/**
 * Test SendGrid direct email
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testSendGridDirect = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    // Send a test email directly using SendGrid
    const sgMail = require('@sendgrid/mail');
    const sendgridKey = process.env.SENDGRID_KEY || '';

    if (!sendgridKey) {
      res.status(500).json({ message: 'SENDGRID_KEY is not set in the environment variables' });
      return;
    }

    sgMail.setApiKey(sendgridKey);

    const msg = {
      to: email as string,
      from: '<EMAIL>',
      subject: 'Test SendGrid Direct Email',
      text: 'This is a test email sent directly using SendGrid.',
      html: '<strong>This is a test email sent directly using SendGrid.</strong>',
    };

    const response = await sgMail.send(msg);

    res.status(200).json({
      message: 'Test SendGrid direct email sent successfully',
      statusCode: response[0].statusCode
    });
  } catch (error: any) {
    console.error('Error sending test SendGrid direct email:', error);
    res.status(500).json({
      message: 'Error sending test SendGrid direct email',
      error: error.response ? error.response.body : error.message
    });
  }
};

/**
 * Test image URL signing for email notifications
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testImageUrlSigning = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email, imageUrl } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    if (!imageUrl) {
      res.status(400).json({ message: 'Image URL parameter is required' });
      return;
    }

    // Check if the URL is an S3 URL
    const isS3 = isS3Url(imageUrl as string);
    console.log('Is S3 URL:', isS3);
    console.log('CloudFront domain from env:', process.env.CLOUDFRONT_DOMAIN);

    // Get a signed URL if it's an S3 URL
    let signedUrl = imageUrl as string;
    if (isS3) {
      try {
        signedUrl = await s3SigningService.getSignedUrl(imageUrl as string);
        console.log('Signed URL:', signedUrl);
      } catch (error) {
        console.error('Error signing URL:', error);
        // Keep the original URL if signing fails
      }
    }

    // Test with logo URL as well
    const logoUrl = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/signup-sheet-logo.png';
    const isLogoS3 = isS3Url(logoUrl);
    console.log('Is logo S3 URL:', isLogoS3);
    let signedLogoUrl = logoUrl;
    if (isLogoS3) {
      try {
        signedLogoUrl = await s3SigningService.getSignedUrl(logoUrl);
        console.log('Signed logo URL:', signedLogoUrl);
      } catch (error) {
        console.error('Error signing logo URL:', error);
        // Keep the original URL if signing fails
      }
    }

    // Load the default template with the signed URL
    const html = await loadTemplate('default-template', {
      message: 'This is a test email to verify image URL signing.',
      image_url: signedUrl,
      logo_url: signedLogoUrl,
      request_url: `${DEEP_LINK_SCHEME}home`,
      app_download_url: 'https://qwrm.app/download'
    });

    // Send the test email
    await emailService.sendEmail(
      email as string,
      'QWRM Image URL Signing Test',
      html
    );

    res.status(200).json({
      message: 'Test email with signed image URL sent successfully',
      originalUrl: imageUrl,
      isS3Url: isS3,
      signedUrl: signedUrl,
      logoUrl: logoUrl,
      isLogoS3Url: isLogoS3,
      signedLogoUrl: signedLogoUrl,
      cloudFrontDomain: process.env.CLOUDFRONT_DOMAIN || 'not set'
    });
  } catch (error) {
    console.error('Error sending test email with signed image URL:', error);
    next(error);
  }
};

/**
 * Test duplicate email prevention
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testDuplicateEmailPrevention = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const testEmail = '<EMAIL>';
    const User = require('../models/User').default;

    // Clean up any existing test users
    await User.destroy({
      where: { email: testEmail }
    });

    // Test 1: Create first enabled user
    const user1 = await User.create({
      display_name: 'Test User 1',
      first_name: 'Test',
      last_name: 'User1',
      email: testEmail,
      password_hash: 'test-hash-1',
      provider: 'google',
      provider_user_id: testEmail,
      access_level: 'user',
      enabled: true,
      auto_locate: false
    });

    // Test 2: Try to create second enabled user with same email (should fail)
    let duplicateError = null;
    try {
      await User.create({
        display_name: 'Test User 2',
        first_name: 'Test',
        last_name: 'User2',
        email: testEmail,
        password_hash: 'test-hash-2',
        provider: 'google',
        provider_user_id: testEmail,
        access_level: 'user',
        enabled: true,
        auto_locate: false
      });
    } catch (error: any) {
      duplicateError = error.message;
    }

    // Test 3: Create disabled user with same email (should succeed)
    const disabledUser = await User.create({
      display_name: 'Test User 3 (Disabled)',
      first_name: 'Test',
      last_name: 'User3',
      email: testEmail,
      password_hash: 'test-hash-3',
      provider: 'google',
      provider_user_id: testEmail,
      access_level: 'user',
      enabled: false,
      auto_locate: false
    });

    // Clean up test data
    await User.destroy({
      where: { email: testEmail }
    });

    res.status(200).json({
      message: 'Duplicate email prevention test completed',
      results: {
        firstUserCreated: !!user1.id,
        duplicateEnabledUserBlocked: !!duplicateError,
        duplicateError: duplicateError,
        disabledUserCreated: !!disabledUser.id
      }
    });
  } catch (error) {
    console.error('Error testing duplicate email prevention:', error);
    next(error);
  }
};

/**
 * Test email verification functionality
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const testEmailVerification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.query;

    if (!email) {
      res.status(400).json({ message: 'Email parameter is required' });
      return;
    }

    // Find or create a test user
    let user = await User.findOne({
      where: { email: email as string, enabled: true }
    });

    if (!user) {
      // Create a test user
      user = await User.create({
        display_name: 'Test User',
        first_name: 'Test',
        last_name: 'User',
        email: email as string,
        provider: 'google',
        provider_user_id: email as string,
        access_level: 'user',
        enabled: true,
        auto_locate: false,
        email_verified: false
      });
    }

    // Generate verification token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

    // Delete any existing tokens for this user
    await EmailVerificationToken.destroy({
      where: { user_id: user.id }
    });

    // Create new verification token
    await EmailVerificationToken.create({
      user_id: user.id,
      token,
      expires_at: expiresAt
    });

    // Create verification URL
    const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
    const verificationUrl = `${API_BASE_URL}/api/auth/verify-email?token=${token}`;

    // Load email template
    const html = await loadTemplate('email-verification-template', {
      user_name: user.first_name || user.display_name || 'User',
      verification_url: verificationUrl,
      app_download_url: 'https://qwrm.app/download'
    });

    // Send verification email
    await emailService.sendEmail(
      user.email,
      'Verify Your Email Address - QWRM',
      html
    );

    res.status(200).json({
      message: 'Test email verification sent successfully',
      email: user.email,
      verificationUrl,
      tokenExpires: expiresAt
    });
  } catch (error) {
    console.error('Error sending test email verification:', error);
    next(error);
  }
};

export default {
  testEmailService,
  testNotificationWithEmail,
  testEmailWithImage,
  testEventNotificationEmail,
  testDateConfirmedNotification,
  testEmailWithLogo,
  testQuorumNotification,
  testDateConfirmedNotification2,
  testFlexibleDateNotification,
  testDateConfirmedEmail,
  testSendGridDirect,
  testImageUrlSigning,
  testDuplicateEmailPrevention,
  testEmailVerification
};
