import { Request, Response, NextFunction } from 'express';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import crypto from 'crypto';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import multer from 'multer';
import { promisify } from 'util';

// Initialize environment variables
dotenv.config();

// Configure S3 client
const s3Client = new S3Client({
  region: process.env.AMAZON_S3_REGION || 'us-east-2',
  credentials: {
    accessKeyId: process.env.AMAZON_S3_KEY_ID || '',
    secretAccessKey: process.env.AMAZON_S3_SECRET || '',
  },
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Use an absolute path to ensure the directory is created correctly
    const uploadDir = path.resolve(process.cwd(), 'uploads');
    console.log('Upload directory:', uploadDir);

    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      console.log('Creating upload directory...');
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + '-' + file.originalname);
  }
});

export const upload = multer({ storage });

// Generate a signed URL for an existing S3 object
export const getSignedObjectUrl = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { key, expiresIn = '3600' } = req.query;

    if (!key) {
      res.status(400).json({ error: 'key parameter is required' });
      return;
    }

    // Create the command to get an object from S3
    const command = new GetObjectCommand({
      Bucket: process.env.AMAZON_S3_BUCKET_NAME,
      Key: key as string,
    });

    // Generate a presigned URL that expires in the specified time (default: 1 hour)
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: parseInt(expiresIn as string) });
    console.log('Generated signed URL for existing object:', signedUrl);

    res.json({
      signedUrl,
      key
    });
  } catch (error) {
    console.error('Error generating signed URL for existing object:', error);
    next(error);
  }
};

// Generate a presigned URL for uploading a file to S3
export const getPresignedUrl = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log('Received presigned URL request:', req.body);
    const { fileType, fileName } = req.body;

    if (!fileType || !fileName) {
      console.log('Missing required fields:', { fileType, fileName });
      res.status(400).json({ error: 'fileType and fileName are required' });
      return;
    }

    console.log('S3 client config:', {
      region: process.env.AMAZON_S3_REGION,
      bucketName: process.env.AMAZON_S3_BUCKET_NAME,
      keyIdExists: !!process.env.AMAZON_S3_KEY_ID,
      secretExists: !!process.env.AMAZON_S3_SECRET
    });

    // Generate a unique file name to prevent collisions
    const fileExtension = fileName.split('.').pop();
    const randomFileName = `${crypto.randomUUID()}.${fileExtension}`;

    // Create the S3 key (path in the bucket)
    const key = `uploads/${randomFileName}`;

    // Create the command to put an object in S3
    const command = new PutObjectCommand({
      Bucket: process.env.AMAZON_S3_BUCKET_NAME,
      Key: key,
      ContentType: fileType,
    });

    // Generate a presigned URL that expires in 5 minutes
    const presignedUrl = await getSignedUrl(s3Client, command, { expiresIn: 300 });
    console.log('Generated presigned URL:', presignedUrl);

    // Calculate the final URL that will be used to access the file after upload
    const s3Url = `https://${process.env.AMAZON_S3_BUCKET_NAME}.s3.${process.env.AMAZON_S3_REGION}.amazonaws.com/${key}`;
    const fileUrl = s3Url;
    console.log('S3 URL will be:', s3Url);

    const response = {
      presignedUrl,
      fileUrl,
      key
    };

    console.log('Sending response:', response);
    res.json(response);
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    next(error);
  }
};

// Upload a file directly to S3 from the server
// Use a more specific type for the request to include the file property added by multer
export const uploadToS3 = async (req: Request & { file?: any }, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.file) {
      res.status(400).json({ error: 'No file uploaded' });
      return;
    }

    console.log('Received file upload:', req.file);

    // Generate a unique file name
    const fileExtension = path.extname(req.file.originalname);
    const randomFileName = `${crypto.randomUUID()}${fileExtension}`;

    // Create the S3 key (path in the bucket)
    const key = `uploads/${randomFileName}`;

    // Check if the file exists on disk
    if (!fs.existsSync(req.file.path)) {
      console.error('File does not exist on disk:', req.file.path);
      res.status(500).json({ error: 'File not found on server' });
      return;
    }

    console.log('Reading file from disk:', req.file.path);
    // Read the file from disk
    const fileContent = fs.readFileSync(req.file.path);
    console.log('File read successfully, size:', fileContent.length);

    // Upload the file to S3
    const command = new PutObjectCommand({
      Bucket: process.env.AMAZON_S3_BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ContentType: req.file.mimetype,
    });

    console.log('Uploading to S3:', {
      bucket: process.env.AMAZON_S3_BUCKET_NAME,
      key,
      contentType: req.file.mimetype
    });

    const result = await s3Client.send(command);
    console.log('S3 upload result:', result);

    // Calculate the S3 URL
    const s3Url = `https://${process.env.AMAZON_S3_BUCKET_NAME}.s3.${process.env.AMAZON_S3_REGION}.amazonaws.com/${key}`;
    const fileUrl = s3Url;
    console.log('S3 URL will be:', s3Url);

    // Delete the temporary file
    fs.unlinkSync(req.file.path);

    res.json({
      fileUrl,
      key
    });
  } catch (error) {
    console.error('Error uploading to S3:', error);

    // Clean up the temporary file if it exists
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    next(error);
  }
};
