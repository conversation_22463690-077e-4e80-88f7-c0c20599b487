import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import User from '../models/User';
import EmailVerificationToken from '../models/EmailVerificationToken';
import emailService from '../services/emailService';
import { loadTemplate } from '../utils/emailTemplateUtils';

// Deep link scheme for the app
const DEEP_LINK_SCHEME = 'qwrm://';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

/**
 * Send email verification email to user
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const sendVerificationEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email } = req.body;

    if (!email) {
      res.status(400).json({ message: 'Email is required' });
      return;
    }

    // Find user by email
    const user = await User.findOne({
      where: {
        email,
        enabled: true
      }
    });

    if (!user) {
      // Don't reveal if user exists or not for security
      res.status(200).json({ message: 'If an account with that email exists, a verification email has been sent.' });
      return;
    }

    // Check if user is already verified
    if (user.email_verified) {
      res.status(400).json({ message: 'Email is already verified' });
      return;
    }

    // Generate verification token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

    // Delete any existing tokens for this user
    await EmailVerificationToken.destroy({
      where: { user_id: user.id }
    });

    // Create new verification token
    await EmailVerificationToken.create({
      user_id: user.id,
      token,
      expires_at: expiresAt
    });

    // Create verification URL
    const verificationUrl = `${API_BASE_URL}/api/auth/verify-email?token=${token}`;

    // Load email template
    const html = await loadTemplate('email-verification-template', {
      user_name: user.first_name || user.display_name || 'User',
      verification_url: verificationUrl,
      app_download_url: 'https://qwrm.app/download'
    });

    // Send verification email
    await emailService.sendEmail(
      user.email,
      'Verify Your Email Address - QWRM',
      html
    );

    res.status(200).json({ message: 'Verification email sent successfully' });
  } catch (error) {
    console.error('Error sending verification email:', error);
    next(error);
  }
};

/**
 * Verify email using token
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const verifyEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { token } = req.query;

    if (!token || typeof token !== 'string') {
      const deepLinkUrl = `${DEEP_LINK_SCHEME}email-verification`;
      const errorHtml = generateVerificationErrorHtml(deepLinkUrl);
      res.status(400).send(errorHtml);
      return;
    }

    // Find the verification token
    const verificationToken = await EmailVerificationToken.findOne({
      where: {
        token,
        used: false
      }
    });

    if (!verificationToken) {
      const deepLinkUrl = `${DEEP_LINK_SCHEME}email-verification`;
      const errorHtml = generateVerificationErrorHtml(deepLinkUrl);
      res.status(400).send(errorHtml);
      return;
    }

    // Check if token has expired
    if (verificationToken.expires_at < new Date()) {
      const deepLinkUrl = `${DEEP_LINK_SCHEME}email-verification`;
      const errorHtml = generateVerificationErrorHtml(deepLinkUrl);
      res.status(400).send(errorHtml);
      return;
    }

    // Get the user
    const user = await User.findByPk(verificationToken.user_id);
    if (!user) {
      const deepLinkUrl = `${DEEP_LINK_SCHEME}email-verification`;
      const errorHtml = generateVerificationErrorHtml(deepLinkUrl);
      res.status(400).send(errorHtml);
      return;
    }

    // Mark user as verified
    await user.update({ email_verified: true });

    // Mark token as used
    await verificationToken.update({ used: true });

    // Return success HTML page with deep link to email verification screen
    // The email verification screen will detect the user is verified and redirect appropriately
    const deepLinkUrl = `${DEEP_LINK_SCHEME}email-verification`;
    const successHtml = generateVerificationSuccessHtml(user.first_name || user.display_name || 'User', deepLinkUrl);
    res.status(200).send(successHtml);
  } catch (error) {
    console.error('Error verifying email:', error);
    // Return error HTML page with deep link to email verification screen
    const deepLinkUrl = `${DEEP_LINK_SCHEME}email-verification`;
    const errorHtml = generateVerificationErrorHtml(deepLinkUrl);
    res.status(400).send(errorHtml);
  }
};

/**
 * Check email verification status
 * @param req Request object (authenticated)
 * @param res Response object
 * @param next Next function
 */
export const checkVerificationStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authReq = req as any;
    const userId = authReq.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    const user = await User.findByPk(userId);
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    res.status(200).json({
      email_verified: user.email_verified,
      email: user.email
    });
  } catch (error) {
    console.error('Error checking verification status:', error);
    next(error);
  }
};

/**
 * Resend verification email for authenticated user
 * @param req Request object (authenticated)
 * @param res Response object
 * @param next Next function
 */
export const resendVerificationEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authReq = req as any;
    const userId = authReq.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    const user = await User.findByPk(userId);
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    if (user.email_verified) {
      res.status(400).json({ message: 'Email is already verified' });
      return;
    }

    // Use the existing sendVerificationEmail logic
    req.body = { email: user.email };
    await sendVerificationEmail(req, res, next);
  } catch (error) {
    console.error('Error resending verification email:', error);
    next(error);
  }
};

/**
 * Generate HTML for successful email verification
 * @param userName User's name
 * @param deepLinkUrl Deep link URL to the app
 * @returns HTML string
 */
const generateVerificationSuccessHtml = (userName: string, deepLinkUrl: string): string => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verified - QWRM</title>
    <style>
        body {
            margin: 0;
            padding: 20px 20px 40px 20px;
            font-family: Arial, Helvetica, sans-serif;
            background-color: #00ccff;
            color: #333333;
            text-align: center;
        }
        .main-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .content-box {
            background-color: #0033FF;
            border: 1px solid #000066;
            margin-bottom: 30px;
            box-shadow: 6px 6px 0px 0px #000066;
            padding: 30px 20px;
            text-align: center;
        }
        .title {
            margin: 0 0 20px 0;
            font-size: 24px;
            color: #ffffff;
            font-weight: bold;
        }
        .welcome-message {
            margin: 0 0 20px 0;
            font-size: 18px;
            color: #ffffff;
            line-height: 1.5;
        }
        .description {
            margin: 0 0 30px 0;
            font-size: 16px;
            color: #ffffff;
            line-height: 1.5;
        }
        .cta-button {
            background-color: #00ccff;
            border: 2px solid #000066;
            box-shadow: 4px 4px 0px 0px #000066;
            display: inline-block;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            color: #000066;
            text-decoration: none;
            text-transform: uppercase;
        }
        .cta-button:hover {
            background-color: #00b8e6;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="content-box">
            <div class="title">Email Verified!</div>
            <div class="welcome-message">Hi ${userName},</div>
            <div class="description">
                Your email has been successfully verified. You can now access all features of QWRM and start creating and joining events.
            </div>
            <a href="${deepLinkUrl}" class="cta-button">Open QWRM</a>
        </div>
    </div>
    <script>
        // Auto-redirect to app after 3 seconds
        setTimeout(function() {
            window.location.href = '${deepLinkUrl}';
        }, 3000);
    </script>
</body>
</html>`;
};

/**
 * Generate HTML for failed email verification
 * @param deepLinkUrl Deep link URL to the app
 * @returns HTML string
 */
const generateVerificationErrorHtml = (deepLinkUrl: string): string => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Failed - QWRM</title>
    <style>
        body {
            margin: 0;
            padding: 20px 20px 40px 20px;
            font-family: Arial, Helvetica, sans-serif;
            background-color: #00ccff;
            color: #333333;
            text-align: center;
        }
        .main-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .content-box {
            background-color: #0033FF;
            border: 1px solid #000066;
            margin-bottom: 30px;
            box-shadow: 6px 6px 0px 0px #000066;
            padding: 30px 20px;
            text-align: center;
        }
        .title {
            margin: 0 0 20px 0;
            font-size: 24px;
            color: #ffffff;
            font-weight: bold;
        }
        .description {
            margin: 0 0 30px 0;
            font-size: 16px;
            color: #ffffff;
            line-height: 1.5;
        }
        .cta-button {
            background-color: #00ccff;
            border: 2px solid #000066;
            box-shadow: 4px 4px 0px 0px #000066;
            display: inline-block;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            color: #000066;
            text-decoration: none;
            text-transform: uppercase;
        }
        .cta-button:hover {
            background-color: #00b8e6;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="content-box">
            <div class="title">Verification Failed</div>
            <div class="description">
                The verification link is invalid, expired, or has already been used. Please try requesting a new verification email from the app.
            </div>
            <a href="${deepLinkUrl}" class="cta-button">Open QWRM</a>
        </div>
    </div>
</body>
</html>`;
};
