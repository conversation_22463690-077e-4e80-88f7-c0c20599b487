import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../types';
import Event, { EventParticipant } from '../models/Event';
import EventDate from '../models/EventDate';
import User from '../models/User';
import notificationUtils from '../utils/notificationUtils';
import { sendEventInvitationEmails } from '../utils/emailNotificationUtils';

// Get all participants for an event
export const getEventParticipants = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);

    // Validate event ID
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Get the participants array
    const participantIds = event.participants || [];

    // If there are no participants, return an empty array
    if (participantIds.length === 0) {
      res.json([]);
      return;
    }

    // Fetch user details for each participant
    const users = await User.findAll({
      where: { id: participantIds },
      attributes: ['id', 'display_name', 'first_name', 'last_name', 'email', 'profile_image_url']
    });

    // Map user IDs to user objects
    const participantsWithDetails = participantIds.map(userId => {
      const user = users.find(u => u.id === userId);
      return {
        user_id: userId,
        status: 'going', // Default status
        joined_at: new Date().toISOString(),
        user: user ? {
          id: user.id,
          display_name: user.display_name,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          profile_image_url: user.profile_image_url
        } : undefined
      };
    });

    // Return the participants with user details
    res.json(participantsWithDetails);
  } catch (error) {
    console.error(`Error getting participants for event ${req.params.eventId}:`, error);
    next(error);
  }
};

// Sign up for an event
export const signUpForEvent = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const userId = req.user?.id;
    const { status = 'going' } = req.body;

    // Validate event ID
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Validate user ID
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Validate status
    if (!['going', 'invited', 'maybe', 'declined'].includes(status)) {
      res.status(400).json({ message: 'Invalid status' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Get the user to include their name in the participant record
    const user = await User.findByPk(userId);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Check if the user is already a participant
    const participants = event.participants || [];
    const isParticipant = participants.includes(userId);

    // Create a new participant object for the response
    const participantInfo = {
      user_id: userId,
      status: status as 'going' | 'invited' | 'maybe' | 'declined',
      joined_at: new Date().toISOString()
    };

    // Update the participants array
    let updatedParticipants = [...participants];
    if (!isParticipant) {
      // Add new participant
      updatedParticipants.push(userId);
    }

    // Count the number of participants
    const participantsCount = updatedParticipants.length;

    // Check if quorum is met
    const quorumMet = event.quorum !== undefined && participantsCount >= event.quorum;
    const quorumJustMet = quorumMet && !event.quorum_met;

    console.log(`Event ${event.id} - Quorum check: quorumMet=${quorumMet}, quorumJustMet=${quorumJustMet}, participants=${participantsCount}, quorum=${event.quorum}, current quorum_met=${event.quorum_met}`);

    // Update the event with the new participants array and quorum status
    await event.update({
      participants: updatedParticipants,
      participants_count: participantsCount,
      quorum_met: quorumMet
    });

    // Update participants in all event dates
    const eventDates = await EventDate.findAll({
      where: { event_id: eventId }
    });

    // Add the user to participants for each event date
    for (const eventDate of eventDates) {
      const dateParticipants = eventDate.participants || [];
      if (!dateParticipants.includes(userId)) {
        await eventDate.update({
          participants: [...dateParticipants, userId]
        });
      }
    }

    // If quorum was just met, send notifications to all participants
    if (quorumJustMet) {
      console.log(`Event ${event.id} - Quorum just met! Sending notifications to ${updatedParticipants.length} participants`);
      // Get all participant user IDs
      const participantUserIds = updatedParticipants;

      // Send notifications to all participants
      await notificationUtils.createNotificationsWithPush(
        userId, // Current user is the sender
        participantUserIds,
        'Event Quorum Reached',
        'event_quorum_reached',
        `The event "${event.name}" has reached its quorum of ${event.quorum} participants!`,
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'event',
          params: { id: event.id }
        }
      );
    }

    res.json(participantInfo);
  } catch (error) {
    console.error(`Error signing up for event ${req.params.eventId}:`, error);
    next(error);
  }
};

// Update participation status
export const updateParticipationStatus = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const userId = req.user?.id;
    const { status } = req.body;

    // Validate event ID
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Validate user ID
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Validate status
    if (!status || !['going', 'invited', 'maybe', 'declined'].includes(status)) {
      res.status(400).json({ message: 'Invalid status' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Check if the user is already a participant
    const participants = event.participants || [];
    const isParticipant = participants.includes(userId);

    if (!isParticipant) {
      res.status(404).json({ message: 'Participant not found' });
      return;
    }

    // Create a participant info object for the response
    const participantInfo = {
      user_id: userId,
      status: status as 'going' | 'invited' | 'maybe' | 'declined',
      joined_at: new Date().toISOString()
    };

    // Count the number of participants
    const participantsCount = participants.length;

    // Check if quorum is met
    const quorumMet = event.quorum !== undefined && participantsCount >= event.quorum;
    const quorumJustMet = quorumMet && !event.quorum_met;

    console.log(`Event ${event.id} - Quorum check: quorumMet=${quorumMet}, quorumJustMet=${quorumJustMet}, participants=${participantsCount}, quorum=${event.quorum}, current quorum_met=${event.quorum_met}`);

    // Update the event with the quorum status
    await event.update({
      quorum_met: quorumMet
    });

    // If quorum was just met, send notifications to all participants
    if (quorumJustMet) {
      console.log(`Event ${event.id} - Quorum just met! Sending notifications to ${participants.length} participants`);
      // Get all participant user IDs
      const participantUserIds = participants;

      // Send notifications to all participants
      await notificationUtils.createNotificationsWithPush(
        userId, // Current user is the sender
        participantUserIds,
        'Event Quorum Reached',
        'event_quorum_reached',
        `The event "${event.name}" has reached its quorum of ${event.quorum} participants!`,
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'event',
          params: { id: event.id }
        }
      );
    }

    res.json(participantInfo);
  } catch (error) {
    console.error(`Error updating participation status for event ${req.params.eventId}:`, error);
    next(error);
  }
};

// Leave an event (remove participation)
export const leaveEvent = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const userId = req.user?.id;

    // Validate event ID
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Validate user ID
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Filter out the participant
    const participants = (event.participants || []).filter(id => id !== userId);

    // Count the number of participants
    const participantsCount = participants.length;

    // Check if quorum is met
    const quorumMet = event.quorum !== undefined && participantsCount >= event.quorum;
    const quorumLost = !quorumMet && event.quorum_met;

    // Update the event with the modified participants array and quorum status
    await event.update({
      participants,
      participants_count: participantsCount,
      quorum_met: quorumMet
    });

    // Update participants in all event dates
    const eventDates = await EventDate.findAll({
      where: { event_id: eventId }
    });

    // Remove the user from participants for each event date
    for (const eventDate of eventDates) {
      const dateParticipants = eventDate.participants || [];
      if (dateParticipants.includes(userId)) {
        await eventDate.update({
          participants: dateParticipants.filter(id => id !== userId)
        });
      }
    }

    // If quorum was just lost, send notifications to all participants
    if (quorumLost) {
      // Get all participant user IDs
      const participantUserIds = participants;

      // Send notifications to all participants
      await notificationUtils.createNotificationsWithPush(
        userId, // Current user is the sender
        participantUserIds,
        'Event Quorum Lost',
        'event_update',
        `The event "${event.name}" no longer has enough participants to meet quorum.`,
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'event',
          params: { id: event.id }
        }
      );
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error leaving event ${req.params.eventId}:`, error);
    next(error);
  }
};

// Invite a user to an event
export const inviteToEvent = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const { userId } = req.body;
    const inviterId = req.user?.id;

    // Validate event ID and user ID
    if (isNaN(eventId) || isNaN(userId)) {
      res.status(400).json({ message: 'Invalid event ID or user ID' });
      return;
    }

    // Validate inviter ID
    if (!inviterId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Get the user to include their name in the participant record
    const user = await User.findByPk(userId);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Check if the user is already a participant
    const participants = event.participants || [];
    const isParticipant = participants.includes(userId);

    if (isParticipant) {
      res.status(400).json({ message: 'User is already invited or participating' });
      return;
    }

    // Create a participant info object for the response
    const participantInfo = {
      user_id: userId,
      status: 'invited',
      joined_at: new Date().toISOString()
    };

    // Add the new participant
    const updatedParticipants = [...participants, userId];

    // Update the event with the new participants array
    await event.update({
      participants: updatedParticipants,
      participants_count: updatedParticipants.length
    });

    // Send notification to the invited user
    await notificationUtils.createNotificationWithPush(
      inviterId, // Inviter is the sender
      userId, // Invited user is the receiver
      'Event Invitation',
      'connection_request', // Using connection_request as the notice type for invitations
      `You've been invited to "${event.name}" by ${user.display_name || 'a user'}`,
      {
        eventId: event.id,
        eventName: event.name,
        eventImageUrl: event.image_url, // Include the event image URL
        screen: 'event',
        params: { id: event.id }
      }
    );

    // Send email invitation to the invited user
    try {
      await sendEventInvitationEmails(event.id, inviterId, [userId]);
      console.log(`Event invitation email sent to user ${userId} for event ${event.id}`);
    } catch (emailError) {
      console.error(`Failed to send event invitation email to user ${userId}:`, emailError);
      // Don't fail the invitation if email sending fails
    }

    res.status(201).json(participantInfo);
  } catch (error) {
    console.error(`Error inviting user to event ${req.params.eventId}:`, error);
    next(error);
  }
};
