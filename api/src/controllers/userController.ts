import { Request, Response, NextFunction } from 'express';
import { Op } from 'sequelize';
import User from '../models/User';

// Get all users
export const getAllUsers = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const users = await User.findAll({
      attributes: ['id', 'display_name', 'email', 'access_level']
    });
    res.json(users);
  } catch (error) {
    console.error('Error getting all users:', error);
    next(error);
  }
};

// Get user by ID
export const getUserById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const user = await User.findByPk(req.params.id, {
      attributes: ['id', 'display_name', 'first_name', 'last_name', 'email', 'access_level', 'auto_locate', 'profile_image_url']
    });

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    res.json(user);
  } catch (error) {
    console.error(`Error getting user by ID ${req.params.id}:`, error);
    next(error);
  }
};

// Create a new user
export const createUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      display_name,
      first_name,
      last_name,
      email,
      password,
      provider = 'google'
    } = req.body;

    // Check if an enabled user already exists with this email
    const existingEnabledUser = await User.findOne({
      where: {
        email,
        enabled: true
      }
    });

    if (existingEnabledUser) {
      // Account already exists and is enabled
      res.status(409).json({ message: 'Email already exists' });
      return;
    }

    // Always create a new user (even if disabled users exist with this email)
    const newUser = await User.create({
      display_name,
      first_name,
      last_name,
      email,
      password_hash: password,
      provider,
      provider_user_id: email,
      access_level: 'user',
      enabled: true,  // Add the missing required property
      auto_locate: false  // Add the missing required property
    });

    res.status(201).json({
      id: newUser.id,
      display_name: newUser.display_name,
      email: newUser.email
    });
  } catch (error) {
    console.error('Error creating user:', error);
    next(error);
  }
};

// Update a user
export const updateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { display_name, first_name, last_name, email, phone_number, auto_locate, profile_image_url, enabled } = req.body;

    // If email is being updated, check if another enabled user already has this email
    if (email) {
      const existingEnabledUser = await User.findOne({
        where: {
          email,
          enabled: true,
          id: { [Op.ne]: req.params.id } // Exclude the current user
        }
      });

      if (existingEnabledUser) {
        // Another enabled user already has this email
        res.status(409).json({ message: 'Email already exists' });
        return;
      }
    }

    const [updatedCount, updatedUsers] = await User.update({
      display_name,
      first_name,
      last_name,
      email,
      phone_number,
      auto_locate,
      profile_image_url,
      enabled,
      updated_at: new Date()
    }, {
      where: { id: req.params.id },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    const updatedUser = updatedUsers[0];
    res.json({
      id: updatedUser.id,
      display_name: updatedUser.display_name,
      first_name: updatedUser.first_name,
      last_name: updatedUser.last_name,
      email: updatedUser.email,
      phone_number: updatedUser.phone_number,
      auto_locate: updatedUser.auto_locate,
      profile_image_url: updatedUser.profile_image_url
    });
  } catch (error) {
    console.error(`Error updating user ${req.params.id}:`, error);
    next(error);
  }
};

// Delete a user
export const deleteUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const deletedCount = await User.destroy({
      where: { id: req.params.id }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting user ${req.params.id}:`, error);
    next(error);
  }
};