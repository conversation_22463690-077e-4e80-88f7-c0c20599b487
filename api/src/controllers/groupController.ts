import { Request, Response, NextFunction } from 'express';
import Group from '../models/Group';

// Get all groups
export const getAllGroups = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Only return groups where the current user is the owner
    const groups = await Group.findAll({
      where: {
        owner_id: userId,
        enabled: true
      },
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']]
    });

    res.json(groups);
  } catch (error) {
    console.error('Error getting all groups:', error);
    next(error);
  }
};

// Get group by ID
export const getGroupById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const group = await Group.findOne({
      where: {
        id: req.params.id,
        owner_id: userId
      }
    });

    if (!group) {
      res.status(404).json({ message: 'Group not found or you do not have access to it' });
      return;
    }

    res.json(group);
  } catch (error) {
    console.error(`Error getting group by ID ${req.params.id}:`, error);
    next(error);
  }
};

// Create a new group
export const createGroup = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      name,
      owner_id,  // Changed to align with model
      enabled = true,  // Default to true
      contacts = []  // Array of contact IDs to add as members
    } = req.body;

    // Get the highest sort_order for this user's groups and add 1
    const maxSortOrder = await Group.max('sort_order', {
      where: { owner_id }
    }) as number || 0;

    const newGroup = await Group.create({
      name,
      owner_id,
      enabled,
      members: contacts, // Store contact IDs in the members array
      sort_order: maxSortOrder + 1,
      created_at: new Date(),
      updated_at: new Date()
    });

    res.status(201).json(newGroup);
  } catch (error) {
    console.error('Error creating group:', error);
    next(error);
  }
};

// Update a group
export const updateGroup = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const { name, enabled, contacts, members } = req.body;

    // First check if the group exists and belongs to the user
    const existingGroup = await Group.findOne({
      where: {
        id: req.params.id,
        owner_id: userId
      }
    });

    if (!existingGroup) {
      res.status(404).json({ message: 'Group not found or you do not have access to it' });
      return;
    }

    // Prepare update data
    const updateData: any = {
      name,
      enabled,
      updated_at: new Date()
    };

    // Only update members if contacts or members array is provided
    if (contacts !== undefined) {
      updateData.members = contacts;
    } else if (members !== undefined) {
      updateData.members = members;
    }

    // Update the group, maintaining the original owner_id
    const [updatedCount, updatedGroups] = await Group.update(updateData, {
      where: {
        id: req.params.id,
        owner_id: userId
      },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'Group not found' });
      return;
    }

    const updatedGroup = updatedGroups[0];
    res.json(updatedGroup);
  } catch (error) {
    console.error(`Error updating group ${req.params.id}:`, error);
    next(error);
  }
};

// Delete a group
export const deleteGroup = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Only allow deletion of groups owned by the current user
    const deletedCount = await Group.destroy({
      where: {
        id: req.params.id,
        owner_id: userId
      }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'Group not found or you do not have access to it' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting group ${req.params.id}:`, error);
    next(error);
  }
};

// Update group order
export const updateGroupOrder = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const { groupOrders } = req.body; // Array of { id, sort_order }

    if (!Array.isArray(groupOrders)) {
      res.status(400).json({ message: 'groupOrders must be an array' });
      return;
    }

    // Update each group's sort_order
    const updatePromises = groupOrders.map(({ id, sort_order }) =>
      Group.update(
        { sort_order },
        {
          where: {
            id,
            owner_id: userId
          }
        }
      )
    );

    await Promise.all(updatePromises);

    res.json({ message: 'Group order updated successfully' });
  } catch (error) {
    console.error('Error updating group order:', error);
    next(error);
  }
};
