import { Request, Response, NextFunction } from 'express';
import ConnectRequest from '../models/ConnectRequest';
import Contact from '../models/Contact';
import User from '../models/User';
import Notification from '../models/Notification';
import { createNotificationWithPush } from '../utils/notificationUtils';
import sequelize from '../db';

interface AuthRequest extends Request {
  user?: Partial<User>;
}

// Get all connect requests for the authenticated user
export const getAllConnectRequests = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const connectRequests = await ConnectRequest.findAll({
      where: {
        owner_id: ownerId,
        is_accepted: false,
        is_declined: false
      },
      order: [['created_at', 'DESC']]
    });

    res.json(connectRequests);
  } catch (error) {
    console.error('Error getting all connect requests:', error);
    next(error);
  }
};

// Get connect request by ID
export const getConnectRequestById = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const connectRequest = await ConnectRequest.findOne({
      where: {
        id: req.params.id,
        owner_id: ownerId
      }
    });

    if (!connectRequest) {
      res.status(404).json({ message: 'Connect request not found' });
      return;
    }

    res.json(connectRequest);
  } catch (error) {
    console.error(`Error getting connect request ${req.params.id}:`, error);
    next(error);
  }
};

// Create a new connect request
export const createConnectRequest = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const {
      email,
      first_name,
      last_name,
      phone_number
    } = req.body;

    // Validate required fields
    if (!first_name) {
      res.status(400).json({ message: 'First name is required' });
      return;
    }

    // Validate that email is provided
    if (!email || email.trim() === '') {
      res.status(400).json({ message: 'Email is required for connect requests' });
      return;
    }

    // Check if a user exists with this email
    let userId: number | undefined;
    let existingUser: User | null = null;
    if (email && email.trim() !== '') {
      existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        userId = existingUser.id;
      }
    }

    // Create the connect request
    const newConnectRequest = await ConnectRequest.create({
      owner_id: ownerId,
      user_id: userId,
      email,
      first_name,
      last_name,
      phone_number,
      is_accepted: false,
      is_declined: false,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Note: We no longer create a contact immediately when sending a request
    // Contacts will be created for both users only when the request is accepted

    // If we found a user with this email, create a notification for them
    if (userId && existingUser) {
      // Get the sender's information
      const sender = await User.findByPk(ownerId);
      const senderName = sender ? sender.display_name : 'Someone';

      // Get the sender's profile image URL
      const senderProfileImg = sender?.profile_image_url;

      // We'll store the full URL in the notification avatar field
      // The frontend will handle signing the URL using cloudfrontService.getSignedUrl

      // Create a notification for the user
      await createNotificationWithPush(
        ownerId,
        userId,
        'Connection Request',
        'connection_request',
        `${senderName} wants to connect`,
        {
          screen: 'notifications'
        },
        newConnectRequest.id, // Pass the connect request ID directly
        senderProfileImg // Pass the sender's profile image URL
      );

      console.log(`Sent connection request notification to user ${existingUser.display_name} (ID: ${userId})`);
    }

    // Return the connect request (no contact created yet)
    res.status(201).json(newConnectRequest);
  } catch (error) {
    console.error('Error creating connect request:', error);
    next(error);
  }
};

// Accept a connect request
export const acceptConnectRequest = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;

    // Find the connect request
    const connectRequest = await ConnectRequest.findByPk(id);

    if (!connectRequest) {
      res.status(404).json({ message: 'Connect request not found' });
      return;
    }

    // Check if the request is already accepted or declined
    if (connectRequest.is_accepted || connectRequest.is_declined) {
      res.status(400).json({ message: 'Connect request already processed' });
      return;
    }

    // Mark the request as accepted
    await connectRequest.update({
      is_accepted: true,
      updated_at: new Date()
    });

    // Find the user who sent the request
    const owner = await User.findByPk(connectRequest.owner_id);
    if (!owner) {
      res.status(404).json({ message: 'Request owner not found' });
      return;
    }

    // Find the user who received the request (current user)
    const currentUserId = (req as AuthRequest).user?.id;
    if (!currentUserId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Get current user info for notification
    const currentUser = await User.findByPk(currentUserId);

    // Now we create contacts for BOTH users when the request is accepted
    // Use a transaction to ensure both contacts are created atomically
    const transaction = await sequelize.transaction();

    let recipientContact: Contact;
    let senderContact: Contact;

    try {
      // 1. Create a contact for the recipient (current user gets the sender as a contact)
      recipientContact = await Contact.create({
        user_id: connectRequest.owner_id,
        first_name: owner.first_name || owner.display_name,
        last_name: owner.last_name || '',
        email: owner.email,
        owner_id: currentUserId,
        is_priority: false,
        enabled: true,
        created_at: new Date(),
        updated_at: new Date()
      }, { transaction });

      // 2. Create a contact for the sender (sender gets the recipient as a contact)
      senderContact = await Contact.create({
        user_id: currentUserId,
        first_name: connectRequest.first_name,
        last_name: connectRequest.last_name || '',
        email: connectRequest.email,
        phone_number: connectRequest.phone_number,
        owner_id: connectRequest.owner_id,
        is_priority: false,
        enabled: true,
        created_at: new Date(),
        updated_at: new Date()
      }, { transaction });

      // Commit the transaction if both contacts were created successfully
      await transaction.commit();
    } catch (error) {
      // Rollback the transaction if any contact creation failed
      await transaction.rollback();
      console.error('Error creating contacts during connect request acceptance:', error);
      res.status(500).json({ message: 'Failed to create contacts' });
      return;
    }

    // Create a notification for the owner
    const currentUserName = currentUser ? currentUser.display_name : 'Someone';

    await createNotificationWithPush(
      currentUserId,
      connectRequest.owner_id,
      'Connection Accepted',
      'connection_request',
      `${currentUserName} accepted your connection request`,
      {
        contactId: senderContact.id,
        screen: 'contacts'
      }
    );

    res.json({
      message: 'Connect request accepted',
      connectRequest,
      recipientContact,
      senderContact
    });
  } catch (error) {
    console.error(`Error accepting connect request ${req.params.id}:`, error);
    next(error);
  }
};

// Decline a connect request
export const declineConnectRequest = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;

    // Find the connect request
    const connectRequest = await ConnectRequest.findByPk(id);

    if (!connectRequest) {
      res.status(404).json({ message: 'Connect request not found' });
      return;
    }

    // Check if the request is already accepted or declined
    if (connectRequest.is_accepted || connectRequest.is_declined) {
      res.status(400).json({ message: 'Connect request already processed' });
      return;
    }

    // Mark the request as declined
    await connectRequest.update({
      is_declined: true,
      updated_at: new Date()
    });

    // Find the notification for this connect request and delete it
    const currentUserId = (req as AuthRequest).user?.id;
    if (currentUserId) {
      await Notification.destroy({
        where: {
          receiver_id: currentUserId,
          connect_request_id: connectRequest.id,
          notice_type: 'connection_request'
        }
      });
    }

    res.json({
      message: 'Connect request declined',
      connectRequest
    });
  } catch (error) {
    console.error(`Error declining connect request ${req.params.id}:`, error);
    next(error);
  }
};

// Delete a connect request
export const deleteConnectRequest = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const deletedCount = await ConnectRequest.destroy({
      where: {
        id: req.params.id,
        owner_id: ownerId
      }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'Connect request not found' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting connect request ${req.params.id}:`, error);
    next(error);
  }
};
