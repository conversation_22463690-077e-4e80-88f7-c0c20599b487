import { Request, Response, NextFunction } from 'express';
import UserPreference from '../models/UserPreference';

// Get all user preferences
export const getAllUserPreferences = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userPreferences = await UserPreference.findAll();
    res.json(userPreferences);
  } catch (error) {
    console.error('Error getting all user preferences:', error);
    next(error);
  }
};

// Get user preference by ID
export const getUserPreferenceById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userPreference = await UserPreference.findByPk(req.params.id);

    if (!userPreference) {
      res.status(404).json({ message: 'User preference not found' });
      return;
    }

    res.json(userPreference);
  } catch (error) {
    console.error(`Error getting user preference by ID ${req.params.id}:`, error);
    next(error);
  }
};

// Get user preference by user ID
export const getUserPreferenceByUserId = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userPreference = await UserPreference.findOne({
      where: { user_id: req.params.userId }
    });

    if (!userPreference) {
      res.status(404).json({ message: 'User preference not found for this user' });
      return;
    }

    res.json(userPreference);
  } catch (error) {
    console.error(`Error getting user preference for user ID ${req.params.userId}:`, error);
    next(error);
  }
};

// Create a new user preference
export const createUserPreference = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      user_id,
      notifications_enabled = true,
      language = 'en',
      push_notifications_enabled = true,
      notify_event_cancellation = true,
      notify_event_changes = false,
      notify_contact_questions = true,
      notify_top_tier_events = true,
      email_notifications_enabled = true,
      participant_reminders_enabled = true,
      notify_dues_owed = true,
      notify_tickets_not_purchased = false,
      venmo_enabled = true,
      paypal_enabled = false,
      venmo_username,
      paypal_username
    } = req.body;

    const newUserPreference = await UserPreference.create({
      user_id,
      notifications_enabled,
      language,
      push_notifications_enabled,
      notify_event_cancellation,
      notify_event_changes,
      notify_contact_questions,
      notify_top_tier_events,
      email_notifications_enabled,
      participant_reminders_enabled,
      notify_dues_owed,
      notify_tickets_not_purchased,
      venmo_enabled,
      paypal_enabled,
      venmo_username,
      paypal_username,
      created_at: new Date(),
      updated_at: new Date()
    });

    res.status(201).json(newUserPreference);
  } catch (error) {
    console.error('Error creating user preference:', error);
    next(error);
  }
};

// Update a user preference
export const updateUserPreference = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      user_id,
      notifications_enabled,
      language,
      push_notifications_enabled,
      notify_event_cancellation,
      notify_event_changes,
      notify_contact_questions,
      notify_top_tier_events,
      email_notifications_enabled,
      participant_reminders_enabled,
      notify_dues_owed,
      notify_tickets_not_purchased,
      venmo_enabled,
      paypal_enabled,
      venmo_username,
      paypal_username
    } = req.body;

    const [updatedCount, updatedUserPreferences] = await UserPreference.update({
      user_id,
      notifications_enabled,
      language,
      push_notifications_enabled,
      notify_event_cancellation,
      notify_event_changes,
      notify_contact_questions,
      notify_top_tier_events,
      email_notifications_enabled,
      participant_reminders_enabled,
      notify_dues_owed,
      notify_tickets_not_purchased,
      venmo_enabled,
      paypal_enabled,
      venmo_username,
      paypal_username,
      updated_at: new Date()
    }, {
      where: { id: req.params.id },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'User preference not found' });
      return;
    }

    const updatedUserPreference = updatedUserPreferences[0];
    res.json(updatedUserPreference);
  } catch (error) {
    console.error(`Error updating user preference ${req.params.id}:`, error);
    next(error);
  }
};

// Delete a user preference
export const deleteUserPreference = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const deletedCount = await UserPreference.destroy({
      where: { id: req.params.id }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'User preference not found' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting user preference ${req.params.id}:`, error);
    next(error);
  }
};
