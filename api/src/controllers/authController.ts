import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import axios from 'axios';
import User, { AuthProvider } from '../models/User'; // Import AuthProvider from User model
import Session from '../models/Session';
import PasswordResetToken from '../models/PasswordResetToken';
import EmailVerificationToken from '../models/EmailVerificationToken';
import { checkAndCreateConnectRequestNotifications } from '../utils/connectRequestUtils';
import emailService from '../services/emailService';
import { loadTemplate } from '../utils/emailTemplateUtils';
import crypto from 'crypto';

// We'll initialize the Google OAuth client if needed later
// This is a placeholder for future token verification if needed

interface LoginRequest {
  email: string;
  password: string;
}

// Login
export const login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email, password }: LoginRequest = req.body;

    // Find the most recent enabled user with this email
    const user = await User.findOne({
      where: {
        email,
        enabled: true
      },
      order: [['created_at', 'DESC']]
    });

    if (!user || !user.password_hash) {
      res.status(401).json({ message: 'Invalid credentials' });
      return;
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
      res.status(401).json({ message: 'Invalid credentials' });
      return;
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'fallback_secret';
    const token = jwt.sign(
      { id: user.id, email: user.email, access_level: user.access_level },
      jwtSecret,
      { expiresIn: '24h' } // Extended to 24 hours
    );

    // Generate refresh token
    const refreshSecret = process.env.REFRESH_TOKEN_SECRET || 'fallback_refresh_secret';
    const refreshToken = jwt.sign(
      { id: user.id },
      refreshSecret,
      { expiresIn: '7d' }
    );

    // Store session
    await Session.create({
      user_id: user.id,
      session_token: token,
      user_agent: req.headers['user-agent'],
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    });

    res.json({
      message: 'Login successful',
      token,
      refreshToken,
      user: {
        id: user.id,
        display_name: user.display_name,
        email: user.email,
        access_level: user.access_level,
        profile_image_url: user.profile_image_url,
        email_verified: user.email_verified
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    next(error);
  }
};

interface RegisterRequest {
  display_name: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  provider?: AuthProvider;
}

// Register
export const register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      display_name,
      first_name,
      last_name,
      email,
      password,
      provider = 'google' // Changed to 'google' assuming it's a valid AuthProvider value
    }: RegisterRequest = req.body;

    // Check if an enabled user already exists with this email
    const existingEnabledUser = await User.findOne({
      where: {
        email,
        enabled: true
      }
    });

    if (existingEnabledUser) {
      // Account already exists and is enabled
      res.status(409).json({ message: 'Email already exists' });
      return;
    }

    // Always create a new user (even if disabled users exist with this email)
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    const user = await User.create({
      display_name,
      first_name,
      last_name,
      email,
      password_hash: passwordHash,
      provider,
      provider_user_id: email,
      access_level: 'user',
      enabled: true,           // Add missing required property
      auto_locate: false,      // Add missing required property
      email_verified: false    // New users need to verify their email
    });

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'fallback_secret';
    const token = jwt.sign(
      { id: user.id, email: user.email, access_level: user.access_level },
      jwtSecret,
      { expiresIn: '24h' } // Extended to 24 hours
    );

    // Generate refresh token
    const refreshSecret = process.env.REFRESH_TOKEN_SECRET || 'fallback_refresh_secret';
    const refreshToken = jwt.sign(
      { id: user.id },
      refreshSecret,
      { expiresIn: '7d' }
    );

    // Store session
    await Session.create({
      user_id: user.id,
      session_token: token,
      user_agent: req.headers['user-agent'],
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    });

    // Check for any pending connect requests for this email and create notifications
    try {
      await checkAndCreateConnectRequestNotifications(user.id, email);
    } catch (connectError) {
      console.error('Error checking connect requests during registration:', connectError);
      // Don't fail the registration if this fails
    }

    // Send email verification email
    try {
      const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

      // Create verification token record
      await EmailVerificationToken.create({
        user_id: user.id,
        token: verificationToken,
        expires_at: expiresAt
      });

      // Create verification URL
      const verificationUrl = `${API_BASE_URL}/api/auth/verify-email?token=${verificationToken}`;

      // Load email template
      const html = await loadTemplate('email-verification-template', {
        user_name: user.first_name || user.display_name || 'User',
        verification_url: verificationUrl,
        app_download_url: 'https://qwrm.app/download'
      });

      // Send verification email
      await emailService.sendEmail(
        user.email,
        'Verify Your Email Address - QWRM',
        html
      );

      console.log(`Verification email sent to ${user.email}`);
    } catch (emailError) {
      console.error('Error sending verification email during registration:', emailError);
      // Don't fail the registration if email sending fails
    }

    res.status(201).json({
      message: 'Registration successful',
      token,
      refreshToken,
      user: {
        id: user.id,
        display_name: user.display_name,
        email: user.email,
        access_level: user.access_level,
        profile_image_url: user.profile_image_url,
        email_verified: user.email_verified
      }
    });
  } catch (error: any) {
    console.error('Registration error:', error);
    next(error);
  }
};

// Logout
export const logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      // Invalidate session
      await Session.update(
        { expires_at: new Date() },
        { where: { session_token: token } }
      );
    }

    res.json({ message: 'Logout successful' });
  } catch (error) {
    console.error('Logout error:', error);
    next(error);
  }
};

interface RefreshTokenRequest {
  refreshToken: string;
}

// Refresh token
export const refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { refreshToken }: RefreshTokenRequest = req.body;

    if (!refreshToken) {
      res.status(401).json({ message: 'Refresh token required' });
      return;
    }

    const refreshSecret = process.env.REFRESH_TOKEN_SECRET || 'fallback_refresh_secret';

    try {
      const decoded = jwt.verify(refreshToken, refreshSecret) as { id: number };

      // Get user
      const user = await User.findByPk(decoded.id);

      if (!user) {
        res.status(404).json({ message: 'User not found' });
        return;
      }

      // Generate new access token
      const jwtSecret = process.env.JWT_SECRET || 'fallback_secret';
      const newToken = jwt.sign(
        { id: user.id, email: user.email, access_level: user.access_level },
        jwtSecret,
        { expiresIn: '24h' } // Extended to 24 hours
      );

      // Store new session
      await Session.create({
        user_id: user.id,
        session_token: newToken,
        user_agent: req.headers['user-agent'],
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
      });

      res.json({
        token: newToken
      });
    } catch (err) {
      console.error('Token verification error:', err);
      res.status(403).json({ message: 'Invalid refresh token' });
    }
  } catch (error) {
    console.error('Refresh token error:', error);
    next(error);
  }
};

// Google OAuth token verification and user authentication/creation
export const googleTokenAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { access_token, code_verifier, redirect_uri, platform } = req.body;

    if (!access_token) {
      res.status(400).json({ message: 'Authorization code is required' });
      return;
    }

    let email, name, firstName, lastName, googleId;

    // Select the correct client ID based on platform or try both
    let clientId: string | undefined;

    // If platform is specified, use the appropriate client ID
    if (platform === 'ios') {
      clientId = process.env.GOOGLE_IOS_OAUTH_CLIENT_ID;
    } else if (platform === 'android') {
      clientId = process.env.GOOGLE_ANDROID_OAUTH_CLIENT_ID;
    } else {
      // Fallback: try to determine from redirect_uri or use Android as default
      clientId = process.env.GOOGLE_ANDROID_OAUTH_CLIENT_ID || process.env.GOOGLE_IOS_OAUTH_CLIENT_ID;
    }

    if (!clientId) {
      console.error('No Google OAuth client ID configured');
      res.status(500).json({ message: 'OAuth configuration error' });
      return;
    }

    // Use the redirect URI sent from the frontend, or fallback to default
    const redirectUriToUse = redirect_uri || 'com.signupsheet://';

    console.log('Token exchange parameters:', {
      code: access_token?.substring(0, 20) + '...',
      client_id: clientId,
      redirect_uri: redirectUriToUse,
      has_code_verifier: !!code_verifier
    });

    let tokenRequestData: any = {
      code: access_token,
      client_id: clientId,
      redirect_uri: redirectUriToUse,
      grant_type: 'authorization_code'
    };

    // For PKCE flow (which is what we're using), we don't need client_secret
    // Include code_verifier for PKCE flow
    if (code_verifier) {
      tokenRequestData.code_verifier = code_verifier;
    } else {
      console.warn('No code_verifier provided for PKCE flow');
    }

    let actualAccessToken: string;
    try {
      const tokenResponse = await axios.post('https://oauth2.googleapis.com/token', tokenRequestData);
      actualAccessToken = tokenResponse.data.access_token;
    } catch (tokenError: any) {
      console.error('Token exchange failed:', tokenError.response?.data || tokenError.message);
      console.error('Full token error:', JSON.stringify(tokenError.response?.data, null, 2));
      res.status(400).json({
        message: 'Failed to exchange authorization code for access token',
        error: tokenError.response?.data?.error_description || 'Invalid authorization code'
      });
      return;
    }

    // Get user info from Google using the access token
    try {
      const googleUserInfo = await axios.get('https://www.googleapis.com/oauth2/v3/userinfo', {
        headers: { Authorization: `Bearer ${actualAccessToken}` }
      });

      ({
        email,
        name,
        given_name: firstName,
        family_name: lastName,
        sub: googleId
      } = googleUserInfo.data);

    } catch (userInfoError: any) {
      console.error('Failed to get user info from Google:', userInfoError.response?.data || userInfoError.message);
      res.status(400).json({
        message: 'Failed to verify Google token',
        error: userInfoError.response?.data?.error_description || 'Invalid access token'
      });
      return;
    }

    if (!email) {
      res.status(400).json({ message: 'Email not provided by Google' });
      return;
    }

    // Check if an enabled user already exists with this email
    let user = await User.findOne({
      where: {
        email,
        enabled: true
      }
    });
    let isNewUser = false;

    if (!user) {
      // Create new user (even if disabled users exist with this email)
      user = await User.create({
        display_name: name || email.split('@')[0],
        first_name: firstName || '',
        last_name: lastName || '',
        email,
        provider: 'google',
        provider_user_id: googleId,
        access_level: 'user',
        enabled: true,
        auto_locate: false,
        email_verified: true, // Google users are already verified
        access_token
      });
      isNewUser = true;
    } else {
      // Update existing enabled user's Google credentials
      await user.update({
        provider: 'google',
        provider_user_id: googleId,
        access_token
      });
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'fallback_secret';
    const token = jwt.sign(
      { id: user.id, email: user.email, access_level: user.access_level },
      jwtSecret,
      { expiresIn: '24h' }
    );

    // Generate refresh token
    const refreshSecret = process.env.REFRESH_TOKEN_SECRET || 'fallback_refresh_secret';
    const refreshToken = jwt.sign(
      { id: user.id },
      refreshSecret,
      { expiresIn: '7d' }
    );

    // Store session
    await Session.create({
      user_id: user.id,
      session_token: token,
      user_agent: req.headers['user-agent'],
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    });

    // If this is a new user, check for any pending connect requests for this email
    if (isNewUser) {
      try {
        await checkAndCreateConnectRequestNotifications(user.id, email);
      } catch (connectError) {
        console.error('Error checking connect requests during Google auth:', connectError);
        // Don't fail the authentication if this fails
      }
    }

    res.json({
      message: 'Google authentication successful',
      token,
      refreshToken,
      isNewUser,
      user: {
        id: user.id,
        display_name: user.display_name,
        email: user.email,
        access_level: user.access_level,
        profile_image_url: user.profile_image_url,
        email_verified: user.email_verified
      }
    });
  } catch (error) {
    console.error('Google authentication error:', error);
    next(error);
  }
};

// Legacy OAuth handlers (for future use with web flows)
export const googleAuth = (_req: Request, res: Response): void => {
  console.log('Google OAuth authentication requested');
  res.status(501).json({ message: 'Direct OAuth flow not implemented, use token authentication' });
};

export const googleAuthCallback = (_req: Request, res: Response): void => {
  console.log('Google OAuth callback requested');
  res.status(501).json({ message: 'Direct OAuth flow not implemented, use token authentication' });
};

export const facebookAuth = (_req: Request, res: Response): void => {
  console.log('Facebook OAuth authentication requested');
  res.status(501).json({ message: 'Facebook OAuth not implemented yet' });
};

export const facebookAuthCallback = (_req: Request, res: Response): void => {
  console.log('Facebook OAuth callback requested');
  res.status(501).json({ message: 'Facebook OAuth callback not implemented yet' });
};

// Forgot Password
export const forgotPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email }: { email: string } = req.body;

    if (!email) {
      res.status(400).json({ message: 'Email is required' });
      return;
    }

    // Check if user exists and is enabled
    const user = await User.findOne({
      where: {
        email,
        enabled: true
      }
    });

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user) {
      // Generate secure random token
      const token = crypto.randomBytes(32).toString('hex');

      // Set expiration to 1 hour from now
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000);

      // Store the reset token
      await PasswordResetToken.create({
        email,
        token,
        expires_at: expiresAt
      });

      // Create deep link URL
      const resetUrl = `qwrm://reset-password?email=${encodeURIComponent(email)}&token=${token}`;

      // Load the password reset email template
      const html = await loadTemplate('password-reset-template', {
        logo_url: 'https://api.qwrm.app/assets/qwrm-logo-yellow.png',
        reset_url: resetUrl,
        app_download_url: 'https://qwrm.app/download',
        app_store_url: 'https://apps.apple.com/app/qwrm',
        play_store_url: 'https://play.google.com/store/apps/details?id=com.signupsheet',
        app_store_button_url: 'https://api.qwrm.app/assets/app-store-button.png',
        play_store_button_url: 'https://api.qwrm.app/assets/play-store-button.png'
      });

      // Send the password reset email
      await emailService.sendEmail(
        email,
        'Reset Your QWRM Password',
        html
      );
    }

    res.json({ message: 'If an account with that email exists, we have sent a password reset link.' });
  } catch (error) {
    console.error('Forgot password error:', error);
    next(error);
  }
};

// Reset Password
export const resetPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { email, token, password }: { email: string; token: string; password: string } = req.body;

    if (!email || !token || !password) {
      res.status(400).json({ message: 'Email, token, and password are required' });
      return;
    }

    // Find the reset token
    const resetToken = await PasswordResetToken.findOne({
      where: {
        email,
        token,
        used: false
      }
    });

    if (!resetToken) {
      res.status(400).json({ message: 'Invalid or expired reset token' });
      return;
    }

    // Check if token is expired
    if (resetToken.isExpired()) {
      res.status(400).json({ message: 'Reset token has expired' });
      return;
    }

    // Find the user
    const user = await User.findOne({
      where: {
        email,
        enabled: true
      }
    });

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Hash the new password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Update user's password
    await user.update({
      password_hash: passwordHash
    });

    // Mark the reset token as used
    await resetToken.update({
      used: true
    });

    // Invalidate all existing sessions for this user
    await Session.destroy({
      where: {
        user_id: user.id
      }
    });

    res.json({ message: 'Password has been reset successfully' });
  } catch (error) {
    console.error('Reset password error:', error);
    next(error);
  }
};
