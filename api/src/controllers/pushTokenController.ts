import { Request, Response, NextFunction } from 'express';
import { Expo } from 'expo-server-sdk';
import PushToken from '../models/PushToken';
import { AuthRequest } from '../types';

// Register a push token for a user
export const registerPushToken = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { token, deviceId, deviceType } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Validate the token
    if (!Expo.isExpoPushToken(token)) {
      res.status(400).json({ message: 'Invalid Expo push token' });
      return;
    }

    // Check if the token already exists for this device
    const existingToken = await PushToken.findOne({
      where: {
        user_id: userId,
        device_id: deviceId
      }
    });

    if (existingToken) {
      // Update the existing token
      await PushToken.update(
        {
          token,
          device_type: deviceType,
          is_active: true,
          updated_at: new Date()
        },
        {
          where: {
            id: existingToken.id
          }
        }
      );

      res.status(200).json({ message: 'Push token updated successfully' });
    } else {
      // Create a new token
      const newToken = await PushToken.create({
        user_id: userId,
        token,
        device_id: deviceId,
        device_type: deviceType,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      });

      res.status(201).json({ message: 'Push token registered successfully', id: newToken.id });
    }
  } catch (error) {
    console.error('Error registering push token:', error);
    next(error);
  }
};

// Deactivate a push token
export const deactivatePushToken = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { deviceId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Find the token for this device
    const existingToken = await PushToken.findOne({
      where: {
        user_id: userId,
        device_id: deviceId
      }
    });

    if (!existingToken) {
      res.status(404).json({ message: 'Push token not found' });
      return;
    }

    // Deactivate the token
    await PushToken.update(
      {
        is_active: false,
        updated_at: new Date()
      },
      {
        where: {
          id: existingToken.id
        }
      }
    );

    res.status(200).json({ message: 'Push token deactivated successfully' });
  } catch (error) {
    console.error('Error deactivating push token:', error);
    next(error);
  }
};

// Get all push tokens for a user
export const getUserPushTokens = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Get all active tokens for this user
    const tokens = await PushToken.findAll({
      where: {
        user_id: userId,
        is_active: true
      }
    });

    res.status(200).json(tokens);
  } catch (error) {
    console.error('Error getting user push tokens:', error);
    next(error);
  }
};
