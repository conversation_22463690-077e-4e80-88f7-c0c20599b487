import { Request, Response, NextFunction } from 'express';
import Event from '../models/Event';
import { AuthRequest } from '../types';
import notificationUtils from '../utils/notificationUtils';

// Mark a user as having paid for an event
export const markAsPaid = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const userId = req.user?.id;

    // Validate event ID
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Validate user ID
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Check if the user is a participant
    const participants = event.participants || [];
    const isParticipant = participants.includes(userId);

    if (!isParticipant) {
      res.status(403).json({ message: 'You must be a participant to pay for this event' });
      return;
    }

    // Check if the user has already paid
    const paidParticipants = event.paid_participants || [];
    const hasAlreadyPaid = paidParticipants.includes(userId);

    if (hasAlreadyPaid) {
      res.status(200).json({ message: 'You have already paid for this event', success: true });
      return;
    }

    // Add the user to the paid_participants array
    const updatedPaidParticipants = [...paidParticipants, userId];

    // Update the event with the new paid_participants array
    await event.update({
      paid_participants: updatedPaidParticipants
    });

    // Notify the event owner that a participant has paid
    if (event.owner_id) {
      await notificationUtils.createNotificationWithPush(
        userId, // Current user is the sender
        event.owner_id, // Event owner is the receiver
        'Payment Received',
        'payment_received',
        `A participant has paid for your event "${event.name}"`,
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'event',
          params: { id: event.id }
        }
      );
    }

    res.status(200).json({ message: 'Payment marked as successful', success: true });
  } catch (error) {
    console.error(`Error marking payment for event ${req.params.eventId}:`, error);
    next(error);
  }
};

// Check if a user has paid for an event
export const checkPaymentStatus = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const userId = req.user?.id;

    // Validate event ID
    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Validate user ID
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Check if the user has paid
    const paidParticipants = event.paid_participants || [];
    const hasPaid = paidParticipants.includes(userId);

    res.status(200).json({ hasPaid });
  } catch (error) {
    console.error(`Error checking payment status for event ${req.params.eventId}:`, error);
    next(error);
  }
};
