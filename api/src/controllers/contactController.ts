import { Request, Response, NextFunction } from 'express';
import Contact from '../models/Contact';
import User from '../models/User';
import { AuthProvider, AccessLevel } from '../types';

interface AuthRequest extends Request {
  user?: Partial<User>;
}

// Get all contacts
export const getAllContacts = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const contacts = await Contact.findAll({
      where: {
        owner_id: ownerId
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'display_name', 'first_name', 'last_name', 'profile_image_url']
        }
      ]
    });

    res.json(contacts);
  } catch (error) {
    console.error('Error getting all contacts:', error);
    next(error);
  }
};

// Get contact by ID
export const getContactById = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const contact = await Contact.findOne({
      where: {
        id: req.params.id,
        owner_id: ownerId
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'display_name', 'first_name', 'last_name', 'profile_image_url']
        }
      ]
    });

    if (!contact) {
      res.status(404).json({ message: 'Contact not found' });
      return;
    }

    res.json(contact);
  } catch (error) {
    console.error(`Error getting contact by ID ${req.params.id}:`, error);
    next(error);
  }
};

// Create a new contact
export const createContact = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const {
      user_id,
      first_name,
      last_name,
      email,
      phone_number,
      is_priority = false,
      hide_events = false,
      is_blocked = false,
      enabled = true
    } = req.body;

    // Validate required fields
    if (!first_name) {
      res.status(400).json({ message: 'First name is required' });
      return;
    }

    // Create contact with the authenticated user's ID as owner_id
    const contactData: any = {
      user_id,
      first_name,
      last_name,
      phone_number,
      owner_id: ownerId, // Force owner_id to be the authenticated user's ID
      is_priority,
      hide_events,
      is_blocked,
      enabled,
      created_at: new Date(),
      updated_at: new Date()
    };

    // Only add email if it's provided and not empty
    if (email && email.trim() !== '') {
      contactData.email = email;

      // Check if an enabled user exists with this email
      const existingUser = await User.findOne({
        where: {
          email,
          enabled: true
        },
        order: [['created_at', 'DESC']]
      });

      if (existingUser) {
        // If a user with this email exists, set the contact's user_id to that user's id
        contactData.user_id = existingUser.id;
        console.log(`Found existing user with email ${email}, setting contact's user_id to ${existingUser.id}`);
      }
    }

    const newContact = await Contact.create(contactData);

    res.status(201).json(newContact);
  } catch (error: any) {
    console.error('Error creating contact:', error);

    if (error.name === 'SequelizeValidationError') {
      res.status(400).json({
        message: 'Validation error',
        errors: error.errors.map((e: any) => ({ field: e.path, message: e.message }))
      });
      return;
    }

    // Check for duplicate email error (this might still happen during race conditions)
    if (error.name === 'SequelizeUniqueConstraintError' && error.errors.some((e: any) => e.path === 'email')) {
      res.status(400).json({
        message: 'Validation error',
        errors: [{ field: 'email', message: 'You already have a contact with this email address' }]
      });
      return;
    }

    next(error);
  }
};

// Update a contact
export const updateContact = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const {
      user_id,
      first_name,
      last_name,
      email,
      phone_number,
      is_priority,
      hide_events,
      is_blocked,
      enabled
    } = req.body;

    // First verify the contact belongs to the user
    const contact = await Contact.findOne({
      where: {
        id: req.params.id,
        owner_id: ownerId
      }
    });

    if (!contact) {
      res.status(404).json({ message: 'Contact not found' });
      return;
    }

    // Prepare update data
    const updateData: any = {
      user_id,
      first_name,
      last_name,
      phone_number,
      is_priority,
      hide_events,
      is_blocked,
      enabled,
      updated_at: new Date()
    };

    // If email is provided, check if a user exists with this email
    if (email && email.trim() !== '') {
      updateData.email = email;

      // Check if a user exists with this email
      const existingUser = await User.findOne({ where: { email } });

      if (existingUser) {
        // If a user with this email exists, set the contact's user_id to that user's id
        updateData.user_id = existingUser.id;
        console.log(`Found existing user with email ${email}, setting contact's user_id to ${existingUser.id}`);
      }
    }

    const [updatedCount, updatedContacts] = await Contact.update(updateData, {
      where: {
        id: req.params.id,
        owner_id: ownerId
      },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'Contact not found' });
      return;
    }

    const updatedContact = updatedContacts[0];
    res.json(updatedContact);
  } catch (error: any) {
    console.error(`Error updating contact ${req.params.id}:`, error);

    if (error.name === 'SequelizeValidationError') {
      res.status(400).json({
        message: 'Validation error',
        errors: error.errors.map((e: any) => ({ field: e.path, message: e.message }))
      });
      return;
    }

    // Check for duplicate email error (this might still happen during race conditions)
    if (error.name === 'SequelizeUniqueConstraintError' && error.errors.some((e: any) => e.path === 'email')) {
      res.status(400).json({
        message: 'Validation error',
        errors: [{ field: 'email', message: 'You already have a contact with this email address' }]
      });
      return;
    }

    next(error);
  }
};

// Delete a contact
export const deleteContact = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const ownerId = req.user?.id;

    if (!ownerId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const deletedCount = await Contact.destroy({
      where: {
        id: req.params.id,
        owner_id: ownerId
      }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'Contact not found' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting contact ${req.params.id}:`, error);
    next(error);
  }
};
