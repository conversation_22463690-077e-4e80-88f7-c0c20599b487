import { Request, Response, NextFunction } from 'express';
import Notification from '../models/Notification';
import User from '../models/User';

// Get all notifications
export const getAllNotifications = async (_req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const notifications = await Notification.findAll();
    res.json(notifications);
  } catch (error) {
    console.error('Error getting all notifications:', error);
    next(error);
  }
};

// Get notifications for a specific user
export const getNotificationsByUserId = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.params.userId;

    // First, get all notifications for the user
    const notifications = await Notification.findAll({
      where: { receiver_id: userId },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'display_name', 'first_name', 'last_name', 'profile_image_url']
        }
      ],
      order: [['created_at', 'DESC']] // Newest first
    });

    // Create a list to store the enhanced notifications
    const enhancedNotifications = [];

    // For each notification, check if it has a connect_request_id and fetch the connect request status
    for (const notification of notifications) {
      // Use type assertion to allow adding properties that aren't in the model
      const notificationData = notification.toJSON() as any;

      // If this is a connection request notification with a connect_request_id, fetch the connect request status
      if (notification.notice_type === 'connection_request' && notification.connect_request_id) {
        try {
          // Import the ConnectRequest model here to avoid circular dependencies
          const ConnectRequest = require('../models/ConnectRequest').default;

          console.log(`Fetching connect request ${notification.connect_request_id} for notification ${notification.id}`);

          // Fetch the connect request
          const connectRequest = await ConnectRequest.findByPk(notification.connect_request_id);

          if (connectRequest) {
            console.log(`Found connect request ${connectRequest.id} with status: accepted=${connectRequest.is_accepted}, declined=${connectRequest.is_declined}`);

            // Add the connect request status to the notification data
            notificationData.connect_request_status = {
              is_accepted: connectRequest.is_accepted,
              is_declined: connectRequest.is_declined
            };

            console.log(`Added connect_request_status to notification ${notification.id}:`, notificationData.connect_request_status);
          } else {
            console.log(`Connect request ${notification.connect_request_id} not found for notification ${notification.id}`);
          }
        } catch (err) {
          console.error(`Error fetching connect request ${notification.connect_request_id}:`, err);
          // Continue even if there's an error fetching the connect request
        }
      }

      enhancedNotifications.push(notificationData);
    }

    res.json(enhancedNotifications);
  } catch (error) {
    console.error(`Error getting notifications for user ${req.params.userId}:`, error);
    next(error);
  }
};

// Get notification by ID
export const getNotificationById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const notification = await Notification.findByPk(req.params.id);

    if (!notification) {
      res.status(404).json({ message: 'Notification not found' });
      return;
    }

    res.json(notification);
  } catch (error) {
    console.error(`Error getting notification by ID ${req.params.id}:`, error);
    next(error);
  }
};

// Create a new notification
export const createNotification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      sender_id,
      receiver_id,
      subject,
      notice_type,
      message,
      read = false
    } = req.body;

    const newNotification = await Notification.create({
      sender_id,
      receiver_id,
      subject,
      notice_type,
      message,
      read,
      created_at: new Date(),
      updated_at: new Date()
    });

    res.status(201).json(newNotification);
  } catch (error) {
    console.error('Error creating notification:', error);
    next(error);
  }
};

// Update a notification
export const updateNotification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      sender_id,
      receiver_id,
      subject,
      notice_type,
      message,
      read
    } = req.body;

    const [updatedCount, updatedNotifications] = await Notification.update({
      sender_id,
      receiver_id,
      subject,
      notice_type,
      message,
      read,
      updated_at: new Date()
    }, {
      where: { id: req.params.id },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'Notification not found' });
      return;
    }

    const updatedNotification = updatedNotifications[0];
    res.json(updatedNotification);
  } catch (error) {
    console.error(`Error updating notification ${req.params.id}:`, error);
    next(error);
  }
};

// Mark notification as read
export const markNotificationAsRead = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const [updatedCount, updatedNotifications] = await Notification.update({
      read: true,
      updated_at: new Date()
    }, {
      where: { id: req.params.id },
      returning: true
    });

    if (updatedCount === 0) {
      res.status(404).json({ message: 'Notification not found' });
      return;
    }

    const updatedNotification = updatedNotifications[0];
    res.json(updatedNotification);
  } catch (error) {
    console.error(`Error marking notification ${req.params.id} as read:`, error);
    next(error);
  }
};

// Delete a notification
export const deleteNotification = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const deletedCount = await Notification.destroy({
      where: { id: req.params.id }
    });

    if (deletedCount === 0) {
      res.status(404).json({ message: 'Notification not found' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting notification ${req.params.id}:`, error);
    next(error);
  }
};
