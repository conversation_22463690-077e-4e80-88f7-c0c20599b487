import { Request, Response, NextFunction } from 'express';
import Message from '../models/Message';
import User from '../models/User';
import Event from '../models/Event';
import { AuthRequest } from '../types';
import notificationUtils from '../utils/notificationUtils';

// Get all messages for an event
export const getEventMessages = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);

    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    // Get messages for the event, ordered by creation time
    const messages = await Message.findAll({
      where: { event_id: eventId },
      order: [['created_at', 'ASC']],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'display_name', 'first_name', 'last_name', 'profile_image_url']
        }
      ]
    });

    res.json(messages);
  } catch (error) {
    console.error('Error getting event messages:', error);
    next(error);
  }
};

// Create a new message
export const createMessage = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const eventId = parseInt(req.params.eventId);
    const { text } = req.body;
    const userId = req.user?.id; // Get user ID from authenticated request

    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    if (!text || typeof text !== 'string' || text.trim() === '') {
      res.status(400).json({ message: 'Message text is required' });
      return;
    }

    // Validate user ID
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Get the event to access participants
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Get the user who is posting the message
    const user = await User.findByPk(userId);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Create the message
    const newMessage = await Message.create({
      text,
      user_id: userId,
      event_id: eventId
    });

    // Fetch the created message with user information
    const messageWithUser = await Message.findByPk(newMessage.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'display_name', 'first_name', 'last_name', 'profile_image_url']
        }
      ]
    });

    // Get all participants to notify them about the new message
    const participants = event.participants || [];

    // Only notify participants who are not the message sender
    const participantUserIds = participants
      .filter(id => id !== userId);

    if (participantUserIds.length > 0) {
      // Get the user's display name
      const senderName = user.display_name || 'Someone';

      // Send notifications to all participants
      await notificationUtils.createNotificationsWithPush(
        userId, // Current user is the sender
        participantUserIds,
        'New Message',
        'new_comment',
        `${senderName} posted a message in "${event.name}": ${text.length > 30 ? text.substring(0, 30) + '...' : text}`,
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'event',
          params: { id: event.id }
        }
      );
    }

    res.status(201).json(messageWithUser);
  } catch (error) {
    console.error('Error creating message:', error);
    next(error);
  }
};
