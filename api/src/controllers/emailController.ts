import { Request, Response, NextFunction } from 'express';
import emailService from '../services/emailService';
import { loadTemplate, generateConnectionRequestEmail, generateEventInvitationEmail } from '../utils/emailTemplateUtils';
import User from '../models/User';
import { isS3Url, getSignedImageUrl } from '../utils/imageUtils';

// Deep link scheme for the app
const DEEP_LINK_SCHEME = 'qwrm://';

/**
 * Send a test email
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const sendTestEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { to } = req.body;

    if (!to) {
      res.status(400).json({ message: 'Recipient email is required' });
      return;
    }

    // Generate test email content with deep link
    const html = await generateConnectionRequestEmail(
      'Test User',
      'https://via.placeholder.com/120',
      'This is a test connection request.',
      `${DEEP_LINK_SCHEME}notifications`,
      'https://qwrm.app/download'
    );

    // Send the email
    await emailService.sendEmail(to, 'QWRM Test Email', html);

    res.status(200).json({ message: 'Test email sent successfully' });
  } catch (error) {
    console.error('Error sending test email:', error);
    next(error);
  }
};

/**
 * Send a connection request email
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const sendConnectionRequestEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, recipientId, requestUrl, appDownloadUrl } = req.body;

    if (!userId || !recipientId || !requestUrl) {
      res.status(400).json({ message: 'Missing required fields' });
      return;
    }

    // Get user information
    const sender = await User.findByPk(userId);
    const recipient = await User.findByPk(recipientId);

    if (!sender || !recipient) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Get sender's profile image URL or use placeholder
    let profileImageUrl = sender.profile_image_url || 'https://via.placeholder.com/120';
    console.log('Connection request controller - Original image URL:', profileImageUrl);

    // Sign the image URL if it's from S3
    if (profileImageUrl && isS3Url(profileImageUrl)) {
      profileImageUrl = getSignedImageUrl(profileImageUrl);
      console.log('Connection request controller - Signed image URL:', profileImageUrl);
    }

    // Create message for the email
    const message = `${sender.display_name} wants to connect.`;

    // Use deep link for notifications screen
    const deepLinkUrl = `${DEEP_LINK_SCHEME}notifications`;

    // Generate email content
    const html = await generateConnectionRequestEmail(
      sender.display_name,
      profileImageUrl,
      message,
      deepLinkUrl,
      appDownloadUrl || 'https://qwrm.app/download'
    );

    // Send the email
    await emailService.sendEmailToUser(
      recipientId,
      `${sender.display_name} wants to connect on QWRM`,
      html
    );

    res.status(200).json({ message: 'Connection request email sent successfully' });
  } catch (error) {
    console.error('Error sending connection request email:', error);
    next(error);
  }
};

/**
 * Send a test event invitation email
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const sendTestEventInvitationEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { to } = req.body;

    if (!to) {
      res.status(400).json({ message: 'Recipient email is required' });
      return;
    }

    // Generate test event invitation email content
    const html = await generateEventInvitationEmail(
      'Test Event',
      'Test Host',
      'Saturday, January 1, 2024 at 7:00 PM',
      'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png',
      123,
      'https://qwrm.app/download'
    );

    // Send the email
    await emailService.sendEmail(to, 'Test Host invited you to Test Event', html);

    res.status(200).json({ message: 'Test event invitation email sent successfully' });
  } catch (error) {
    console.error('Error sending test event invitation email:', error);
    next(error);
  }
};

/**
 * Send a custom email using a template
 * @param req Request object
 * @param res Response object
 * @param next Next function
 */
export const sendCustomEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { to, subject, templateName, replacements } = req.body;

    if (!to || !subject || !templateName || !replacements) {
      res.status(400).json({ message: 'Missing required fields' });
      return;
    }

    // Load the template with replacements
    const html = await loadTemplate(templateName, replacements);

    // Send the email
    await emailService.sendEmail(to, subject, html);

    res.status(200).json({ message: 'Custom email sent successfully' });
  } catch (error) {
    console.error('Error sending custom email:', error);
    next(error);
  }
};

export default {
  sendTestEmail,
  sendConnectionRequestEmail,
  sendTestEventInvitationEmail,
  sendCustomEmail
};
