<!DOCTYPE html>
<html>
<head>
  <title>Opening QWRM...</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script>
    const isiOS = /iPhone|iPad|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    const appScheme = 'qwrm://notifications';
    const appStoreURL = 'https://apps.apple.com/us/app/qwrm/id6450262881';
    const playStoreURL = 'https://play.google.com/store/apps/details?id=com.signupsheet';

    window.onload = function () {
      const fallbackTimeout = setTimeout(() => {
        if (isiOS) window.location = appStoreURL;
        else if (isAndroid) window.location = playStoreURL;
      }, 1500); // 1.5s gives time for scheme to open if app is installed

      window.location = appScheme;
    };
  </script>
</head>
<body>
  <p>Opening QWRM...</p>
</body>
</html>