import express from 'express';
import { testEmailService, testNotificationWithEmail, testEmailWithImage, testEventNotificationEmail, testDateConfirmedNotification, testEmailWithLogo, testQuorumNotification, testDateConfirmedNotification2, testFlexibleDateNotification, testDateConfirmedEmail, testSendGridDirect, testImageUrlSigning, testDuplicateEmailPrevention, testEmailVerification } from '../controllers/testController';

const router = express.Router();

// Test email service
router.get('/email', testEmailService);

// Test notification with email
router.get('/notification-email', testNotificationWithEmail);

// Test email with specific image URL
router.get('/email-with-image', testEmailWithImage);

// Test event notification email
router.get('/event-notification-email', testEventNotificationEmail);

// Test date confirmed notification
router.get('/date-confirmed-notification', testDateConfirmedNotification);

// Test email with logo
router.get('/email-with-logo', testEmailWithLogo);

// Test quorum notification
router.get('/quorum-notification', testQuorumNotification);

// Test date confirmed notification (direct)
router.get('/date-confirmed-notification2', testDateConfirmedNotification2);

// Test flexible date notification
router.get('/flexible-date-notification', testFlexibleDateNotification);

// Test date confirmed email
router.get('/date-confirmed-email', testDateConfirmedEmail);

// Test SendGrid direct email
router.get('/sendgrid-direct', testSendGridDirect);

// Test image URL signing for email notifications
router.get('/image-url-signing', testImageUrlSigning);

// Test duplicate email prevention
router.get('/duplicate-email-prevention', testDuplicateEmailPrevention);

// Test email verification
router.get('/email-verification', testEmailVerification);

export default router;
