import express from 'express';
import {
  getEventParticipants,
  signUpForEvent,
  updateParticipationStatus,
  leaveEvent,
  inviteToEvent
} from '../controllers/eventParticipantController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all participants for an event
router.get('/events/:eventId/participants', authenticateToken, (req, res, next) => getEventParticipants(req, res, next));

// Sign up for an event
router.post('/events/:eventId/signup', authenticateToken, (req, res, next) => signUpForEvent(req as any, res, next));

// Update participation status
router.put('/events/:eventId/participation', authenticateToken, (req, res, next) => updateParticipationStatus(req as any, res, next));

// Leave an event
router.delete('/events/:eventId/participation', authenticateToken, (req, res, next) => leaveEvent(req as any, res, next));

// Invite a user to an event
router.post('/events/:eventId/invite', authenticateToken, (req, res, next) => inviteToEvent(req as any, res, next));

export default router;
