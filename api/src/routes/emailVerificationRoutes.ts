import express from 'express';
import { 
  sendVerificationEmail, 
  verifyEmail, 
  checkVerificationStatus, 
  resendVerificationEmail 
} from '../controllers/emailVerificationController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Send verification email (public endpoint)
router.post('/send-verification', sendVerificationEmail);

// Verify email using token (public endpoint - accessed via email link)
router.get('/verify-email', verifyEmail);

// Check verification status (authenticated endpoint)
router.get('/verification-status', authenticateToken, checkVerificationStatus);

// Resend verification email (authenticated endpoint)
router.post('/resend-verification', authenticateToken, resendVerificationEmail);

export default router;
