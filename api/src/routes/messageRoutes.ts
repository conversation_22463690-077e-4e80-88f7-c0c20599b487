import express from 'express';
import {
  getEventMessages,
  createMessage
} from '../controllers/messageController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all messages for an event
router.get('/events/:eventId/messages', authenticateToken, getEventMessages);

// Create a new message for an event
router.post('/events/:eventId/messages', authenticateToken, createMessage);

export default router;
