import express from 'express';
import {
  getAllNotifications,
  getNotificationById,
  getNotificationsByUserId,
  createNotification,
  updateNotification,
  markNotificationAsRead,
  deleteNotification
} from '../controllers/notificationController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all notifications
router.get('/', authenticateToken, getAllNotifications);

// Get notifications for a specific user
router.get('/user/:userId', authenticateToken, getNotificationsByUserId);

// Get notification by ID
router.get('/:id', authenticateToken, getNotificationById);

// Create a new notification
router.post('/', authenticateToken, createNotification);

// Update a notification
router.put('/:id', authenticateToken, updateNotification);

// Mark notification as read
router.patch('/:id/read', authenticateToken, markNotificationAsRead);
router.post('/:id/read', authenticateToken, markNotificationAsRead); // Add POST endpoint for compatibility

// Delete a notification
router.delete('/:id', authenticateToken, deleteNotification);

export default router;
