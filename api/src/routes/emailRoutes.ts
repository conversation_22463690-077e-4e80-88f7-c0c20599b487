import express from 'express';
import { sendTestEmail, sendConnectionRequestEmail, sendTestEventInvitationEmail, sendCustomEmail } from '../controllers/emailController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Send a test email
router.post('/test', authenticateToken, sendTestEmail);

// Send a connection request email
router.post('/connection-request', authenticateToken, sendConnectionRequestEmail);

// Send a test event invitation email
router.post('/test-event-invitation', authenticateToken, sendTestEventInvitationEmail);

// Send a custom email using a template
router.post('/custom', authenticateToken, sendCustomEmail);

export default router;
