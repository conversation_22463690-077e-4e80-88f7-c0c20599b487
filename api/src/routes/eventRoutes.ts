import express from 'express';
import {
  getAllEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  sendEventInvitationEmailsToEmailAddresses
} from '../controllers/eventController';
import { markAsPaid, checkPaymentStatus } from '../controllers/paymentController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all events
router.get('/', authenticateToken, getAllEvents);

// Get event by ID
router.get('/:id', authenticateToken, getEventById);

// Create a new event
router.post('/', authenticateToken, createEvent);

// Update an event
router.put('/:id', authenticateToken, updateEvent);

// Delete an event
router.delete('/:id', authenticateToken, deleteEvent);

// Mark a user as having paid for an event
router.post('/:eventId/mark-paid', authenticateToken, markAsPaid);

// Check if a user has paid for an event
router.get('/:eventId/payment-status', authenticateToken, checkPaymentStatus);

// Send event invitation emails to email addresses (for non-users)
router.post('/:id/invite-emails', authenticateToken, sendEventInvitationEmailsToEmailAddresses);

export default router;
