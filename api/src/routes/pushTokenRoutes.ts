import express from 'express';
import {
  registerPushToken,
  deactivatePushToken,
  getUserPushTokens
} from '../controllers/pushTokenController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Register a push token
router.post('/register', authenticateToken, registerPushToken);

// Deactivate a push token
router.post('/deactivate', authenticateToken, deactivatePushToken);

// Get all push tokens for a user
router.get('/user', authenticateToken, getUserPushTokens);

export default router;
