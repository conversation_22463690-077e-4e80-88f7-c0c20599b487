import express from 'express';
import userRoutes from './userRoutes';
import authRoutes from './authRoutes';
import eventRoutes from './eventRoutes';
import contactRoutes from './contactRoutes';
import notificationRoutes from './notificationRoutes';
import userPreferenceRoutes from './userPreferenceRoutes';
import groupRoutes from './groupRoutes';
import eventParticipantRoutes from './eventParticipantRoutes';
import eventDateRoutes from './eventDateRoutes';
import messageRoutes from './messageRoutes';
import linkExtractorRoutes from './linkExtractorRoutes';
import uploadRoutes from './uploadRoutes';
import pushTokenRoutes from './pushTokenRoutes';
import emailRoutes from './emailRoutes';
import emailVerificationRoutes from './emailVerificationRoutes';
import testRoutes from './testRoutes';
import connectRequestRoutes from './connectRequestRoutes';

const router = express.Router();

// Mount routes
router.use('/users', userRoutes);
router.use('/auth', authRoutes);
router.use('/events', eventRoutes);
router.use('/contacts', contactRoutes);
router.use('/notifications', notificationRoutes);
router.use('/user-preferences', userPreferenceRoutes);
router.use('/groups', groupRoutes);
router.use('/participants', eventParticipantRoutes);
router.use('/event-dates', eventDateRoutes);
router.use('/messages', messageRoutes);
router.use('/uploads', uploadRoutes);
router.use('/push-tokens', pushTokenRoutes);
router.use('/emails', emailRoutes);
router.use('/auth', emailVerificationRoutes);
router.use('/test', testRoutes);
router.use('/connect-requests', connectRequestRoutes);
router.use('/', linkExtractorRoutes); // Mount link extractor routes at root level

export { router };
