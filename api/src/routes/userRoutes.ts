import express from 'express';
import { authenticateToken } from '../middleware/auth';
import * as userController from '../controllers/userController';

const router = express.Router();

// GET /api/users - Get all users (admin only)
router.get('/', authenticateToken, userController.getAllUsers);

// GET /api/users/:id - Get a user by ID
router.get('/:id', authenticateToken, userController.getUserById);

// POST /api/users - Create a new user
router.post('/', userController.createUser);

// PUT /api/users/:id - Update a user
router.put('/:id', authenticateToken, userController.updateUser);

// DELETE /api/users/:id - Delete a user
router.delete('/:id', authenticateToken, userController.deleteUser);

export default router;
