import express from 'express';
import {
  getAllConnectRequests,
  getConnectRequestById,
  createConnectRequest,
  acceptConnectRequest,
  declineConnectRequest,
  deleteConnectRequest
} from '../controllers/connectRequestController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all connect requests
router.get('/', authenticateToken, getAllConnectRequests);

// Get connect request by ID
router.get('/:id', authenticateToken, getConnectRequestById);

// Create a new connect request
router.post('/', authenticateToken, createConnectRequest);

// Accept a connect request
router.post('/:id/accept', authenticateToken, acceptConnectRequest);

// Decline a connect request
router.post('/:id/decline', authenticateToken, declineConnectRequest);

// Delete a connect request
router.delete('/:id', authenticateToken, deleteConnectRequest);

export default router;
