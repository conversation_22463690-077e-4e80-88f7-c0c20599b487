import express from 'express';
import {
  getAllContacts,
  getContactById,
  createContact,
  updateContact,
  deleteContact
} from '../controllers/contactController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all contacts
router.get('/', authenticateToken, getAllContacts);

// Get contact by ID
router.get('/:id', authenticateToken, getContactById);

// Create a new contact
router.post('/', authenticateToken, createContact);

// Update a contact
router.put('/:id', authenticateToken, updateContact);

// Delete a contact
router.delete('/:id', authenticateToken, deleteContact);

export default router;
