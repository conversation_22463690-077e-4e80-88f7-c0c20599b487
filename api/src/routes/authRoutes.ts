import express from 'express';
import * as authController from '../controllers/authController';

const router = express.Router();

// POST /api/auth/login - Login
router.post('/login', authController.login);

// POST /api/auth/register - Register
router.post('/register', authController.register);

// POST /api/auth/logout - Logout
router.post('/logout', authController.logout);

// POST /api/auth/refresh-token - Refresh token
router.post('/refresh-token', authController.refreshToken);

// POST /api/auth/google/token - Google token authentication
router.post('/google/token', authController.googleTokenAuth);

// GET /api/auth/google - Google OAuth login (legacy/web flow)
router.get('/google', authController.googleAuth);

// POST /api/auth/forgot-password - Forgot password
router.post('/forgot-password', authController.forgotPassword);

// POST /api/auth/reset-password - Reset password
router.post('/reset-password', authController.resetPassword);

// GET /api/auth/google/callback - Google OAuth callback (legacy/web flow)
router.get('/google/callback', authController.googleAuthCallback);

// GET /api/auth/facebook - Facebook OAuth login
router.get('/facebook', authController.facebookAuth);

// GET /api/auth/facebook/callback - Facebook OAuth callback
router.get('/facebook/callback', authController.facebookAuthCallback);

export default router;
