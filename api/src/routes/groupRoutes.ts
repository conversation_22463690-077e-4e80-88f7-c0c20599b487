import express from 'express';
import {
  getAllGroups,
  getGroupById,
  createGroup,
  updateGroup,
  deleteGroup,
  updateGroupOrder
} from '../controllers/groupController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all groups
router.get('/', authenticateToken, getAllGroups);

// Update group order (must come before /:id routes)
router.put('/order', authenticateToken, updateGroupOrder);

// Get group by ID
router.get('/:id', authenticateToken, getGroupById);

// Create a new group
router.post('/', authenticateToken, createGroup);

// Update a group
router.put('/:id', authenticateToken, updateGroup);

// Delete a group
router.delete('/:id', authenticateToken, deleteGroup);

export default router;
