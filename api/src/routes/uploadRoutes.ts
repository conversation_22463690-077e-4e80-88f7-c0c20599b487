import express from 'express';
import { getPresignedUrl, uploadToS3, upload, getSignedObjectUrl } from '../controllers/uploadController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get a presigned URL for uploading a file to S3
router.post('/presigned-url', authenticateToken, getPresignedUrl);

// Get a signed URL for an existing S3 object
router.get('/signed-url', authenticateToken, getSignedObjectUrl);

// Upload a file directly to S3 from the server
// Use type assertion to fix TypeScript compatibility issues
router.post('/direct', authenticateToken, upload.single('file') as any, uploadToS3);

export default router;
