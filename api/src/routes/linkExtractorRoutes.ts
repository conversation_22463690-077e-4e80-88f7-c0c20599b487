import express from 'express';
import { extractEventFromLink } from '../controllers/linkExtractorController';
import { authenticateToken } from '../middleware/auth';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// Extract event information from a link
router.post('/extract-from-link', authenticateToken, extractEventFromLink);

// Serve the deep link HTML page
router.get('/open', (req, res) => {
  const redirectUrl = req.query.redirect as string || 'qwrm://notifications';
  
  // Read the template
  let html = fs.readFileSync(path.join(__dirname, '../views/deeplink.html'), 'utf8');
  
  // Replace the default app scheme with the redirect URL
  html = html.replace('qwrm://notifications', redirectUrl);
  
  res.send(html);
});

export default router;
