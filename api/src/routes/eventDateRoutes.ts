import express from 'express';
import { authenticateToken } from '../middleware/auth';
import EventDate from '../models/EventDate';
import Event from '../models/Event';
import { AuthRequest } from '../types';
import { Op } from 'sequelize';
import * as notificationUtils from '../utils/notificationUtils';
import { formatNotificationDate } from '../utils/dateUtils';

const router = express.Router();

// Get all dates for an event
router.get('/events/:eventId/dates', authenticateToken, async (req, res, next) => {
  try {
    const eventId = parseInt(req.params.eventId);

    if (isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid event ID' });
      return;
    }

    const eventDates = await EventDate.findAll({
      where: { event_id: eventId },
      order: [['date', 'ASC']]
    });

    res.json(eventDates);
  } catch (error) {
    console.error(`Error getting dates for event ${req.params.eventId}:`, error);
    next(error);
  }
});

// Sign up for a specific date
router.post('/events/:eventId/dates/:dateId/signup', authenticateToken, async (req: AuthRequest, res, next) => {
  try {
    const eventId = parseInt(req.params.eventId);
    const dateId = parseInt(req.params.dateId);
    const userId = req.user?.id;

    if (isNaN(eventId) || isNaN(dateId)) {
      res.status(400).json({ message: 'Invalid event ID or date ID' });
      return;
    }

    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event date
    const eventDate = await EventDate.findOne({
      where: { id: dateId, event_id: eventId }
    });

    if (!eventDate) {
      res.status(404).json({ message: 'Event date not found' });
      return;
    }

    // Check if the user is already a participant for this date
    const participants = eventDate.participants || [];
    if (participants.includes(userId)) {
      res.status(400).json({ message: 'User is already signed up for this date' });
      return;
    }

    // Add the user to the participants for this date
    const updatedParticipants = [...participants, userId];
    await eventDate.update({
      participants: updatedParticipants
    });

    res.json({
      success: true,
      message: 'Successfully signed up for the date',
      participants: updatedParticipants
    });
  } catch (error) {
    console.error(`Error signing up for date ${req.params.dateId} of event ${req.params.eventId}:`, error);
    next(error);
  }
});

// Sign up for multiple dates
router.post('/events/:eventId/signup-multiple', authenticateToken, async (req: AuthRequest, res, next) => {
  try {
    const eventId = parseInt(req.params.eventId);
    const { dateIds } = req.body;
    const userId = req.user?.id;

    if (isNaN(eventId) || !Array.isArray(dateIds) || dateIds.length === 0) {
      res.status(400).json({ message: 'Invalid event ID or date IDs' });
      return;
    }

    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event
    const event = await Event.findByPk(eventId);

    if (!event) {
      res.status(404).json({ message: 'Event not found' });
      return;
    }

    // Find all the event dates
    const eventDates = await EventDate.findAll({
      where: {
        id: { [Op.in]: dateIds },
        event_id: eventId
      }
    });

    if (eventDates.length === 0) {
      res.status(404).json({ message: 'No valid event dates found' });
      return;
    }

    // Add the user to the participants for each date
    for (const eventDate of eventDates) {
      const participants = eventDate.participants || [];
      if (!participants.includes(userId)) {
        const updatedParticipants = [...participants, userId];
        await eventDate.update({
          participants: updatedParticipants
        });
      }
    }

    // Add the user to the event participants if not already there
    const eventParticipants = event.participants || [];
    if (!eventParticipants.includes(userId)) {
      const updatedEventParticipants = [...eventParticipants, userId];
      await event.update({
        participants: updatedEventParticipants,
        participants_count: updatedEventParticipants.length
      });
    }

    // Check if any date has met quorum
    const quorum = event.quorum || 1;
    let quorumMet = false;
    let confirmedDate = null;

    // Reload event dates to get the updated participants
    const updatedEventDates = await EventDate.findAll({
      where: { event_id: eventId }
    });

    for (const date of updatedEventDates) {
      if (date.participants.length >= quorum) {
        quorumMet = true;
        confirmedDate = date.date;
        break;
      }
    }

    // Update the event if quorum is met
    console.log(`Event ${event.id} - Quorum check in event date routes: quorumMet=${quorumMet}, current quorum_met=${event.quorum_met}, multiple_dates=${event.multiple_dates}`);
    if (quorumMet && !event.quorum_met) {
      // Get all participants
      const participantIds = event.participants || [];

      // Send quorum reached notification for ALL events when quorum is met
      console.log(`Event ${event.id} - Quorum just met! Sending quorum notifications to ${participantIds.length} participants`);
      await notificationUtils.createNotificationsWithPush(
        userId, // Current user is the sender
        participantIds,
        'Event Quorum Reached',
        'event_quorum_reached',
        `The event "${event.name}" has reached its quorum of ${event.quorum} participants!`,
        {
          eventId: event.id,
          eventName: event.name,
          eventImageUrl: event.image_url, // Include the event image URL
          screen: 'event',
          params: { id: event.id }
        }
      );

      // Only set confirmed_date if we have a valid date
      if (confirmedDate) {
        await event.update({
          quorum_met: true,
          confirmed_date: confirmedDate
        });

        // Send date confirmed notification if we have multiple possible dates (flexible dates)
        // Get the count of event dates to determine if this is a flexible date event
        const eventDatesCount = await EventDate.count({ where: { event_id: eventId } });
        console.log(`Event ${event.id} - Confirmed date set: ${confirmedDate}, eventDatesCount=${eventDatesCount}`);

        // If we have more than one date, this is a flexible date event
        if (eventDatesCount > 1) {
          // Format the confirmed date for the notification
          const formattedDate = formatNotificationDate(confirmedDate);

          // Send date confirmed notifications to all participants
          console.log(`Event ${event.id} - Sending date confirmed notifications to ${participantIds.length} participants`);
          await notificationUtils.createNotificationsWithPush(
            userId, // Current user is the sender
            participantIds,
            'Date Confirmed',
            'event_date_confirmed',
            `The date for "${event.name}" has been confirmed: ${formattedDate}`,
            {
              eventId: event.id,
              eventName: event.name,
              eventImageUrl: event.image_url, // Include the event image URL
              screen: 'event',
              params: { id: event.id }
            }
          );

          console.log(`Sent date confirmed notifications to ${participantIds.length} participants for event ${event.id}`);
        }
      } else {
        await event.update({
          quorum_met: true
        });
      }
    }

    // Fetch the updated event with its dates
    const updatedEvent = await Event.findByPk(eventId, {
      include: [{
        model: EventDate,
        as: 'eventDates',
        attributes: ['id', 'date', 'participants']
      }]
    });

    // Convert event to JSON and add dates in the expected format
    const eventJSON: any = updatedEvent?.toJSON();
    eventJSON.date = eventJSON.eventDates?.map((eventDate: any) => eventDate.date) || [];

    res.json({
      success: true,
      message: 'Successfully signed up for the selected dates',
      updatedEvent: eventJSON
    });
  } catch (error) {
    console.error(`Error signing up for multiple dates of event ${req.params.eventId}:`, error);
    next(error);
  }
});

// Leave a specific date
router.delete('/events/:eventId/dates/:dateId/signup', authenticateToken, async (req: AuthRequest, res, next) => {
  try {
    const eventId = parseInt(req.params.eventId);
    const dateId = parseInt(req.params.dateId);
    const userId = req.user?.id;

    if (isNaN(eventId) || isNaN(dateId)) {
      res.status(400).json({ message: 'Invalid event ID or date ID' });
      return;
    }

    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }

    // Find the event date
    const eventDate = await EventDate.findOne({
      where: { id: dateId, event_id: eventId }
    });

    if (!eventDate) {
      res.status(404).json({ message: 'Event date not found' });
      return;
    }

    // Check if the user is a participant for this date
    const participants = eventDate.participants || [];
    if (!participants.includes(userId)) {
      res.status(400).json({ message: 'User is not signed up for this date' });
      return;
    }

    // Remove the user from the participants for this date
    const updatedParticipants = participants.filter(id => id !== userId);
    await eventDate.update({
      participants: updatedParticipants
    });

    res.json({
      success: true,
      message: 'Successfully left the date',
      participants: updatedParticipants
    });
  } catch (error) {
    console.error(`Error leaving date ${req.params.dateId} of event ${req.params.eventId}:`, error);
    next(error);
  }
});

export default router;
