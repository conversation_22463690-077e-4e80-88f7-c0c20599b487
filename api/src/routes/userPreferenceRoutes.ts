import express from 'express';
import {
  getAllUserPreferences,
  getUserPreferenceById,
  getUserPreferenceByUserId,
  createUserPreference,
  updateUserPreference,
  deleteUserPreference
} from '../controllers/userPreferenceController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Get all user preferences
router.get('/', authenticateToken, getAllUserPreferences);

// Get user preference by ID
router.get('/:id', authenticateToken, getUserPreferenceById);

// Get user preference by user ID
router.get('/user/:userId', authenticateToken, getUserPreferenceByUserId);

// Create a new user preference
router.post('/', authenticateToken, createUserPreference);

// Update a user preference
router.put('/:id', authenticateToken, updateUserPreference);

// Delete a user preference
router.delete('/:id', authenticateToken, deleteUserPreference);

export default router;
