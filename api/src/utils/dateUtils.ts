/**
 * Format a date for display
 * @param date Date to format
 * @param timezone Optional timezone (defaults to UTC)
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string, timezone: string = 'UTC'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: timezone
  });
};

/**
 * Format a date for display in notifications
 * @param date Date to format
 * @param timezone Optional timezone (defaults to UTC)
 * @returns Formatted date string (e.g., "January 1 at 2:00 PM")
 */
export const formatNotificationDate = (date: Date | string, timezone: string = 'UTC'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Format the date part (e.g., "January 1")
  const options: Intl.DateTimeFormatOptions = {
    month: 'long',
    day: 'numeric',
    timeZone: timezone
  };
  const dateStr = dateObj.toLocaleDateString('en-US', options);

  // Format the time part (e.g., "2:00 PM")
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: timezone
  };
  const timeStr = dateObj.toLocaleTimeString('en-US', timeOptions);

  // Combine date and time
  return `${dateStr} at ${timeStr}`;
};

/**
 * Check if a date is in the past
 * @param date Date to check
 * @returns True if the date is in the past
 */
export const isDateInPast = (date: Date | string): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  return dateObj < now;
};

/**
 * Get the earliest date from an array of dates
 * @param dates Array of dates
 * @returns The earliest date
 */
export const getEarliestDate = (dates: (Date | string)[]): Date => {
  if (!dates || dates.length === 0) {
    return new Date();
  }

  return dates
    .map(date => typeof date === 'string' ? new Date(date) : date)
    .reduce((earliest, current) => current < earliest ? current : earliest);
};

/**
 * Ensure a date is timezone-aware by converting to UTC if needed
 * @param date Date to process
 * @returns Date object with timezone information
 */
export const ensureTimezoneAware = (date: Date | string): Date => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // If the date doesn't have timezone info (local time), treat it as UTC
  if (typeof date === 'string' && !date.includes('Z') && !date.includes('+') && !date.includes('-')) {
    // If it's an ISO string without timezone, append 'Z' to make it UTC
    if (date.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?$/)) {
      return new Date(date + 'Z');
    }
  }

  return dateObj;
};

/**
 * Convert a date to UTC ISO string for database storage
 * @param date Date to convert
 * @returns UTC ISO string
 */
export const toUTCString = (date: Date | string): string => {
  const dateObj = ensureTimezoneAware(date);
  return dateObj.toISOString();
};

export default {
  formatDate,
  formatNotificationDate,
  isDateInPast,
  getEarliestDate,
  ensureTimezoneAware,
  toUTCString
};
