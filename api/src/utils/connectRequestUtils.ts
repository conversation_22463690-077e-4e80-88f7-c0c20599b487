import ConnectRequest from '../models/ConnectRequest';
import User from '../models/User';
import { createNotificationWithPush } from './notificationUtils';
import { NoticeType } from '../models/Notification';
import { Op, Sequelize } from 'sequelize';

/**
 * Check for pending connect requests for a newly registered user's email
 * and create notifications for them
 * @param userId The ID of the newly registered user
 * @param email The email of the newly registered user
 */
export const checkAndCreateConnectRequestNotifications = async (
  userId: number,
  email: string
): Promise<void> => {
  try {
    console.log(`Checking for connect requests for new user ${userId} with email ${email}`);

    // Find all pending connect requests for this email
    const pendingRequests = await ConnectRequest.findAll({
      where: {
        email,
        is_accepted: false,
        is_declined: false,
        [Op.and]: [Sequelize.literal('user_id IS NULL')] // These are requests sent before the user existed
      },
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'display_name', 'profile_image_url']
        }
      ]
    });

    console.log(`Found ${pendingRequests.length} pending connect requests for email ${email}`);

    // Update the user_id field for all these connect requests
    for (const request of pendingRequests) {
      await request.update({
        user_id: userId,
        updated_at: new Date()
      });

      // Get the sender's information
      // Need to use getOwner() to access the associated model
      const sender = await request.getOwner();
      if (!sender) {
        console.error(`Owner not found for connect request ${request.id}`);
        continue;
      }

      const senderName = sender.display_name || 'Someone';
      const senderProfileImg = sender.profile_image_url;

      // Create a notification for the new user
      await createNotificationWithPush(
        sender.id, // Sender ID
        userId,    // Receiver ID (the new user)
        'Connection Request',
        'connection_request',
        `${senderName} wants to connect`,
        {
          screen: 'notifications'
        },
        request.id, // Connect request ID
        senderProfileImg // Sender's profile image
      );

      console.log(`Created connect request notification for user ${userId} from sender ${sender.id}`);
    }
  } catch (error) {
    console.error('Error checking and creating connect request notifications:', error);
    throw error;
  }
};
