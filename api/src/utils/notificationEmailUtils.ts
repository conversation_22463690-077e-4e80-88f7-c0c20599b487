import { loadTemplate } from './emailTemplateUtils';
import { NoticeType } from '../types';
import emailService from '../services/emailService';
import { isS3Url } from './imageUtils';
import s3SigningService from '../services/s3SigningService';

// Base URL for the app
const APP_BASE_URL = 'https://qwrm.app';
const APP_DOWNLOAD_URL = 'https://qwrm.app/download';

// Deep link scheme for the app
const DEEP_LINK_SCHEME = 'qwrm://';

// Generate action URL based on notification data
const getActionUrl = (noticeType: NoticeType, data: Record<string, any> = {}): string => {
  // For event-related notifications, use deep links to the event screen
  if (data.eventId) {
    return `${DEEP_LINK_SCHEME}event?id=${data.eventId}`;
  }

  // For user-related notifications, use deep links to the user profile
  if (data.userId) {
    return `${DEEP_LINK_SCHEME}user?id=${data.userId}`;
  }

  // Default deep links based on notification type
  const urlMap: Record<NoticeType, string> = {
    connection_request: `${DEEP_LINK_SCHEME}notifications`, // Connect requests go to notifications screen
    event_cancellation: `${DEEP_LINK_SCHEME}home`,
    event_update: `${DEEP_LINK_SCHEME}home`,
    payment_due: `${DEEP_LINK_SCHEME}home`,
    payment_received: `${DEEP_LINK_SCHEME}home`,
    event_quorum_reached: `${DEEP_LINK_SCHEME}home`,
    event_date_confirmed: `${DEEP_LINK_SCHEME}home`,
    new_comment: `${DEEP_LINK_SCHEME}home`
  };

  // Return the deep link or fallback to the web URL
  return urlMap[noticeType] || APP_BASE_URL;
};

/**
 * Generate HTML for a notification email using the default template
 * @param subject Email subject
 * @param message Email message
 * @param noticeType Type of notification
 * @param data Additional data for the notification
 * @returns HTML content for the email
 */
export const generateNotificationEmail = async (
  subject: string,
  message: string,
  noticeType: NoticeType,
  data: Record<string, any> = {}
): Promise<string> => {
  // Get the action URL based on the notification type and data
  const requestUrl = getActionUrl(noticeType, data);

  // Get the appropriate image URL based on notification type
  let imageUrl = 'https://via.placeholder.com/120';

  console.log('Notification type:', noticeType);
  console.log('Notification data:', data);

  // Use specific image URLs for different notification types
  if (noticeType === 'event_quorum_reached') {
    // Use the quorum reached image
    imageUrl = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/quorum-padded.png';
    console.log('Using quorum reached image URL:', imageUrl);
  }
  else if (noticeType === 'event_date_confirmed') {
    // Use the date confirmed image
    imageUrl = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/date-confirmed-padded.png';
    console.log('Using date confirmed image URL:', imageUrl);
  }
  // For event cancellations, use the event cancelled image
  else if (noticeType === 'event_cancellation') {
    // Use the event cancelled image
    imageUrl = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-cancelled-padded.png';
    console.log('Using event cancelled image URL:', imageUrl);
  }
  // For connection requests, use the sender's profile image
  else if (noticeType === 'connection_request') {
    if (data.imageUrl) {
      imageUrl = data.imageUrl;
      console.log('Using connection request image URL:', imageUrl);
    }
  }
  // For new comments, don't send email notifications
  else if (noticeType === 'new_comment') {
    // No email for new comments
    console.log('No email will be sent for new comment notifications');
  }
  // For other event-related notifications, use the event's image_url
  else if (noticeType === 'event_update') {
    if (data.eventImageUrl) {
      imageUrl = data.eventImageUrl;
      console.log('Using event image URL:', imageUrl);
    }
  }
  // For payment notifications, use the event's image if available
  else if (noticeType === 'payment_due' || noticeType === 'payment_received') {
    if (data.eventImageUrl) {
      imageUrl = data.eventImageUrl;
      console.log('Using payment notification image URL:', imageUrl);
    }
  }
  // Fallback to imageUrl in data if provided
  else if (data.imageUrl) {
    imageUrl = data.imageUrl;
    console.log('Using fallback image URL from data:', imageUrl);
  }

  // Sign the image URL if it's from S3
  console.log('Original image URL for email:', imageUrl);
  if (imageUrl && isS3Url(imageUrl)) {
    // Use the S3 signing service to get a signed URL
    try {
      const signedUrl = await s3SigningService.getSignedUrl(imageUrl);
      imageUrl = signedUrl;
      console.log('Signed image URL for email:', imageUrl);
    } catch (error) {
      console.error('Error signing image URL for email:', error);
      // Keep the original URL if signing fails
    }
  }

  // Use the default template with notification content
  return loadTemplate('default-template', {
    message: message,
    image_url: imageUrl,
    request_url: requestUrl,
    app_download_url: APP_DOWNLOAD_URL
  });
};

/**
 * Send a notification email to a user
 * @param userId User ID to send the email to
 * @param subject Email subject
 * @param message Email message
 * @param noticeType Type of notification
 * @param data Additional data for the notification
 * @returns Promise with SendGrid response or null if no email should be sent
 */
export const sendNotificationEmail = async (
  userId: number,
  subject: string,
  message: string,
  noticeType: NoticeType,
  data: Record<string, any> = {}
): Promise<any> => {
  try {
    // Skip sending emails for new comment notifications
    if (noticeType === 'new_comment') {
      console.log(`Skipping email for ${noticeType} notification to user ${userId}`);
      return null;
    }

    // Ensure emails are sent for important notifications
    if (noticeType === 'event_quorum_reached' || noticeType === 'event_date_confirmed' || noticeType === 'event_cancellation') {
      console.log(`Ensuring email is sent for important notification: ${noticeType} to user ${userId}`);
    }

    console.log(`Preparing to send ${noticeType} email notification to user ${userId}`);
    console.log(`Email notification data:`, data);

    // Generate the email HTML using the default template
    const html = await generateNotificationEmail(subject, message, noticeType, data);

    // Send the email to the user (using default sender)
    return await emailService.sendEmailToUser(userId, subject, html, undefined, noticeType);
  } catch (error) {
    console.error(`Error sending notification email to user ${userId}:`, error);
    throw error;
  }
};

/**
 * Send notification emails to multiple users
 * @param userIds Array of user IDs to send emails to
 * @param subject Email subject
 * @param message Email message
 * @param noticeType Type of notification
 * @param data Additional data for the notification
 * @returns Promise with SendGrid response or null if no email should be sent
 */
export const sendNotificationEmails = async (
  userIds: number[],
  subject: string,
  message: string,
  noticeType: NoticeType,
  data: Record<string, any> = {}
): Promise<any> => {
  try {
    // Skip sending emails for new comment notifications
    if (noticeType === 'new_comment') {
      console.log(`Skipping emails for ${noticeType} notification to ${userIds.length} users`);
      return null;
    }

    // Ensure emails are sent for important notifications
    if (noticeType === 'event_quorum_reached' || noticeType === 'event_date_confirmed' || noticeType === 'event_cancellation') {
      console.log(`Ensuring emails are sent for important notification: ${noticeType} to ${userIds.length} users`);
    }

    console.log(`Preparing to send ${noticeType} email notifications to ${userIds.length} users`);
    console.log(`Email notification data:`, data);

    // Generate the email HTML using the default template
    const html = await generateNotificationEmail(subject, message, noticeType, data);

    // Send the email to the users (using default sender)
    return await emailService.sendEmailToUsers(userIds, subject, html, undefined, noticeType);
  } catch (error) {
    console.error('Error sending notification emails:', error);
    throw error;
  }
};

export default {
  generateNotificationEmail,
  sendNotificationEmail,
  sendNotificationEmails
};
