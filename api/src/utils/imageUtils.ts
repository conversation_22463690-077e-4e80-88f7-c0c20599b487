// Import only what we need
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

/**
 * Get a signed URL for an S3 image
 * @param url The S3 URL to sign
 * @returns The signed URL
 */
export const getSignedImageUrl = (url: string): string => {
  try {
    console.log('getSignedImageUrl - Input URL:', url);

    if (!url) {
      console.log('Empty URL provided, returning empty string');
      return '';
    }

    // Check if the URL already contains query parameters (indicating it's already signed)
    if (url.includes('?X-Amz-Algorithm=') || url.includes('&X-Amz-Algorithm=')) {
      console.log('URL appears to be already signed, returning as is');
      return url;
    }

    // Extract the key from the URL
    // Example: https://bucket-name.s3.region.amazonaws.com/uploads/file.jpg
    let key = url;

    if (url.includes('http')) {
      const urlParts = url.split('/');
      console.log('URL parts:', urlParts);

      // Find the bucket name part (contains s3.)
      const bucketIndex = urlParts.findIndex(part => part.includes('s3.'));

      if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {
        // Get everything after the bucket domain
        key = urlParts.slice(bucketIndex + 1).join('/');
        // console.log('Extracted key from S3 URL:', key);
      } else {
        // Try to find 'uploads', 'static', or other common folder names
        const folderKeywords = ['uploads', 'static', 'images', 'assets'];

        for (const keyword of folderKeywords) {
          const keywordIndex = urlParts.findIndex(part => part === keyword);
          if (keywordIndex !== -1) {
            // Get the path starting from the keyword
            key = urlParts.slice(keywordIndex).join('/');
            // console.log(`Extracted key using ${keyword} keyword:`, key);
            break;
          }
        }
      }
    }

    // For now, we'll use the CloudFront domain if available
    const cloudFrontDomain = process.env.CLOUDFRONT_DOMAIN;
    console.log('CloudFront domain:', cloudFrontDomain);

    if (cloudFrontDomain) {
      // Make sure we don't have double slashes in the URL
      if (key.startsWith('/')) {
        key = key.substring(1);
      }

      const signedUrl = `https://${cloudFrontDomain}/${key}`;
      console.log('Generated signed URL:', signedUrl);
      return signedUrl;
    }

    // If no CloudFront domain is available, return the original URL
    // In a real implementation, you would call the S3 signing API here
    console.log('No CloudFront domain available, returning original URL');
    return url;
  } catch (error) {
    console.error('Error signing image URL:', error);
    return url; // Return the original URL on error
  }
};

/**
 * Check if a URL is an S3 URL
 * @param url The URL to check
 * @returns True if the URL is an S3 URL, false otherwise
 */
export const isS3Url = (url: string): boolean => {
  if (!url) return false;

  // Check for standard S3 URL patterns
  if (url.includes('s3.') && url.includes('amazonaws.com')) {
    return true;
  }

  // Check for CloudFront URLs that serve S3 content
  const cloudFrontDomain = process.env.CLOUDFRONT_DOMAIN;
  if (cloudFrontDomain && url.includes(cloudFrontDomain)) {
    return true;
  }

  // Check for other common S3 patterns
  if (url.includes('signupsheet-dev-files') ||
      url.includes('signupsheet-prod-files') ||
      url.includes('signupsheet-staging-files')) {
    return true;
  }

  // Check for specific static files
  if (url.includes('/static/event-default.png')) {
    return true;
  }

  return false;
};

export default {
  getSignedImageUrl,
  isS3Url
};
