import emailService from '../services/emailService';
import { generateEventInvitationEmail } from './emailTemplateUtils';
import Event from '../models/Event';
import User from '../models/User';
import { formatDate } from './dateUtils';

// Deep link scheme for the app
const DEEP_LINK_SCHEME = 'qwrm://';

/**
 * Send event invitation emails to participants
 * @param eventId ID of the event
 * @param hostId ID of the event host
 * @param participantIds Array of participant user IDs
 * @returns Promise with SendGrid response
 */
export const sendEventInvitationEmails = async (
  eventId: number,
  hostId: number,
  participantIds: number[]
): Promise<any> => {
  try {
    // Get event details with associated event dates
    const event = await Event.findByPk(eventId, {
      include: [{ association: 'eventDates', order: [['date', 'ASC']] }]
    });
    if (!event) {
      throw new Error(`Event with ID ${eventId} not found`);
    }

    // Get host details
    const host = await User.findByPk(hostId);
    if (!host) {
      throw new Error(`Host with ID ${hostId} not found`);
    }

    // Format the event date for display
    let eventDate = 'TBD';

    // First check if there's a confirmed date
    if (event.confirmed_date) {
      eventDate = formatDate(event.confirmed_date);
    }
    // If no confirmed date, check if there are event dates
    else if (event.eventDates && event.eventDates.length > 0) {
      eventDate = formatDate(event.eventDates[0].date);
    }

    // Generate the email HTML using the new event invitation template
    const appDownloadUrl = 'https://qwrm.app/download';
    const eventImageUrl = event.image_url || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png';

    const html = await generateEventInvitationEmail(
      event.name,
      host.display_name || 'Someone',
      eventDate,
      eventImageUrl,
      eventId,
      appDownloadUrl
    );

    // Send emails to participants
    return await emailService.sendEmailToUsers(
      participantIds,
      `${host.display_name} invited you to ${event.name}`,
      html
    );
  } catch (error) {
    console.error('Error sending event invitation emails:', error);
    throw error;
  }
};

/**
 * Send event invitation emails directly to email addresses (for non-users)
 * @param eventId ID of the event
 * @param hostId ID of the event host
 * @param emailAddresses Array of email addresses to send invitations to
 * @returns Promise with SendGrid response
 */
export const sendEventInvitationEmailsToAddresses = async (
  eventId: number,
  hostId: number,
  emailAddresses: string[]
): Promise<any> => {
  try {
    if (emailAddresses.length === 0) {
      console.log('No email addresses provided for event invitations');
      return null;
    }

    // Get event details with associated event dates
    const event = await Event.findByPk(eventId, {
      include: [{ association: 'eventDates', order: [['date', 'ASC']] }]
    });
    if (!event) {
      throw new Error(`Event with ID ${eventId} not found`);
    }

    // Get host details
    const host = await User.findByPk(hostId);
    if (!host) {
      throw new Error(`Host with ID ${hostId} not found`);
    }

    // Format the event date for display
    let eventDate = 'TBD';

    // First check if there's a confirmed date
    if (event.confirmed_date) {
      eventDate = formatDate(event.confirmed_date);
    }
    // If no confirmed date, check if there are event dates
    else if (event.eventDates && event.eventDates.length > 0) {
      eventDate = formatDate(event.eventDates[0].date);
    }

    // Generate the email HTML using the new event invitation template
    const appDownloadUrl = 'https://qwrm.app/download';
    const eventImageUrl = event.image_url || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png';

    const html = await generateEventInvitationEmail(
      event.name,
      host.display_name || 'Someone',
      eventDate,
      eventImageUrl,
      eventId,
      appDownloadUrl
    );

    // Send emails directly to the email addresses
    const subject = `${host.display_name} invited you to ${event.name}`;

    console.log(`Sending event invitation emails to ${emailAddresses.length} email addresses:`, emailAddresses);

    return await emailService.sendBulkEmail(emailAddresses, subject, html);
  } catch (error) {
    console.error('Error sending event invitation emails to addresses:', error);
    throw error;
  }
};

/**
 * Send event cancellation emails to participants
 * @param eventId ID of the event
 * @param hostId ID of the event host
 * @param participantIds Array of participant user IDs
 * @returns null - Event cancellation emails are disabled
 */
export const sendEventCancellationEmails = async (
  _eventId: number,
  _hostId: number,
  _participantIds: number[]
): Promise<null> => {
  console.log('Event cancellation emails are disabled');
  return null;
};

export default {
  sendEventInvitationEmails,
  // sendEventCancellationEmails is still exported but does nothing
  sendEventCancellationEmails
};
