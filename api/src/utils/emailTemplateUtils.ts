import fs from 'fs';
import path from 'path';
import moment from 'moment';
import { isS3Url } from './imageUtils';
import s3SigningService from '../services/s3SigningService';

// Static asset URLs for emails
const LOGO_URL = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/qwrm-logo.png';
const APP_STORE_BUTTON_URL = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/app-store.png';
const PLAY_STORE_BUTTON_URL = 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/play-store.png';

// Store URLs
const APP_STORE_URL = 'https://apps.apple.com/us/app/qwrm/id6450262881';
const PLAY_STORE_URL = 'https://play.google.com/store/apps/details?id=com.signupsheet.app';

// Deep link scheme for the app
const DEEP_LINK_SCHEME = 'qwrm://';

/**
 * Load an email template and replace placeholders with values
 * @param templateName Name of the template file (without extension)
 * @param replacements Object with key-value pairs to replace in the template
 * @returns HTML content with replacements applied
 */
export const loadTemplate = async (
  templateName: string,
  replacements: Record<string, string> = {}
): Promise<string> => {
  try {
    // Get the template path
    const templatePath = path.join(__dirname, '..', 'templates', `${templateName}.html`);

    // Read the template file
    let templateContent = fs.readFileSync(templatePath, 'utf8');

    // Add logo URL to replacements if not already provided
    if (!replacements.logo_url) {
      // Sign the logo URL
      let logoUrl = LOGO_URL;
      if (isS3Url(logoUrl)) {
        try {
          const signedUrl = await s3SigningService.getSignedUrl(logoUrl);
          logoUrl = signedUrl;
        } catch (error) {
          console.error('Error signing logo URL for email:', error);
          // Keep the original URL if signing fails
        }
      }
      replacements.logo_url = logoUrl;
    }

    // Add App Store button URL to replacements if not already provided
    if (!replacements.app_store_button_url) {
      // Sign the App Store button URL
      let appStoreButtonUrl = APP_STORE_BUTTON_URL;
      if (isS3Url(appStoreButtonUrl)) {
        try {
          const signedUrl = await s3SigningService.getSignedUrl(appStoreButtonUrl);
          appStoreButtonUrl = signedUrl;
        } catch (error) {
          console.error('Error signing App Store button URL for email:', error);
          // Keep the original URL if signing fails
        }
      }
      replacements.app_store_button_url = appStoreButtonUrl;
    }

    // Add Play Store button URL to replacements if not already provided
    if (!replacements.play_store_button_url) {
      // Sign the Play Store button URL
      let playStoreButtonUrl = PLAY_STORE_BUTTON_URL;
      if (isS3Url(playStoreButtonUrl)) {
        try {
          const signedUrl = await s3SigningService.getSignedUrl(playStoreButtonUrl);
          playStoreButtonUrl = signedUrl;
        } catch (error) {
          console.error('Error signing Play Store button URL for email:', error);
          // Keep the original URL if signing fails
        }
      }
      replacements.play_store_button_url = playStoreButtonUrl;
    }

    // Add store URLs to replacements if not already provided
    if (!replacements.app_store_url) {
      replacements.app_store_url = APP_STORE_URL;
    }

    if (!replacements.play_store_url) {
      replacements.play_store_url = PLAY_STORE_URL;
    }

    // Replace placeholders with values
    Object.entries(replacements).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      templateContent = templateContent.replace(regex, value);
    });

    // Log the image URL in the final HTML
    if (replacements.image_url) {
      // Log a snippet of the HTML around the image tag to verify it's being inserted correctly
      const imageTagIndex = templateContent.indexOf(replacements.image_url);
      if (imageTagIndex > -1) {
        const start = Math.max(0, imageTagIndex - 100);
        const end = Math.min(templateContent.length, imageTagIndex + replacements.image_url.length + 100);
      } else {
        console.log('Warning: Image URL not found in final HTML template');
      }
    }

    return templateContent;
  } catch (error) {
    console.error(`Error loading email template ${templateName}:`, error);
    throw error;
  }
};

/**
 * Generate HTML for a connection request email
 * @param name Name of the person sending the request
 * @param imageUrl URL of the image
 * @param message Message content
 * @param requestUrl URL to view the request (optional, will use deep link to notifications if not provided)
 * @param appDownloadUrl URL to download the app
 * @returns HTML content for the email
 */
export const generateConnectionRequestEmail = async (
  name: string,
  imageUrl: string,
  message: string,
  requestUrl: string = `${DEEP_LINK_SCHEME}notifications`,
  appDownloadUrl: string
): Promise<string> => {
  // Sign the image URL if it's from S3
  console.log('Connection request - Original image URL:', imageUrl);
  if (imageUrl && isS3Url(imageUrl)) {
    try {
      const signedUrl = await s3SigningService.getSignedUrl(imageUrl);
      imageUrl = signedUrl;
      console.log('Connection request - Signed image URL:', imageUrl);
    } catch (error) {
      console.error('Error signing image URL for connection request:', error);
      // Keep the original URL if signing fails
    }
  }

  const templateData = {
    message: message || `${name} wants to connect.`,
    image_url: imageUrl,
    request_url: requestUrl,
    app_download_url: appDownloadUrl
  };

  console.log('Connection request - Template data:', templateData);
  return loadTemplate('default-template', templateData);
};

/**
 * Generate HTML for an event invitation email using the new event invitation template
 * @param eventName Name of the event
 * @param hostName Name of the event host
 * @param eventDate Date of the event
 * @param eventImageUrl URL of the event image
 * @param eventId ID of the event (used to create deep link)
 * @param appDownloadUrl URL to download the app
 * @returns HTML content for the email
 */
export const generateEventInvitationEmail = async (
  eventName: string,
  hostName: string,
  eventDate: string,
  eventImageUrl: string,
  eventId: number,
  appDownloadUrl: string
): Promise<string> => {
  // Sign the event image URL if it's from S3
  console.log('Event invitation - Original event image URL:', eventImageUrl);
  let signedEventImageUrl = eventImageUrl;
  if (eventImageUrl && isS3Url(eventImageUrl)) {
    try {
      signedEventImageUrl = await s3SigningService.getSignedUrl(eventImageUrl);
      console.log('Event invitation - Signed event image URL:', signedEventImageUrl);
    } catch (error) {
      console.error('Error signing event image URL for event invitation:', error);
      // Keep the original URL if signing fails
    }
  }

  // Create deep link to the event
  const eventUrl = `${DEEP_LINK_SCHEME}event?id=${eventId}`;

  // Format the event date using moment.js
  const formattedEventDate = moment(eventDate).format('MMMM D, YYYY');

  // Use the new event invitation template
  const templateData = {
    // Don't explicitly set logo_url - let loadTemplate handle signing it automatically
    host_name: hostName,
    event_name: eventName,
    event_date: formattedEventDate,
    event_image_url: signedEventImageUrl || 'https://signupsheet-dev-files.s3.us-east-2.amazonaws.com/static/event-default.png',
    request_url: eventUrl,
    app_download_url: appDownloadUrl,
    app_store_url: APP_STORE_URL,
    play_store_url: PLAY_STORE_URL,
    // Don't explicitly set button URLs - let loadTemplate handle signing them automatically
  };

  console.log('Event invitation - Template data:', templateData);
  return loadTemplate('event-invitation-template', templateData);
};

/**
 * Generate HTML for an event invitation email using the old default template (deprecated)
 * @param eventName Name of the event
 * @param hostName Name of the event host
 * @param eventDate Date of the event
 * @param eventLocation Location of the event
 * @param message Message content
 * @param imageUrl URL of the image
 * @param eventId ID of the event (used to create deep link)
 * @param appDownloadUrl URL to download the app
 * @returns HTML content for the email
 */
export const generateEventInvitationEmailOld = async (
  eventName: string,
  hostName: string,
  eventDate: string,
  eventLocation: string,
  message: string,
  imageUrl: string,
  eventId: number,
  appDownloadUrl: string
): Promise<string> => {
  // Sign the image URL if it's from S3
  console.log('Event invitation - Original image URL:', imageUrl);
  if (imageUrl && isS3Url(imageUrl)) {
    try {
      const signedUrl = await s3SigningService.getSignedUrl(imageUrl);
      imageUrl = signedUrl;
      console.log('Event invitation - Signed image URL:', imageUrl);
    } catch (error) {
      console.error('Error signing image URL for event invitation:', error);
      // Keep the original URL if signing fails
    }
  }

  // Create deep link to the event
  const eventUrl = `${DEEP_LINK_SCHEME}event?id=${eventId}`;

  // This would use a different template, but for now we'll use the default template
  const templateData = {
    message: message || `${hostName} invites you to ${eventName} on ${eventDate} at ${eventLocation}.`,
    image_url: imageUrl || 'https://via.placeholder.com/120',
    request_url: eventUrl,
    app_download_url: appDownloadUrl
  };

  console.log('Event invitation - Template data:', templateData);
  return loadTemplate('default-template', templateData);
};

/**
 * Generate HTML for a custom email
 * @param templateName Name of the template file (without extension)
 * @param replacements Object with key-value pairs to replace in the template
 * @returns HTML content for the email
 */
export const generateCustomEmail = async (
  templateName: string,
  replacements: Record<string, string>
): Promise<string> => {
  return await loadTemplate(templateName, replacements);
};

export default {
  loadTemplate,
  generateConnectionRequestEmail,
  generateEventInvitationEmail,
  generateEventInvitationEmailOld,
  generateCustomEmail
};
