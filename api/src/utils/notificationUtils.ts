import Notification from '../models/Notification';
import { NoticeType } from '../types';
import pushNotificationService from '../services/pushNotificationService';
import { sendNotificationEmail, sendNotificationEmails } from './notificationEmailUtils';

/**
 * Create a notification and send a push notification
 * @param senderId User ID of the sender
 * @param receiverId User ID of the receiver
 * @param subject Notification subject
 * @param noticeType Type of notification
 * @param message Notification message
 * @param data Additional data to send with the push notification
 * @returns The created notification
 */
export const createNotificationWithPush = async (
  senderId: number,
  receiverId: number,
  subject: string,
  noticeType: NoticeType,
  message: string,
  data: Record<string, any> = {},
  connectRequestId?: number,
  avatar?: string
): Promise<Notification | null> => {
  try {
    // Log event cancellation notification
    if (noticeType === 'event_cancellation') {
      console.log('Creating event cancellation notification');
    }

    console.log(`Creating notification: type=${noticeType}, sender=${senderId}, receiver=${receiverId}`);

    // Extract event_id from data if this is an event-related notification
    const eventId = data.eventId || null;

    // Create the notification in the database
    const notification = await Notification.create({
      sender_id: senderId,
      receiver_id: receiverId,
      subject,
      notice_type: noticeType,
      message,
      read: false,
      connect_request_id: connectRequestId,
      event_id: eventId,
      avatar,
      created_at: new Date(),
      updated_at: new Date()
    });

    console.log(`Created notification: id=${notification.id}, type=${noticeType}`);

    // Send push notification
    console.log(`Sending push notification to user ${receiverId}`);
    try {
      const pushResult = await pushNotificationService.sendPushNotificationToUser(
        receiverId,
        subject,
        message,
        {
          ...data,
          notificationId: notification.id,
          noticeType
        },
        noticeType // Pass the notification type for preference checking
      );
      console.log(`Push notification result for user ${receiverId}:`, pushResult);
    } catch (pushError) {
      console.error(`Error sending push notification to user ${receiverId}:`, pushError);
    }

    // Send email notification
    console.log(`Sending email notification to user ${receiverId}`);
    try {
      // For important notifications, ensure we have proper data
      if (noticeType === 'event_quorum_reached' || noticeType === 'event_date_confirmed' || noticeType === 'event_cancellation') {
        console.log(`Preparing important email notification (${noticeType}) for user ${receiverId}`);
        console.log(`Email data:`, {
          ...data,
          notificationId: notification.id,
          imageUrl: avatar,
          eventImageUrl: data.eventImageUrl || null
        });
      }

      const emailResult = await sendNotificationEmail(
        receiverId,
        subject,
        message,
        noticeType,
        {
          ...data,
          notificationId: notification.id,
          imageUrl: avatar, // Pass the avatar as imageUrl for the email
          // If this is an event-related notification and we have an event image URL, include it
          eventImageUrl: data.eventImageUrl || null
        }
      );
      console.log(`Email notification result for user ${receiverId}:`, emailResult ? 'sent' : 'not sent');
    } catch (emailError) {
      console.error(`Error sending email notification to user ${receiverId}:`, emailError);
    }

    return notification;
  } catch (error) {
    console.error('Error creating notification with push:', error);
    throw error;
  }
};

/**
 * Create notifications for multiple users and send push notifications
 * @param senderId User ID of the sender
 * @param receiverIds Array of user IDs to receive the notification
 * @param subject Notification subject
 * @param noticeType Type of notification
 * @param message Notification message
 * @param data Additional data to send with the push notification
 * @returns Array of created notifications
 */
export const createNotificationsWithPush = async (
  senderId: number,
  receiverIds: number[],
  subject: string,
  noticeType: NoticeType,
  message: string,
  data: Record<string, any> = {},
  connectRequestId?: number,
  avatar?: string
): Promise<Notification[]> => {
  try {
    // Log event cancellation notifications
    if (noticeType === 'event_cancellation') {
      console.log('Creating event cancellation notifications');
    }

    console.log(`Creating notifications: type=${noticeType}, sender=${senderId}, receivers=${receiverIds.length}`);

    // Extract event_id from data if this is an event-related notification
    const eventId = data.eventId || null;

    const notifications: Notification[] = [];

    // Create notifications for each receiver
    for (const receiverId of receiverIds) {
      try {
        console.log(`Creating notification for receiver ${receiverId}`);
        const notification = await Notification.create({
          sender_id: senderId,
          receiver_id: receiverId,
          subject,
          notice_type: noticeType,
          message,
          read: false,
          connect_request_id: connectRequestId,
          event_id: eventId,
          avatar,
          created_at: new Date(),
          updated_at: new Date()
        });

        console.log(`Created notification: id=${notification.id}, type=${noticeType}, receiver=${receiverId}`);
        notifications.push(notification);
      } catch (notifError) {
        console.error(`Error creating notification for receiver ${receiverId}:`, notifError);
      }
    }

    // Send push notifications to all receivers
    console.log(`Sending push notifications to ${receiverIds.length} users`);
    try {
      const pushResults = await pushNotificationService.sendPushNotificationsToUsers(
        receiverIds,
        subject,
        message,
        {
          ...data,
          noticeType
        },
        noticeType // Pass the notification type for preference checking
      );
      console.log(`Push notifications results: ${pushResults.length} tickets created`);
    } catch (pushError) {
      console.error('Error sending push notifications:', pushError);
    }

    // Send email notifications to all receivers
    console.log(`Sending email notifications to ${receiverIds.length} users`);
    try {
      // For important notifications, ensure we have proper data
      if (noticeType === 'event_quorum_reached' || noticeType === 'event_date_confirmed' || noticeType === 'event_cancellation') {
        console.log(`Preparing important email notifications (${noticeType}) for ${receiverIds.length} users`);
        console.log(`Email data:`, {
          ...data,
          imageUrl: avatar,
          eventImageUrl: data.eventImageUrl || null
        });
      }

      const emailResults = await sendNotificationEmails(
        receiverIds,
        subject,
        message,
        noticeType,
        {
          ...data,
          imageUrl: avatar, // Pass the avatar as imageUrl for the email
          // If this is an event-related notification and we have an event image URL, include it
          eventImageUrl: data.eventImageUrl || null
        }
      );
      console.log(`Email notifications results:`, emailResults ? 'sent' : 'not sent');
    } catch (emailError) {
      console.error('Error sending email notifications:', emailError);
    }

    return notifications;
  } catch (error) {
    console.error('Error creating notifications with push:', error);
    throw error;
  }
};

export default {
  createNotificationWithPush,
  createNotificationsWithPush
};
