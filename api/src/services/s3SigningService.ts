import axios from 'axios';
import { isS3Url } from '../utils/imageUtils';

/**
 * Service for signing S3 URLs using the same endpoint that the frontend uses
 */
const s3SigningService = {
  /**
   * Gets a signed URL for an S3 object
   * @param url - The original S3 URL or object key
   * @param expiresIn - Expiration time in seconds (default: 1 hour)
   * @returns The signed URL
   */
  getSignedUrl: async (url: string, expiresIn: number = 3600): Promise<string> => {
    if (!url) return '';

    // Check if the URL already contains query parameters (indicating it's already signed)
    if (url.includes('?X-Amz-Algorithm=') || url.includes('&X-Amz-Algorithm=')) {
      return url;
    }

    // Check if the URL is an S3 URL
    if (!isS3Url(url)) {
      // If it's not an S3 URL, return the original URL without signing
      // console.log('Not an S3 URL, skipping signing:', url);
      return url;
    }

    try {
      // Extract the key from the URL if it's a full URL
      let key = url;

      // If it's a full URL, extract the key
      if (url.includes('http')) {
        // Example: https://bucket-name.s3.region.amazonaws.com/uploads/file.jpg
        const urlParts = url.split('/');

        // Find the bucket name part (contains s3.)
        const bucketIndex = urlParts.findIndex(part => part.includes('s3.'));

        if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {
          // Get everything after the bucket domain
          key = urlParts.slice(bucketIndex + 1).join('/');
          // console.log('Extracted key from S3 URL:', key);
        } else {
          // Try to find 'uploads', 'static', or other common folder names
          const folderKeywords = ['uploads', 'static', 'images', 'assets'];

          for (const keyword of folderKeywords) {
            const keywordIndex = urlParts.findIndex(part => part === keyword);
            if (keywordIndex !== -1) {
              // Get the path starting from the keyword
              key = urlParts.slice(keywordIndex).join('/');
              // console.log(`Extracted key using ${keyword} keyword:`, key);
              break;
            }
          }

          // Special case for default event image
          if (url.includes('/static/event-default.png')) {
            key = 'static/event-default.png';
            console.log('Using default event image key:', key);
          }
        }
      }

      // Call the internal API to get a signed URL
      // We're using the same endpoint that the frontend uses
      const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
      const apiUrl = `${API_BASE_URL}/api/uploads/signed-url`;

      const response = await axios.get(apiUrl, {
        params: {
          key,
          expiresIn
        },
        headers: {
          // Add a special header to bypass authentication for internal calls
          'X-Internal-Request': 'true'
        }
      });

      if (response.data && response.data.signedUrl) {
        return response.data.signedUrl;
      }

      console.error('Failed to get signed URL from API:', response.data);
      return url; // Return original URL on error
    } catch (error) {
      console.error('Error getting signed URL from API:', error);
      return url; // Return original URL on error
    }
  }
};

export default s3SigningService;
