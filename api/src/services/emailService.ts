import sgMail from '@sendgrid/mail';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import User from '../models/User';
import UserPreference from '../models/UserPreference';

// Initialize environment variables
dotenv.config();

// Set SendGrid API key
const sendgridKey = process.env.SENDGRID_KEY || '';
if (!sendgridKey) {
  console.error('SENDGRID_KEY is not set in the environment variables');
} else {
  console.log('SENDGRID_KEY is configured');
}
sgMail.setApiKey(sendgridKey);

// Default sender email
const DEFAULT_SENDER = '<EMAIL>';

/**
 * Email service for sending emails using SendGrid
 */
const emailService = {
  /**
   * Send an email using SendGrid
   * @param to Recipient email address
   * @param subject Email subject
   * @param html HTML content of the email
   * @param from Sender email address (<NAME_EMAIL>)
   * @returns Promise with SendGrid response
   */
  sendEmail: async (
    to: string,
    subject: string,
    html: string,
    from: string = DEFAULT_SENDER
  ) => {
    try {
      const msg = {
        to,
        from,
        subject,
        html,
      };

      console.log(`Attempting to send email to ${to} with subject: ${subject}`);
      try {
        const response = await sgMail.send(msg);
        console.log(`Email sent successfully to ${to}:`, response[0].statusCode);
        return response;
      } catch (sendError: any) {
        console.error(`SendGrid error sending to ${to}:`, sendError.response ? sendError.response.body : sendError);
        throw sendError;
      }
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  },

  /**
   * Send an email to multiple recipients
   * @param toAddresses Array of recipient email addresses
   * @param subject Email subject
   * @param html HTML content of the email
   * @param from Sender email address (<NAME_EMAIL>)
   * @returns Promise with SendGrid response
   */
  sendBulkEmail: async (
    toAddresses: string[],
    subject: string,
    html: string,
    from: string = DEFAULT_SENDER
  ) => {
    try {
      const messages = toAddresses.map(to => ({
        to,
        from,
        subject,
        html,
      }));

      console.log(`Attempting to send bulk emails to ${toAddresses.length} recipients with subject: ${subject}`);
      try {
        const response = await sgMail.send(messages);
        console.log(`Bulk emails sent successfully to ${toAddresses.length} recipients:`, response[0].statusCode);
        return response;
      } catch (sendError: any) {
        console.error(`SendGrid error sending bulk emails:`, sendError.response ? sendError.response.body : sendError);
        throw sendError;
      }
    } catch (error) {
      console.error('Error sending bulk emails:', error);
      throw error;
    }
  },

  /**
   * Send an email to a user if they have email notifications enabled
   * @param userId User ID to send email to
   * @param subject Email subject
   * @param html HTML content of the email
   * @param from Sender email address (<NAME_EMAIL>)
   * @param notificationType Optional notification type to check specific preferences
   * @returns Promise with SendGrid response or null if user has email notifications disabled
   */
  sendEmailToUser: async (
    userId: number,
    subject: string,
    html: string,
    from: string = DEFAULT_SENDER,
    notificationType?: string
  ) => {
    try {
      // Get user and their preferences
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      const userPreferences = await UserPreference.findOne({
        where: { user_id: userId }
      });

      // Check if user has email notifications enabled
      if (!userPreferences) {
        console.log(`No user preferences found for user ${userId}, using default settings`);
      } else if (!userPreferences.email_notifications_enabled) {
        console.log(`Email notifications explicitly disabled for user ${userId}`);
        return null;
      } else {
        // Check specific notification type preferences
        if (notificationType === 'event_cancellation' && !userPreferences.notify_event_cancellation) {
          console.log(`Event cancellation email notifications disabled for user ${userId}`);
          return null;
        } else if (notificationType === 'new_comment' && !userPreferences.notify_contact_questions) {
          console.log(`Contact questions email notifications disabled for user ${userId}`);
          return null;
        }
      }

      console.log(`Sending email to user ${userId} at ${user.email}`);

      return await emailService.sendEmail(user.email, subject, html, from);
    } catch (error) {
      console.error(`Error sending email to user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Send emails to multiple users if they have email notifications enabled
   * @param userIds Array of user IDs to send emails to
   * @param subject Email subject
   * @param html HTML content of the email
   * @param from Sender email address (<NAME_EMAIL>)
   * @param notificationType Optional notification type to check specific preferences
   * @returns Promise with SendGrid response
   */
  sendEmailToUsers: async (
    userIds: number[],
    subject: string,
    html: string,
    from: string = DEFAULT_SENDER,
    notificationType?: string
  ) => {
    try {
      // Get users and their preferences
      const users = await User.findAll({
        where: { id: userIds }
      });

      const userPreferences = await UserPreference.findAll({
        where: { user_id: userIds }
      });

      // Filter users based on email notification preferences and specific notification type preferences
      let enabledUserIds: number[] = [];

      if (notificationType === 'event_cancellation') {
        // For event cancellation notifications, check both email_notifications_enabled AND notify_event_cancellation
        enabledUserIds = userPreferences
          .filter(pref => pref.email_notifications_enabled && pref.notify_event_cancellation)
          .map(pref => pref.user_id);
        console.log(`Event cancellation email: ${enabledUserIds.length} of ${userPreferences.length} users have this preference enabled`);
      } else if (notificationType === 'new_comment') {
        // For new comment notifications, check both email_notifications_enabled AND notify_contact_questions
        enabledUserIds = userPreferences
          .filter(pref => pref.email_notifications_enabled && pref.notify_contact_questions)
          .map(pref => pref.user_id);
        console.log(`New comment email: ${enabledUserIds.length} of ${userPreferences.length} users have this preference enabled`);
      } else {
        // For other notification types, just check if email notifications are enabled
        // If a user doesn't have preferences, assume they want emails (default enabled)
        const userIdsWithPrefs = userPreferences.map(pref => pref.user_id);
        const disabledUserIds = userPreferences
          .filter(pref => !pref.email_notifications_enabled)
          .map(pref => pref.user_id);

        enabledUserIds = users
          .filter(user => !disabledUserIds.includes(user.id))
          .map(user => user.id);
        console.log(`${notificationType || 'General'} email: ${enabledUserIds.length} users have email notifications enabled`);
      }

      // Get the enabled users
      const enabledUsers = users.filter(user => enabledUserIds.includes(user.id));

      if (enabledUsers.length === 0) {
        console.log(`No users with email notifications enabled for ${notificationType || 'general'} notifications`);
        return null;
      }

      console.log(`Sending emails to ${enabledUsers.length} users:`, enabledUsers.map(u => u.id));

      // Send emails to enabled users
      const toAddresses = enabledUsers.map(user => user.email);
      return await emailService.sendBulkEmail(toAddresses, subject, html, from);
    } catch (error) {
      console.error('Error sending emails to users:', error);
      throw error;
    }
  }
};

export default emailService;
