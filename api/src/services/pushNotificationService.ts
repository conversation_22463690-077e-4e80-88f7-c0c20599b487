import { Expo, ExpoPushMessage, ExpoPushTicket, ExpoPushReceipt } from 'expo-server-sdk';
import PushToken from '../models/PushToken';
import UserPreference from '../models/UserPreference';
import { Op } from 'sequelize';
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

// Create a new Expo SDK client
// APN_KEY is used for iOS notifications
const expo = new Expo({
  accessToken: process.env.EXPO_ACCESS_TOKEN
});

/**
 * Send push notifications to multiple users
 * @param userIds Array of user IDs to send notifications to
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to send with the notification
 * @param notificationType Optional notification type to check specific preferences
 * @returns Array of push tickets
 */
export const sendPushNotificationsToUsers = async (
  userIds: number[],
  title: string,
  body: string,
  data: Record<string, any> = {},
  notificationType?: string
): Promise<ExpoPushTicket[]> => {
  try {
    // Get user preferences to check if push notifications are enabled
    const userPreferences = await UserPreference.findAll({
      where: {
        user_id: { [Op.in]: userIds },
        push_notifications_enabled: true
      }
    });

    // Filter users based on specific notification type preferences
    let enabledUserIds: number[] = [];

    if (notificationType === 'event_cancellation') {
      // For event cancellation notifications, also check notify_event_cancellation preference
      enabledUserIds = userPreferences
        .filter(pref => pref.notify_event_cancellation === true)
        .map(pref => pref.user_id);
    } else if (notificationType === 'new_comment') {
      // For new comment notifications (contact questions), also check notify_contact_questions preference
      enabledUserIds = userPreferences
        .filter(pref => pref.notify_contact_questions === true)
        .map(pref => pref.user_id);
    } else {
      // For other notification types, just check if push notifications are enabled
      enabledUserIds = userPreferences.map(pref => pref.user_id);
    }

    if (enabledUserIds.length === 0) {
      return [];
    }

    // Get push tokens for users with push notifications enabled
    const pushTokens = await PushToken.findAll({
      where: {
        user_id: { [Op.in]: enabledUserIds },
        is_active: true
      }
    });

    if (pushTokens.length === 0) {
      console.log('No push tokens found for users');
      return [];
    }

    // Create messages array
    const messages: ExpoPushMessage[] = [];

    // Add each push token to the messages array
    for (const pushToken of pushTokens) {
      // Check if the push token is valid
      if (!Expo.isExpoPushToken(pushToken.token)) {
        console.error(`Push token ${pushToken.token} is not a valid Expo push token`);
        continue;
      }

      // Create a message for this push token
      messages.push({
        to: pushToken.token,
        sound: 'default',
        title,
        body,
        data,
        badge: 1,
      });
    }

    // Send the messages
    const chunks = expo.chunkPushNotifications(messages);
    const tickets: ExpoPushTicket[] = [];

    for (const chunk of chunks) {
      try {
        const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        tickets.push(...ticketChunk);
      } catch (error) {
        console.error('Error sending push notification chunk:', error);
      }
    }

    // Process the tickets
    await processPushTickets(tickets, pushTokens);

    return tickets;
  } catch (error) {
    console.error('Error sending push notifications:', error);
    return [];
  }
};

/**
 * Send a push notification to a single user
 * @param userId User ID to send notification to
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to send with the notification
 * @param notificationType Optional notification type to check specific preferences
 * @returns Array of push tickets
 */
export const sendPushNotificationToUser = async (
  userId: number,
  title: string,
  body: string,
  data: Record<string, any> = {},
  notificationType?: string
): Promise<ExpoPushTicket[]> => {
  return sendPushNotificationsToUsers([userId], title, body, data, notificationType);
};

/**
 * Process push tickets to handle errors and update tokens
 * @param tickets Array of push tickets
 * @param pushTokens Array of push tokens
 */
const processPushTickets = async (
  tickets: ExpoPushTicket[],
  pushTokens: PushToken[]
): Promise<void> => {
  // Create a map of push tokens for easy lookup
  const tokenMap = new Map<string, PushToken>();
  pushTokens.forEach(token => {
    tokenMap.set(token.token, token);
  });

  // Process each ticket
  for (let i = 0; i < tickets.length; i++) {
    const ticket = tickets[i];

    if (ticket.status === 'error') {
      // Handle the error
      if (
        ticket.details &&
        ticket.details.error === 'DeviceNotRegistered' &&
        typeof ticket.message === 'object'
      ) {
        // Use type assertion to access the 'to' property
        const message = ticket.message as any;
        if (!message.to) continue;

        const tokenString = Array.isArray(message.to)
          ? message.to[0]
          : message.to;

        const pushToken = tokenMap.get(tokenString);

        if (pushToken) {
          // Deactivate the token
          await PushToken.update(
            { is_active: false },
            { where: { id: pushToken.id } }
          );
          console.log(`Deactivated invalid push token: ${tokenString}`);
        }
      }
    }
  }
};

/**
 * Check push notification receipts to handle errors
 * @param receiptIds Array of receipt IDs
 */
export const checkPushNotificationReceipts = async (
  receiptIds: string[]
): Promise<void> => {
  try {
    // Get the receipts
    const receiptIdChunks = expo.chunkPushNotificationReceiptIds(receiptIds);

    for (const chunk of receiptIdChunks) {
      try {
        const receipts = await expo.getPushNotificationReceiptsAsync(chunk);

        // Process the receipts
        for (const [receiptId, receipt] of Object.entries(receipts)) {
          if (receipt.status === 'error') {
            // Handle the error
            if (receipt.details && receipt.details.error === 'DeviceNotRegistered') {
              // Find the token associated with this receipt and deactivate it
              const pushToken = await PushToken.findOne({
                where: { token: receiptId }
              });

              if (pushToken) {
                await PushToken.update(
                  { is_active: false },
                  { where: { id: pushToken.id } }
                );
                console.log(`Deactivated invalid push token from receipt: ${receiptId}`);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error checking push notification receipts:', error);
      }
    }
  } catch (error) {
    console.error('Error checking push notification receipts:', error);
  }
};

export default {
  sendPushNotificationsToUsers,
  sendPushNotificationToUser,
  checkPushNotificationReceipts
};
