import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db';
import User from './User';
import Event from './Event';

// Attributes interface defines all properties of the model
export interface MessageAttributes {
  id: number;
  text: string;
  user_id: number;
  event_id: number;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface MessageCreationAttributes extends Optional<MessageAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Define the Message model
class Message extends Model<MessageAttributes, MessageCreationAttributes> implements MessageAttributes {
  public id!: number;
  public text!: string;
  public user_id!: number;
  public event_id!: number;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
Message.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    event_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'events',
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'messages',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
Message.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Message.belongsTo(Event, { foreignKey: 'event_id', as: 'event' });

export default Message;
