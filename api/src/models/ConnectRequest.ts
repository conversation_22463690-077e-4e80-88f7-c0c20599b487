import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db';
import User from './User';

// Attributes interface defines all properties of the model
export interface ConnectRequestAttributes {
  id: number;
  owner_id: number; // ID of the user sending the connect request
  user_id?: number; // ID of the user receiving the connect request (if they exist)
  email?: string;
  first_name: string;
  last_name?: string;
  phone_number?: string;
  is_accepted: boolean;
  is_declined: boolean;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface ConnectRequestCreationAttributes extends Optional<ConnectRequestAttributes, 'id' | 'is_accepted' | 'is_declined' | 'created_at' | 'updated_at'> {}

// Define the ConnectRequest model
class ConnectRequest extends Model<ConnectRequestAttributes, ConnectRequestCreationAttributes> implements ConnectRequestAttributes {
  // Add association methods
  public getOwner!: () => Promise<User>;
  public getUser!: () => Promise<User | null>;
  public id!: number;
  public owner_id!: number;
  public user_id?: number;
  public email?: string;
  public first_name!: string;
  public last_name?: string;
  public phone_number?: string;
  public is_accepted!: boolean;
  public is_declined!: boolean;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
ConnectRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    owner_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    email: {
      type: DataTypes.STRING(150),
      allowNull: true,
      validate: {
        isEmail: {
          msg: 'Must be a valid email address',
        },
      }
    },
    first_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    last_name: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    phone_number: {
      type: DataTypes.STRING(15),
      allowNull: true,
    },
    is_accepted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_declined: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'connect_requests',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
ConnectRequest.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });
ConnectRequest.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export default ConnectRequest;
