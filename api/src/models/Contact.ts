import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db'; // Updated import path
import User from './User';

// Attributes interface defines all properties of the model
export interface ContactAttributes {
  id: number;
  user_id?: number;
  first_name: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  owner_id?: number;
  is_priority?: boolean; // Database field name (not camelCase)
  hide_events?: boolean; // Whether to hide events created by this contact
  is_blocked?: boolean; // Whether this contact is blocked
  enabled?: boolean;
  created_at: Date;
  updated_at: Date;
}

// Define properties that are available in the frontend model but not in the database
export interface ContactViewModel extends ContactAttributes {
  name?: string; // Combined name from mock data
  isPriority: boolean; // CamelCase for frontend
  added?: boolean; // For UI to track which contacts are already added
}

// CreationAttributes interface defines the properties required during creation
export interface ContactCreationAttributes extends Optional<ContactAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Define the Contact model
class Contact extends Model<ContactAttributes, ContactCreationAttributes> implements ContactAttributes {
  public id!: number;
  public user_id?: number;
  public first_name!: string;
  public last_name?: string;
  public email?: string;
  public phone_number?: string;
  public owner_id?: number;
  public is_priority?: boolean;
  public hide_events?: boolean;
  public is_blocked?: boolean;
  public enabled?: boolean;

  // Virtual getter for combined name
  get name(): string {
    return `${this.first_name} ${this.last_name || ''}`;
  }

  // Virtual getter for isPriority (camelCase for frontend)
  get isPriority(): boolean {
    return this.is_priority || false;
  }

  // Virtual setter for isPriority
  set isPriority(value: boolean) {
    this.is_priority = value;
  }

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
Contact.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    first_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    last_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING(150),
      allowNull: true,
      validate: {
        isEmail: {
          msg: 'Must be a valid email address',
        },
        // Custom validator to check uniqueness only within the scope of the owner_id
        isUniquePerOwner: async function(this: Contact, value: string) {
          if (value) {
            // Skip validation if owner_id is not set yet
            if (!this.owner_id) return;

            const existingContact = await Contact.findOne({
              where: {
                email: value,
                owner_id: this.owner_id as number
              }
            });
            if (existingContact && existingContact.id !== this.id) {
              throw new Error('You already have a contact with this email address');
            }
          }
        }
      }
    },
    phone_number: {
      type: DataTypes.STRING(15),
      allowNull: true,
    },
    owner_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    is_priority: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    hide_events: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    is_blocked: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'contacts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
Contact.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Contact.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

export default Contact;
