import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db'; // Updated import path
import User from './User';

// Attributes interface defines all properties of the model
export interface GroupAttributes {
  id: number;
  name: string;  // Changed from group_name to name
  owner_id?: number;
  enabled: boolean;
  members?: number[]; // Array of contact IDs that are members of this group
  sort_order: number; // Order for custom sorting
  created_at: Date;
  updated_at: Date;
}

// Define properties that are available in the frontend model but not in the database
export interface GroupViewModel extends GroupAttributes {
  contacts?: string[]; // Array of contact names from mock data
}

// CreationAttributes interface defines the properties required during creation
export interface GroupCreationAttributes extends Optional<GroupAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Define the Group model
class Group extends Model<GroupAttributes, GroupCreationAttributes> implements GroupAttributes {
  public id!: number;
  public name!: string;  // Changed from group_name to name
  public owner_id?: number;
  public enabled!: boolean;
  public members?: number[];
  public sort_order!: number;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
Group.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    owner_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    members: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: true,
      defaultValue: [],
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    sequelize,
    tableName: 'contact_groups', // Keep the original table name
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
Group.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

export default Group;
