import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db';
import User from './User';

// Attributes interface defines all properties of the model
export interface PushTokenAttributes {
  id: number;
  user_id: number;
  token: string;
  device_id: string;
  device_type: 'ios' | 'android' | 'web';
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface PushTokenCreationAttributes extends Optional<PushTokenAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Define the PushToken model
class PushToken extends Model<PushTokenAttributes, PushTokenCreationAttributes> implements PushTokenAttributes {
  public id!: number;
  public user_id!: number;
  public token!: string;
  public device_id!: string;
  public device_type!: 'ios' | 'android' | 'web';
  public is_active!: boolean;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
PushToken.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    token: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    device_id: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    device_type: {
      type: DataTypes.STRING(10),
      allowNull: false,
      validate: {
        isIn: [['ios', 'android', 'web']],
      },
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'push_tokens',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
PushToken.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export default PushToken;
