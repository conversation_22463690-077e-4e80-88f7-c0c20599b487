import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db';
// Import Event type only for TypeScript, not the actual model to avoid circular dependency
import type Event from './Event';

// Attributes interface defines all properties of the model
export interface EventDateAttributes {
  id: number;
  event_id: number;
  date: Date;
  participants: number[]; // Array of user IDs who have signed up for this specific date
  created_at: Date;
  updated_at: Date;
}

// Creation attributes interface defines optional properties for creating a new record
interface EventDateCreationAttributes extends Optional<EventDateAttributes, 'id' | 'participants' | 'created_at' | 'updated_at'> {}

// Define the EventDate model
class EventDate extends Model<EventDateAttributes, EventDateCreationAttributes> implements EventDateAttributes {
  public id!: number;
  public event_id!: number;
  public date!: Date;
  public participants!: number[];
  public created_at!: Date;
  public updated_at!: Date;

  // Virtual fields for associations
  public readonly event?: Event;
}

// Initialize the model
EventDate.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    event_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'events',
        key: 'id'
      }
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    participants: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: false,
      defaultValue: [],
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'event_dates',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations - we'll set up the belongsTo association here
// but the actual association will be established in the Event model

export default EventDate;
