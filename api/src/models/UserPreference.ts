import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db'; // Updated import path
import User from './User';

// Attributes interface defines all properties of the model
export interface UserPreferenceAttributes {
  id: number;
  user_id: number;
  notifications_enabled: boolean;
  language?: string;
  push_notifications_enabled: boolean;
  notify_event_cancellation: boolean;
  notify_event_changes: boolean;
  notify_contact_questions: boolean;
  notify_top_tier_events: boolean;
  email_notifications_enabled: boolean;
  participant_reminders_enabled: boolean;
  notify_dues_owed: boolean;
  notify_tickets_not_purchased: boolean;
  venmo_enabled: boolean;
  paypal_enabled: boolean;
  venmo_username?: string;
  paypal_username?: string;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface UserPreferenceCreationAttributes extends Optional<UserPreferenceAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Define the UserPreference model
class UserPreference extends Model<UserPreferenceAttributes, UserPreferenceCreationAttributes> implements UserPreferenceAttributes {
  public id!: number;
  public user_id!: number;
  public notifications_enabled!: boolean;
  public language?: string;
  public push_notifications_enabled!: boolean;
  public notify_event_cancellation!: boolean;
  public notify_event_changes!: boolean;
  public notify_contact_questions!: boolean;
  public notify_top_tier_events!: boolean;
  public email_notifications_enabled!: boolean;
  public participant_reminders_enabled!: boolean;
  public notify_dues_owed!: boolean;
  public notify_tickets_not_purchased!: boolean;
  public venmo_enabled!: boolean;
  public paypal_enabled!: boolean;
  public venmo_username?: string;
  public paypal_username?: string;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
UserPreference.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    notifications_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    language: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'en',
    },
    push_notifications_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    notify_event_cancellation: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    notify_event_changes: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    notify_contact_questions: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    notify_top_tier_events: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    email_notifications_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    participant_reminders_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    notify_dues_owed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    notify_tickets_not_purchased: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    venmo_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    paypal_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    venmo_username: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    paypal_username: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'user_preferences',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
UserPreference.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export default UserPreference;
