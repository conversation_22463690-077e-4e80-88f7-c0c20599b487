import sequelize from '../db';
import User from './User';
import Contact from './Contact';
import Group from './Group';
import Event from './Event';
import EventDate from './EventDate';
import Notification from './Notification';
import Session from './Session';
import UserPreference from './UserPreference';
import Message from './Message';
import PushToken from './PushToken';
import ConnectRequest from './ConnectRequest';
import PasswordResetToken from './PasswordResetToken';
import EmailVerificationToken from './EmailVerificationToken';

// Initialize the models and associations
const models = {
  User,
  Contact,
  Group,
  Event,
  EventDate,
  Notification,
  Session,
  UserPreference,
  Message,
  PushToken,
  ConnectRequest,
  PasswordResetToken,
  EmailVerificationToken
};

// Define additional associations if needed
// (the primary associations are already defined in each model file)

// Export all models and the sequelize instance
export {
  sequelize,
  User,
  Contact,
  Group,
  Event,
  Notification,
  Session,
  UserPreference,
  Message,
  PushToken,
  ConnectRequest,
  PasswordResetToken,
  EmailVerificationToken
};

export default models;
