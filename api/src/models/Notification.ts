import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db'; // Updated import path
import User from './User';

// Define notification type enum
export type NotificationType = 'connection' | 'cancellation' | 'payment' | 'event' | 'date' | 'comment';
export type NoticeType = 'connection_request' | 'event_cancellation' | 'event_update' | 'payment_due' |
                         'payment_received' | 'event_quorum_reached' | 'event_date_confirmed' | 'new_comment';
export type NotificationIconType = 'calendar-x' | 'message' | 'money' | 'calendar-check' | 'calendar-star';

export interface NotificationAction {
  label: string;
  primary: boolean;
}

// Attributes interface defines all properties of the model
export interface NotificationAttributes {
  id: number;
  sender_id: number;
  receiver_id: number;
  subject: string;
  notice_type: NoticeType;
  message: string;
  read: boolean;
  connect_request_id?: number;
  event_id?: number;
  avatar?: string;
  created_at: Date;
  updated_at: Date;
}

// Define properties that are available in the frontend model but not in the database
export interface NotificationViewModel extends NotificationAttributes {
  type: NotificationType;
  avatar?: string;
  iconType?: NotificationIconType;
  text: string;
  actions?: NotificationAction[];
  boldText?: string[];
}

// CreationAttributes interface defines the properties required during creation
export interface NotificationCreationAttributes extends Optional<NotificationAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Define the Notification model
class Notification extends Model<NotificationAttributes, NotificationCreationAttributes> implements NotificationAttributes {
  public id!: number;
  public sender_id!: number;
  public receiver_id!: number;
  public subject!: string;
  public notice_type!: NoticeType;
  public message!: string;
  public read!: boolean;
  public connect_request_id?: number;
  public event_id?: number;
  public avatar?: string;

  // Virtual getter for text (alias for message for frontend)
  get text(): string {
    return this.message;
  }

  // Derive the frontend type from notice_type
  get type(): NotificationType {
    switch (this.notice_type) {
      case 'connection_request':
        return 'connection';
      case 'event_cancellation':
        return 'cancellation';
      case 'payment_due':
      case 'payment_received':
        return 'payment';
      case 'event_quorum_reached':
      case 'event_update':
        return 'event';
      case 'event_date_confirmed':
        return 'date';
      case 'new_comment':
        return 'comment';
      default:
        return 'event';
    }
  }

  // Derive the icon type from notice_type
  get iconType(): NotificationIconType | undefined {
    switch (this.notice_type) {
      case 'event_cancellation':
        return 'calendar-x';
      case 'new_comment':
        return 'message';
      case 'payment_due':
      case 'payment_received':
        return 'money';
      case 'event_quorum_reached':
        return 'calendar-check';
      case 'event_date_confirmed':
        return 'calendar-star';
      default:
        return undefined;
    }
  }

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
Notification.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    sender_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    receiver_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    subject: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    notice_type: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['connection_request', 'event_cancellation', 'event_update', 'payment_due',
                'payment_received', 'event_quorum_reached', 'event_date_confirmed', 'new_comment']]
      }
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    read: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    connect_request_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'connect_requests',
        key: 'id',
      },
    },
    event_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'events',
        key: 'id',
      },
    },
    avatar: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'notifications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
Notification.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
Notification.belongsTo(User, { foreignKey: 'receiver_id', as: 'receiver' });

export default Notification;
