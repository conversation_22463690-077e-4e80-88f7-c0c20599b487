import { Model, DataTypes, Optional, Op } from 'sequelize';
import sequelize from '../db'; // Updated import path

export type AuthProvider = 'google' | 'facebook';
export type AccessLevel = 'admin' | 'user' | 'editor' | 'viewer';

// Attributes interface defines all properties of the model
export interface UserAttributes {
  id: number;
  display_name: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone_number?: string;
  password_hash?: string;
  access_level: AccessLevel;
  provider: AuthProvider;
  provider_user_id: string;
  access_token?: string;
  refresh_token?: string;
  location?: any; // JSONB
  profile_image_url?: string;
  enabled: boolean;
  auto_locate: boolean;
  email_verified: boolean;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'created_at' | 'updated_at' | 'email_verified'> {}

// Define the User model
class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number;
  public display_name!: string;
  public first_name?: string;
  public last_name?: string;
  public email!: string;
  public phone_number?: string;
  public password_hash?: string;
  public access_level!: AccessLevel;
  public provider!: AuthProvider;
  public provider_user_id!: string;
  public access_token?: string;
  public refresh_token?: string;
  public location?: any;
  public profile_image_url?: string;
  public enabled!: boolean;
  public auto_locate!: boolean;
  public email_verified!: boolean;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    display_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    first_name: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    last_name: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING(150),
      allowNull: false,
      validate: {
        isEmail: {
          msg: 'Must be a valid email address',
        }
      }
    },
    phone_number: {
      type: DataTypes.STRING(20),
      allowNull: true,
    },
    password_hash: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    access_level: {
      type: DataTypes.STRING, // Using ENUM via validator
      allowNull: false,
      defaultValue: 'user',
      validate: {
        isIn: [['admin', 'user', 'editor', 'viewer']],
      },
    },
    provider: {
      type: DataTypes.STRING, // Using ENUM via validator
      allowNull: false,
      validate: {
        isIn: [['google', 'facebook']],
      },
    },
    provider_user_id: {
      type: DataTypes.STRING(250),
      allowNull: false,
    },
    access_token: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    refresh_token: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    location: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    profile_image_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    auto_locate: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    email_verified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false, // Default to true for existing users, will be set to false for new registrations
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default User;
