import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db';
import User from './User';

// Attributes interface defines all properties of the model
export interface EmailVerificationTokenAttributes {
  id: number;
  user_id: number;
  token: string;
  expires_at: Date;
  used: boolean;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface EmailVerificationTokenCreationAttributes extends Optional<EmailVerificationTokenAttributes, 'id' | 'created_at' | 'updated_at' | 'used'> {}

// Define the EmailVerificationToken model
class EmailVerificationToken extends Model<EmailVerificationTokenAttributes, EmailVerificationTokenCreationAttributes> implements EmailVerificationTokenAttributes {
  public id!: number;
  public user_id!: number;
  public token!: string;
  public expires_at!: Date;
  public used!: boolean;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

// Initialize the model
EmailVerificationToken.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    token: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    used: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'email_verification_tokens',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['token'],
        unique: true,
      },
      {
        fields: ['expires_at'],
      },
    ],
  }
);

// Define association with User model
EmailVerificationToken.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

export default EmailVerificationToken;
