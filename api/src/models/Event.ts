import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db'; // Updated import path
import User from './User';
import EventDate from './EventDate';

// Define user status enum type
export type UserEventStatus = 'invited' | 'going' | 'maybe' | 'declined' | null;

// Define a participant interface
export interface EventParticipant {
  user_id: number;
  status: 'going' | 'invited' | 'maybe' | 'declined';
  joined_at: string;
}

// Attributes interface defines all properties of the model
export interface EventAttributes {
  id: number;
  name: string;
  // date field is being replaced by event_dates table
  location?: string;
  address: string;
  // New address fields
  location_name?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  meeting_point?: string;
  owner: string;
  owner_id?: number;
  image_url: string;
  event_link?: string;
  has_options: boolean;
  quorum_met: boolean;
  enabled: boolean;
  participants: number[]; // Array of user IDs
  participants_count: number;
  invitees: number[]; // Array of user IDs
  paid_participants: number[]; // Array of user IDs who have paid
  cost?: number;
  cost_purpose?: string;
  payment_type?: string; // Type of payment (pay_me_back, chip_in, buy_ticket, bring_wallet)
  description?: string;
  quorum?: number;
  max_participants?: number | null;
  confirmed_date?: Date; // The date that met quorum and was confirmed
  multiple_dates: boolean; // Whether users can select multiple dates
  response_cutoff?: number; // Number of hours before the event when users can no longer sign up
  created_at: Date;
  updated_at: Date;
}

// Define properties that are available in the frontend model but not in the database
export interface EventViewModel extends EventAttributes {
  imageURL: string; // CamelCase for frontend
  hasOptions: boolean; // CamelCase for frontend
  quorumMet: boolean; // CamelCase for frontend
  userStatus?: UserEventStatus; // User-specific status (not stored in events table)
  date?: Date[]; // Virtual field for dates from event_dates table
  eventDates?: any[]; // Virtual field for event_dates records
  participantDetails?: EventParticipant[]; // Virtual field for participant details
}

// CreationAttributes interface defines the properties required during creation
export interface EventCreationAttributes extends Optional<EventAttributes,
  'id' |
  'created_at' |
  'updated_at' |
  'description' |
  'participants' |
  'participants_count'
> {}

// Define the Event model
class Event extends Model<EventAttributes, EventCreationAttributes> implements EventAttributes {
  public id!: number;
  public name!: string;
  // date field is being replaced by event_dates table
  public location?: string;
  public address!: string;
  // New address fields
  public location_name?: string;
  public address_line1?: string;
  public address_line2?: string;
  public city?: string;
  public state?: string;
  public zip?: string;
  public meeting_point?: string;
  public owner!: string;
  public owner_id?: number;
  public image_url!: string;
  public event_link?: string;
  public has_options!: boolean;
  public quorum_met!: boolean;
  public enabled!: boolean;
  public participants!: number[]; // Array of user IDs
  public participants_count!: number;
  public invitees!: number[]; // Array of user IDs
  public paid_participants!: number[]; // Array of user IDs who have paid
  public cost?: number;
  public cost_purpose?: string;
  public payment_type?: string; // Type of payment (pay_me_back, chip_in, buy_ticket, bring_wallet)
  public description?: string;
  public quorum?: number;
  public max_participants?: number | null;
  public confirmed_date?: Date; // The date that met quorum and was confirmed
  public multiple_dates!: boolean; // Whether users can select multiple dates
  public response_cutoff?: number; // Number of hours before the event when users can no longer sign up
  public created_at!: Date;
  public updated_at!: Date;

  // Virtual fields for associations
  public readonly eventDates?: EventDate[];
}

// Initialize the model
Event.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    // date field is removed as it's being replaced by event_dates table
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
      defaultValue: '',
    },
    address: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    // New address fields
    location_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    address_line1: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    address_line2: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    zip: {
      type: DataTypes.STRING(20),
      allowNull: true,
    },
    meeting_point: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    owner: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    owner_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    image_url: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    event_link: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    has_options: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    quorum_met: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    participants: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: false,
      defaultValue: [],
    },
    participants_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1, // Start with 1 since the owner is automatically added
    },
    invitees: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: false,
      defaultValue: [],
    },
    paid_participants: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: false,
      defaultValue: [],
    },
    cost: {
      type: DataTypes.DECIMAL,
      allowNull: true,
    },
    cost_purpose: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    payment_type: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    quorum: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
    },
    max_participants: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    confirmed_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    multiple_dates: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    response_cutoff: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Number of hours before the event when users can no longer sign up',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: 'events',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Define associations
Event.belongsTo(User, { foreignKey: 'owner_id', as: 'eventOwner' });
Event.hasMany(EventDate, { foreignKey: 'event_id', as: 'eventDates' });

export default Event;
