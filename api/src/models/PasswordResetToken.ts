import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../db';

// Attributes interface defines all properties of the model
export interface PasswordResetTokenAttributes {
  id: number;
  email: string;
  token: string;
  expires_at: Date;
  used: boolean;
  created_at: Date;
  updated_at: Date;
}

// CreationAttributes interface defines the properties required during creation
export interface PasswordResetTokenCreationAttributes extends Optional<PasswordResetTokenAttributes, 'id' | 'used' | 'created_at' | 'updated_at'> {}

// Define the PasswordResetToken model
class PasswordResetToken extends Model<PasswordResetTokenAttributes, PasswordResetTokenCreationAttributes> implements PasswordResetTokenAttributes {
  public id!: number;
  public email!: string;
  public token!: string;
  public expires_at!: Date;
  public used!: boolean;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Instance method to check if token is expired
  public isExpired(): boolean {
    return new Date() > this.expires_at;
  }

  // Instance method to check if token is valid (not used and not expired)
  public isValid(): boolean {
    return !this.used && !this.isExpired();
  }
}

// Initialize the model
PasswordResetToken.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    email: {
      type: DataTypes.STRING(150),
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    token: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    used: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'password_reset_tokens',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['email'],
      },
      {
        fields: ['token'],
        unique: true,
      },
      {
        fields: ['expires_at'],
      },
    ],
  }
);

export default PasswordResetToken;
