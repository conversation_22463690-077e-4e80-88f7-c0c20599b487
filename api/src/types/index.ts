export type AuthProvider = 'google' | 'facebook';
export type AccessLevel = 'admin' | 'user' | 'editor' | 'viewer';
export type NoticeType = 
  'connection_request' | 
  'event_cancellation' | 
  'event_update' | 
  'payment_due' | 
  'payment_received' | 
  'event_quorum_reached' | 
  'event_date_confirmed' | 
  'new_comment';

export interface User {
  id: number;
  display_name: string | null;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone_number: string | null;
  password_hash: string | null;
  access_level: AccessLevel;
  provider: AuthProvider;
  provider_user_id: string;
  access_token: string | null;
  refresh_token: string | null;
  location: any | null; // JSONB
  enabled: boolean;
  auto_locate: boolean;
  email_verified: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UserPreferences {
  id: number;
  user_id: number;
  notifications_enabled: boolean;
  language: string;
  push_notifications_enabled: boolean;
  notify_event_cancellation: boolean;
  notify_event_changes: boolean;
  notify_contact_questions: boolean;
  notify_top_tier_events: boolean;
  email_notifications_enabled: boolean;
  participant_reminders_enabled: boolean;
  notify_dues_owed: boolean;
  notify_tickets_not_purchased: boolean;
  venmo_enabled: boolean;
  paypal_enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UserNotification {
  id: number;
  owner_id: number | null;
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Notification {
  id: number;
  sender_id: number;
  receiver_id: number;
  subject: string;
  notice_type: NoticeType;
  message: string;
  read: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Session {
  id: number;
  user_id: number;
  session_token: string;
  user_agent: string | null;
  expires_at: Date | null;
  ip_address: string | null;
  city: string | null;
  country: string | null;
  owner_id: number | null;
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Contact {
  id: number;
  user_id: number | null;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string | null;
  owner_id: number | null;
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface JwtPayload {
  id: number;
  email: string;
  access_level: AccessLevel;
  iat?: number;
  exp?: number;
}

export interface AuthRequest extends Express.Request {
  user?: JwtPayload;
}
