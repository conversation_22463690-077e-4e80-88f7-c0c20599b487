{"name": "qwrm-api", "version": "1.0.0", "description": "API for the QWRM application", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn src/server.ts", "build": "tsc", "test": "jest", "lint": "eslint . --ext .ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@sendgrid/mail": "^8.1.5", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "expo-notifications": "^0.29.14", "expo-server-sdk": "^3.14.0", "express": "^4.18.2", "google-auth-library": "^9.15.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "pg": "^8.11.3", "pg-promise": "^11.5.4", "sequelize": "^6.37.7"}, "devDependencies": {"@types/bcrypt": "^5.0.1", "@types/cors": "^2.8.16", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.9.0", "@types/pg": "^8.10.9", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}