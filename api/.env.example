# Database Configuration
DB_HOST=localhost
DB_NAME=signup_sheet
DB_USER=postgres
DB_PASSWORD=postgres

# JWT Secret for Authentication
JWT_SECRET=your_jwt_secret_here

# SendGrid API Key for Email Service
SENDGRID_KEY=your_sendgrid_api_key_here

# AWS S3 Configuration
AMAZON_S3_REGION=us-east-2
AMAZON_S3_KEY_ID=your_aws_access_key_id
AMAZON_S3_SECRET=your_aws_secret_access_key
AMAZON_S3_BUCKET=your_s3_bucket_name

# CloudFront Configuration
CLOUDFRONT_DOMAIN=d256cd65eueaaf.cloudfront.net

# Expo Push Notifications
EXPO_ACCESS_TOKEN=your_expo_access_token
APN_KEY=your_apn_key_for_ios_notifications

# Google OAuth
GOOGLE_ANDROID_OAUTH_CLIENT_ID=your_google_android_client_id
GOOGLE_IOS_OAUTH_CLIENT_ID=your_google_ios_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# OpenAI API Key (for link extraction)
OPENAI_API_KEY=your_openai_api_key

# Server Configuration
PORT=3000
NODE_ENV=development
