CREATE TYPE auth_provider AS ENUM ('google', 'facebook');
CREATE TYPE access_level AS ENUM ('admin', 'user', 'editor', 'viewer');

-- Table: users
-- This table stores user accounts and associated authentication details.
-- Includes support for social authentication providers like Google and Facebook.
-- Contains role-based access control with "access_level" ENUM to define user roles.

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    display_name VA<PERSON><PERSON><PERSON>(100) UNIQUE,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name  VA<PERSON><PERSON><PERSON>(50),
    email VARCHAR(150) NOT NULL UNIQUE,
    password_hash TEXT, -- Nullable for users registered via social authentication
    access_level access_level DEFAULT 'user' NOT NULL, -- Enum for user roles (e.g., admin, user, editor)
    provider auth_provider NOT NULL, -- Using the enum type
    provider_user_id VARCHAR(250) NOT NULL, -- User ID from the provider
    access_token TEXT, -- Access token returned by the provider
    refresh_token TEXT, -- Refresh token returned by the provider (if applicable)
    location JSONB, -- Location data as returned by Android or iOS in JSON format
    enabled BOOLEAN DEFAULT TRUE NOT NULL,
    auto_locate <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Table: user_preferences
-- This table stores user-specific preferences and settings.
-- Each preference is linked to a specific user and includes customizable options
-- such as theme, notification settings, or language preference.

CREATE TABLE user_preferences
(
    id                    SERIAL PRIMARY KEY,                             -- Unique ID for each preference
    user_id               INT                                   NOT NULL, -- Foreign key linking to the users table
    notifications_enabled BOOLEAN     DEFAULT TRUE              NOT NULL, -- Whether notifications are enabled
    language              VARCHAR(10) DEFAULT 'en',                       -- Preferred language of the user (e.g., en, es, fr)

    -- Notification Preferences
    push_notifications_enabled BOOLEAN DEFAULT TRUE NOT NULL,
    notify_event_cancellation BOOLEAN DEFAULT TRUE NOT NULL,
    notify_event_changes BOOLEAN DEFAULT FALSE NOT NULL,
    notify_contact_questions BOOLEAN DEFAULT TRUE NOT NULL,
    notify_top_tier_events BOOLEAN DEFAULT TRUE NOT NULL,

    email_notifications_enabled BOOLEAN DEFAULT FALSE NOT NULL,

    participant_reminders_enabled BOOLEAN DEFAULT TRUE NOT NULL,
    notify_dues_owed BOOLEAN DEFAULT TRUE NOT NULL,
    notify_tickets_not_purchased BOOLEAN DEFAULT FALSE NOT NULL,

    -- Payment Preferences
    venmo_enabled BOOLEAN DEFAULT TRUE NOT NULL,
    paypal_enabled BOOLEAN DEFAULT FALSE NOT NULL,

    created_at            TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Record creation timestamp
    updated_at            TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Record update timestamp
    CONSTRAINT fk_user
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE     -- Links preferences to users
);

-- Table: user_notifications
-- This table stores user-specific notification preferences and settings.
-- Each record links to a user (owner_id) and determines whether notifications are enabled.
-- Timestamps are included to track when preferences were created or last updated.

CREATE TABLE user_notifications (
    id SERIAL PRIMARY KEY, -- Unique identifier for each notification setting
    owner_id INT DEFAULT NULL, -- The user who owns this notification setting
    enabled BOOLEAN DEFAULT TRUE NOT NULL, -- Determines if notifications are enabled for the user
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Timestamp when the record was created
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Timestamp when the record was last updated
    CONSTRAINT fk_owner FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL -- Ensures referential integrity
);

-- Enum: notice_type
-- Defines all possible types of notifications a user can receive.

CREATE TYPE notice_type AS ENUM (
    'connection_request', -- When a user wants to connect
    'event_cancellation', -- An event has been canceled
    'event_update', -- Event details have changed
    'payment_due', -- A payment is due
    'payment_received', -- Payment has been completed
    'event_quorum_reached', -- Enough participants have joined an event
    'event_date_confirmed', -- The date for an event has been finalized
    'new_comment' -- A user has posted a comment
);

-- Table: notifications
-- Stores all system notifications, linking to sender, receiver, and subject.
-- Uses the notice_type enum to specify the type of notification.

CREATE TABLE notifications (
    id SERIAL PRIMARY KEY, -- Unique notification ID
    sender_id INT NOT NULL, -- The user who triggered the notification
    receiver_id INT NOT NULL, -- The user receiving the notification
    subject VARCHAR(255) NOT NULL, -- A short identifier for the notification's subject
    notice_type notice_type NOT NULL, -- Enum specifying the type of notification
    message TEXT NOT NULL, -- The content of the notification
    read BOOLEAN DEFAULT FALSE NOT NULL, -- Indicates whether the user has read the notification
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Timestamp when the notification was created
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Timestamp when the notification was last updated
    CONSTRAINT fk_sender FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE, -- Ensures referential integrity
    CONSTRAINT fk_receiver FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE -- Ensures referential integrity
);

-- Table: sessions
-- This table manages active user sessions for authentication and tracking purposes.
-- Stores session metadata including tokens, IP address, and user device information.
-- References users table as a foreign key to link sessions to user accounts.
-- Session records can be automatically deleted when a user is deleted (ON DELETE CASCADE).

CREATE TABLE sessions (
    id SERIAL PRIMARY KEY,                     -- Unique ID for each session
    user_id INT NOT NULL,                      -- Foreign key linking to the users table
    session_token UUID NOT NULL UNIQUE,        -- Unique session token
    user_agent TEXT,                           -- Information about the user's device/browser
    expires_at TIMESTAMP,                      -- Expiration time for the session
    -- Add location information
    ip_address VARCHAR(45),                    -- IP address of the user (supports both IPv4 and IPv6)
    city       VARCHAR(100),                   -- City of the user
    country    VARCHAR(100),                   -- Country of the user
    owner_id INT DEFAULT NULL,
    enabled BOOLEAN DEFAULT TRUE NOT NULL,     -- Whether the session is enabled (active)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL, -- Session creation time
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT fk_user
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE -- Links sessions to users
);

-- Table: contacts
-- This table stores detailed contact information for users or organizations.
-- Each contact record can optionally belong to a "user" (via user_id).
-- Supports storing personal or professional details, including name, email, and phone number.
-- Contains an "owner_id" foreign key to tie contacts to specific users (with DELETE SET NULL).

CREATE TABLE contacts (
    id SERIAL PRIMARY KEY,
    user_id INT DEFAULT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100),
    email VARCHAR(150) UNIQUE,
    phone_number VARCHAR(15),
    owner_id INT DEFAULT NULL,
    is_priority BOOLEAN DEFAULT FALSE,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT fk_owner
        FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Table: contact_groups
-- This table is used to manage groups of contacts for better organization.
-- Each contact group has a unique name and an optional owner tied to a "users" record.
-- Ideal for categorizing contacts into distinct groupings (e.g., family, work, friends).
-- Contains an "owner_id" foreign key to link the group to specific users (with DELETE SET NULL).

CREATE TABLE contact_groups (
    id SERIAL PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL UNIQUE,
    owner_id INT DEFAULT NULL,
    enabled BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT fk_owner
        FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL
);






