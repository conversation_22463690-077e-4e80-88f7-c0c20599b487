/**
 * Date helper utilities to avoid timezone issues when working with dates
 */

/**
 * Format a Date object to YYYY-MM-DD string using local time (no timezone conversion)
 * This prevents the common issue where selecting July 31st shows as July 30th due to UTC conversion
 * @param date - The Date object to format
 * @returns String in YYYY-MM-DD format
 */
export const formatDateToLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Parse a date string and format it to local YYYY-MM-DD format
 * @param dateString - ISO date string or other parseable date format
 * @returns String in YYYY-MM-DD format using local time
 */
export const parseDateToLocal = (dateString: string): string => {
  const date = new Date(dateString);
  return formatDateToLocal(date);
};

/**
 * Format time from a Date object to HH:MM format
 * @param date - The Date object to extract time from
 * @returns String in HH:MM format
 */
export const formatTimeToLocal = (date: Date): string => {
  return date.toTimeString().slice(0, 5);
};

/**
 * Convert an array of date strings to local date/time entries
 * @param dateStrings - Array of ISO date strings
 * @returns Array of objects with local date and time strings
 */
export const convertDatesToLocal = (dateStrings: string[]): Array<{date: string, time: string}> => {
  return dateStrings.map(dateStr => {
    const dateObj = new Date(dateStr);
    return {
      date: formatDateToLocal(dateObj),
      time: formatTimeToLocal(dateObj)
    };
  });
};
