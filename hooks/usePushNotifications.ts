import { useEffect, useState } from 'react';
import { useAuthStore } from '@/stores/auth';
import pushNotificationService from '@/services/pushNotification';

export const usePushNotifications = () => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [isRegistered, setIsRegistered] = useState(false);
  const { userData, isAuthenticated } = useAuthStore(state => ({
    userData: state.userData,
    isAuthenticated: !!state.userData?.token
  }));

  // Register for push notifications when the user is authenticated
  useEffect(() => {
    let isMounted = true;

    const registerForPushNotifications = async () => {
      if (!isAuthenticated) return;

      try {
        // Get the Expo push token
        const token = await pushNotificationService.registerForPushNotifications();
        
        if (token && isMounted) {
          setExpoPushToken(token);
          
          // Register the token with the server
          const registered = await pushNotificationService.registerPushToken(token);
          if (isMounted) {
            setIsRegistered(registered);
          }
        }
      } catch (error) {
        console.error('Error in usePushNotifications:', error);
      }
    };

    registerForPushNotifications();

    // Set up notification listeners
    const unsubscribe = pushNotificationService.setupNotificationListeners();

    return () => {
      isMounted = false;
      unsubscribe();
    };
  }, [isAuthenticated, userData?.id]);

  // Deactivate the push token when the user logs out
  useEffect(() => {
    if (!isAuthenticated && expoPushToken) {
      pushNotificationService.deactivatePushToken();
      setExpoPushToken(null);
      setIsRegistered(false);
    }
  }, [isAuthenticated, expoPushToken]);

  return {
    expoPushToken,
    isRegistered
  };
};

export default usePushNotifications;
