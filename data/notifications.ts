import { NotificationIconType } from '../components/NotificationIcon';

export interface NotificationAction {
  label: string;
  primary: boolean;
}

export interface Notification {
  id: number;
  type: 'connection' | 'cancellation' | 'payment' | 'event' | 'date' | 'comment';
  avatar?: string;
  iconType?: NotificationIconType;
  text: string;
  actions?: NotificationAction[];
  boldText?: string[];
}

export const notifications: Notification[] = [
  {
    id: 1,
    type: 'connection',
    avatar: 'https://i.pravatar.cc/80?img=1',
    text: 'Jordan H. wants to connect.',
    boldText: ['Jordan H.'],
    actions: [
      { label: 'ACCEPT', primary: true },
      { label: 'IGNORE', primary: false }
    ]
  },
  {
    id: 2,
    type: 'connection',
    avatar: 'https://i.pravatar.cc/80?img=2',
    text: '<PERSON> has accepted your friend request.',
    boldText: ['<PERSON>']
  },
  {
    id: 3,
    type: 'cancellation',
    iconType: 'calendar-x',
    text: '<PERSON> has cancelled Handball @ Greenwood Playground due to weather.',
    boldText: ['<PERSON>', 'Handball @ Greenwood Playground']
  },
  {
    id: 4,
    type: 'cancellation',
    iconType: 'message',
    text: 'Your cancellation of Movie Night: Girlfriends has been sent to all participants.',
    boldText: ['Movie Night: Girlfriends']
  },
  {
    id: 5,
    type: 'payment',
    iconType: 'money',
    text: '$62.50 is due to Ben H. for "Weird Al" Yankovic at Apollo.',
    boldText: ['$62.50', 'Ben H.', '"Weird Al" Yankovic at Apollo'],
    actions: [{ label: 'PAY NOW', primary: true }]
  },
  {
    id: 6,
    type: 'payment',
    iconType: 'money',
    text: 'Sarah K. has paid you $45.00 for Molly Sarlé at Baby\'s All Right.',
    boldText: ['Sarah K.', '$45.00', 'Molly Sarlé at Baby\'s All Right']
  },
  {
    id: 7,
    type: 'event',
    iconType: 'calendar-check',
    text: 'Sports at Mercury Lounge has reached quorum. It\'s on!',
    boldText: ['Sports at Mercury Lounge'],
    actions: [{ label: 'BUY TICKETS', primary: true }]
  },
  {
    id: 8,
    type: 'event',
    iconType: 'calendar-check',
    text: 'Your event David Byrne\'s American Utopia has reached quorum!',
    boldText: ['David Byrne\'s American Utopia']
  },
  {
    id: 9,
    type: 'date',
    iconType: 'calendar-star',
    text: 'Date confirmed: Noah\'s Ark Inspires Von Cleef & Arpels will happen November 16 at 2pm.',
    boldText: ['Noah\'s Ark Inspires Von Cleef & Arpels', 'November 16 at 2pm'],
    actions: [{ label: 'VIEW', primary: true }]
  },
  {
    id: 10,
    type: 'date',
    iconType: 'calendar-star',
    text: 'The date for your event Handball @ Greenwood Playground has been set to October 7 at 3pm.',
    boldText: ['Handball @ Greenwood Playground', 'October 7 at 3pm']
  },
  {
    id: 11,
    type: 'comment',
    iconType: 'message',
    text: 'Ben H. has posted a comment to David Byrne\'s American Utopia.',
    boldText: ['Ben H.', 'David Byrne\'s American Utopia'],
    actions: [{ label: 'VIEW', primary: true }]
  },
  {
    id: 12,
    type: 'comment',
    iconType: 'message',
    text: 'Your comment on Sports at Mercury Lounge has been posted.',
    boldText: ['Sports at Mercury Lounge']
  }
];