export interface Contact {
  id: string;
  name: string;
  isPriority: boolean;
  added?: boolean;
}

// Original contacts list (your existing contacts)
export const contacts: Contact[] = [
  { id: '1', name: '<PERSON>', isPriority: true },
  { id: '2', name: '<PERSON>', isPriority: true },
  { id: '3', name: '<PERSON>', isPriority: false },
  { id: '4', name: '<PERSON>', isPriority: false },
  { id: '5', name: '<PERSON>', isPriority: false },
  { id: '6', name: '<PERSON>', isPriority: false },
  { id: '7', name: '<PERSON>', isPriority: true },
  { id: '8', name: '<PERSON>', isPriority: true },
  { id: '9', name: '<PERSON>', isPriority: true },
  { id: '10', name: '<PERSON>', isPriority: false },
  { id: '11', name: '<PERSON>', isPriority: false },
  { id: '12', name: '<PERSON>', isPriority: false },
  { id: '13', name: '<PERSON>', isPriority: false },
];

// Connected contacts from the phone's contact list
export const connectedContacts: Contact[] = [
  { id: 'c1', name: '<PERSON> M.', isPriority: false, added: true },
  { id: 'c2', name: 'Angela K.', isPriority: false },
  { id: 'c3', name: 'Billy H.', isPriority: false },
  { id: 'c4', name: 'Chris S.', isPriority: false },
  { id: 'c5', name: 'Eddie H.', isPriority: false },
  { id: 'c6', name: 'Elliott R.', isPriority: false, added: true },
  { id: 'c7', name: 'Eric FB.', isPriority: false, added: true },
  { id: 'c8', name: 'Frank K.', isPriority: false },
  { id: 'c9', name: 'Ian C.', isPriority: false, added: true },
  { id: 'c10', name: 'Jeremy S.', isPriority: false },
  { id: 'c11', name: 'Joe H.', isPriority: false },
  { id: 'c12', name: 'John P.', isPriority: false },
  { id: 'c13', name: 'Jordan H.', isPriority: false },
  { id: 'c14', name: 'Kate C.', isPriority: false, added: true },
];

export interface Group {
  id: string;
  name: string;
  contacts: string[]; // Array of contact names
}

// Groups data with their members
export const groups: Group[] = [
  { 
    id: '1', 
    name: 'Top Tier', 
    contacts: ['Ben H.', 'Beth S.', 'Joe P.', 'Jordan H.'] 
  },
  { 
    id: '2', 
    name: 'Archery', 
    contacts: ['Ben H.', 'Billy H.', 'Kate C.'] 
  },
  { 
    id: '3', 
    name: 'Alternative Sports', 
    contacts: ['Eddie H.', 'Eric FB.', 'Mary R.'] 
  },
  { 
    id: '4', 
    name: 'Avant Garde', 
    contacts: ['Joe H.', 'Lawrence K.'] 
  },
  { 
    id: '5', 
    name: 'Concert Goers', 
    contacts: ['Beth S.', 'Joe P.', 'Nina M.'] 
  },
  { 
    id: '6', 
    name: 'Handball Crew', 
    contacts: ['Ian C.', 'Michael H.'] 
  },
  { 
    id: '7', 
    name: 'Hangs', 
    contacts: ['Ben H.', 'Jill F.', 'Joe P.'] 
  },
  { 
    id: '8', 
    name: 'Spectator Sports', 
    contacts: ['Joe P.', 'Jordan H.'] 
  },
  { 
    id: '9', 
    name: 'Steelers Fans', 
    contacts: ['Beth S.', 'Billy H.', 'Kate C.'] 
  },
  { 
    id: '10', 
    name: 'Up For Anything', 
    contacts: ['Beth S.', 'Joe H.', 'Joe P.', 'Jordan H.'] 
  }
];
