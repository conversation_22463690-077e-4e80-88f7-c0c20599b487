export interface Event {
  id: number;
  name: string;
  date: string[]; // Array of timestamp strings
  location?: string; // Location name (e.g., "<PERSON>'s house")
  address: string;
  owner: string;
  owner_id?: number; // ID of the event creator
  imageURL: string;
  hasOptions: boolean;
  quorumMet: boolean;
  isAtCapacity: boolean;
  userStatus: 'invited' | 'going' | null;
  isOwner?: boolean; // Whether the current user is the owner of this event
  isPastEvent?: boolean; // Whether the event is past its last date
  isWithinResponseCutoff?: boolean; // Whether the event is within the response cutoff period
  quorum?: number; // Minimum number of people needed for the event to become official
  max_participants?: number | null; // Maximum number of participants allowed (null means unlimited)
  confirmed_date?: string; // The date that met quorum and was confirmed
  response_cutoff?: number; // Number of hours before the event when users can no longer sign up
  created_at?: string; // When the event was created
  updated_at?: string; // When the event was last updated
}

export const events: Event[] = [
  {
    id: 1,
    name: "<PERSON>'s American Utopia",
    date: ["2023-11-16T17:30:00Z", "2023-12-05T20:00:00Z", "2023-12-13T20:00:00Z"],
    address: "Hudson Theater, Brooklyn, NY",
    owner: "you",
    imageURL: "https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000",
    hasOptions: true,
    quorumMet: false,
    isAtCapacity: false,
    userStatus: 'invited'
  },
  {
    id: 2,
    name: "Molly Sarlé at Baby's All Right",
    date: ["2023-10-18T19:00:00Z"],
    address: "Baby's All Right, Brooklyn, NY",
    owner: "Beth",
    imageURL: "https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000",
    hasOptions: false,
    quorumMet: true,
    isAtCapacity: true,
    userStatus: 'going'
  },
  {
    id: 3,
    name: "Movie Night: Girlfriends",
    date: ["2023-10-13T19:30:00Z"],
    address: "Metrograph, New York, NY",
    owner: "Stephanie B.",
    imageURL: "https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000",
    hasOptions: false,
    quorumMet: true,
    isAtCapacity: false,
    userStatus: null
  },
  {
    id: 4,
    name: "Handball at Greenwood Playground",
    date: ["2023-10-07T14:00:00Z"],
    address: "Greenwood Playground, Brooklyn, NY",
    owner: "Ben H.",
    imageURL: "https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000",
    hasOptions: true,
    quorumMet: false,
    isAtCapacity: false,
    userStatus: null
  },
  {
    id: 5,
    name: "Jazz at Lincoln Center",
    date: ["2023-10-21T20:00:00Z"],
    address: "Lincoln Center, New York, NY",
    owner: "Sarah M.",
    imageURL: "https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000",
    hasOptions: false,
    quorumMet: true,
    isAtCapacity: true,
    userStatus: null
  },
  {
    id: 6,
    name: "Book Club: The Overstory",
    date: ["2023-10-22T15:00:00Z"],
    address: "Prospect Park, Brooklyn, NY",
    owner: "Alex K.",
    imageURL: "https://images.unsplash.com/photo-1584553421349-3557471bed79?fm=jpg&q=60&w=1000",
    hasOptions: false,
    quorumMet: true,
    isAtCapacity: true,
    userStatus: 'invited'
  },
];