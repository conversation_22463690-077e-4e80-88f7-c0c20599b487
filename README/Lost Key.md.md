3. If you’ve lost your upload key

Google Play supports requesting a new upload key:

1.	Generate a new key:

keytool -genkeypair \
  -alias upload-new \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -keystore new-upload.keystore

2.	Export the certificate:

keytool -export \
  -alias upload-new \
  -keystore new-upload.keystore \
  -file upload-new.pem

3.	In the Play Console, go to Release > Setup > App Integrity and follow “Upload key reset” to send them that upload-new.pem.

4.	Once <PERSON> has confirmed, switch your local config to use new-upload.keystore as in step 2 above, rebuild, and re-upload.

That will align your local signing key with what <PERSON> expects (SHA-1 E6:4C…), and your bundle upload will succeed. Let me know if you run into any hiccups!
