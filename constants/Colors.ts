const tintColorLight = '#2f95dc';
const tintColorDark = '#fff';

// Add our app-specific colors
const colors = {
  primary: '#FFFF00',
  primaryDark: '#666600',
  secondary: '#00CCFF',
  secondaryDark: '#000066',
  background: '#202326',
  backgroundDark: '#1A1D20',
  text: {
    light: '#000',
    dark: '#fff',
    muted: '#222222',
  },
  input: {
    background: 'rgba(225, 247, 250, 0.2)',
    border: '#34D3FC',
    placeholder: '#0B4551',
  },
  icon: {
    default: '#00CCFF',
    selected: '#FFFF00',
    muted: '#ccc',
  },
  green: '#00FFCC',
  hyperBlue: '#0033FF',
  error: '#FF0066',
  success: '#4CAF50',
  white: '#fff',
  black: '#000',
  gray: '#838485',
};

export default {
  light: {
    text: colors.text.light,
    background: colors.background,
    tint: tintColorLight,
    tabIconDefault: colors.icon.muted,
    tabIconSelected: colors.icon.selected,
    ...Object.fromEntries(Object.entries(colors).filter(([key]) => key !== 'background')),
  },
  dark: {
    text: colors.text.dark,
    background: colors.background,
    tint: tintColorDark,
    tabIconDefault: colors.icon.muted,
    tabIconSelected: colors.icon.selected,
    ...Object.fromEntries(Object.entries(colors).filter(([key]) => key !== 'text')),
  },
  ...colors,
};
