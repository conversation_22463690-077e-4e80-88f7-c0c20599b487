// Import react-native-get-random-values first to ensure crypto polyfill is available
// This must be imported before any other modules that might use crypto.getRandomValues
import 'react-native-get-random-values';

// Polyfill for Array.prototype.findLast (ES2023)
// Required for React Navigation 7.x compatibility with React Native 0.76.9

if (!Array.prototype.findLast) {
  Array.prototype.findLast = function(predicate, thisArg) {
    if (this == null) {
      throw new TypeError('Array.prototype.findLast called on null or undefined');
    }
    
    if (typeof predicate !== 'function') {
      throw new TypeError('predicate must be a function');
    }
    
    const O = Object(this);
    const len = parseInt(O.length) || 0;
    
    for (let k = len - 1; k >= 0; k--) {
      if (k in O) {
        const kValue = O[k];
        if (predicate.call(thisArg, kValue, k, O)) {
          return kValue;
        }
      }
    }
    
    return undefined;
  };
}

// Polyfill for Array.prototype.findLastIndex (ES2023)
// Also required for complete compatibility
if (!Array.prototype.findLastIndex) {
  Array.prototype.findLastIndex = function(predicate, thisArg) {
    if (this == null) {
      throw new TypeError('Array.prototype.findLastIndex called on null or undefined');
    }
    
    if (typeof predicate !== 'function') {
      throw new TypeError('predicate must be a function');
    }
    
    const O = Object(this);
    const len = parseInt(O.length) || 0;
    
    for (let k = len - 1; k >= 0; k--) {
      if (k in O) {
        const kValue = O[k];
        if (predicate.call(thisArg, kValue, k, O)) {
          return k;
        }
      }
    }
    
    return -1;
  };
}
